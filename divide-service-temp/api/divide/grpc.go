package divide

import (
	dividepb "cnb.cool/cymirror/ces-services/divide-service/gen/proto/ces/divide"
	"cnb.cool/cymirror/ces-services/divide-service/internal/application/divide"
	"context"
	"google.golang.org/protobuf/types/known/emptypb"
)

var _ dividepb.DivideServiceServer = (*ServiceServer)(nil)

type ServiceServer struct {
	divideApplication *divide.Application
}

func NewServiceServer(divideApplication *divide.Application) *ServiceServer {
	return &ServiceServer{divideApplication: divideApplication}
}

func (s ServiceServer) DivideByOrderID(ctx context.Context, req *dividepb.DivideByOrderIDReq) (*emptypb.Empty, error) {
	return s.divideApplication.DivideByOrderID(ctx, req)
}
func (s ServiceServer) DivideMatchOrder(ctx context.Context, req *dividepb.DivideMatchOrderReq) (*emptypb.Empty, error) {
	return s.divideApplication.DivideMatchOrder(ctx, req)
}
