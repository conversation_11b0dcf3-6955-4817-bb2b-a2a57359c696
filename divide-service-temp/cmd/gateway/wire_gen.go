// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"cnb.cool/cymirror/ces-services/common/gateway"
	"cnb.cool/cymirror/ces-services/divide-service/cmd/gateway/internal/config"
	"context"
)

import (
	_ "cnb.cool/cymirror/ces-services/common/resolver"
)

// Injectors from wire.go:

func InitializeApplication(ctx context.Context, configPath2 string) (*Application, func()) {
	configConfig := config.MustLoad(configPath2)
	gatewayConfig := config.GetGatewayConfig(configConfig)
	gatewayGateway, cleanup := gateway.InitializeGateway(ctx, gatewayConfig)
	application := NewApplication(gatewayGateway)
	return application, func() {
		cleanup()
	}
}
