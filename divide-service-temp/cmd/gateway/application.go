package main

import (
	"cnb.cool/cymirror/ces-services/common/gateway"
	dividepb "cnb.cool/cymirror/ces-services/divide-service/gen/proto/ces/divide"
	"context"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"google.golang.org/grpc"
)

type Application struct {
	Gateway *gateway.Gateway
}

func NewApplication(
	gw *gateway.Gateway,
) *Application {
	_ = gw.RegisterHandler(func(gwmux *runtime.ServeMux, conn *grpc.ClientConn) error {
		var err error
		err = dividepb.RegisterDivideServiceHandler(context.Background(), gwmux, conn)
		if err != nil {
			return err
		}

		return err
	})

	return &Application{
		Gateway: gw,
	}
}

func (s *Application) Run(ctx context.Context) error {
	return s.Gateway.Run(ctx)
}
