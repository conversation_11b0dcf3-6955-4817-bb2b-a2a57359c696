// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"cnb.cool/cymirror/ces-services/common/client"
	"cnb.cool/cymirror/ces-services/common/server"
	divide2 "cnb.cool/cymirror/ces-services/divide-service/api/divide"
	"cnb.cool/cymirror/ces-services/divide-service/cmd/grpc/internal"
	client2 "cnb.cool/cymirror/ces-services/divide-service/cmd/grpc/internal/client"
	"cnb.cool/cymirror/ces-services/divide-service/cmd/grpc/internal/config"
	"cnb.cool/cymirror/ces-services/divide-service/internal/application/divide"
	"cnb.cool/cymirror/ces-services/divide-service/internal/infra/repository"
	"context"
)

// Injectors from wire.go:

func InitializeApplication(ctx context.Context, configPath2 string) (*Application, func()) {
	configConfig := config.MustLoad(configPath2)
	grpcServerConfig := config.GetGrpcServerConfig(configConfig)
	serverServer, cleanup := server.InitializeServer(ctx, grpcServerConfig)
	yeepayConfig := config.GetYeepayConfig(configConfig)
	yeepayClient := internal.NewYeepayClient(yeepayConfig)
	databaseConfig := config.GetDBConfig(configConfig)
	db := internal.NewDB(databaseConfig)
	walletRepository := repository.NewWalletRepository(db)
	orderRepository := repository.NewOrderRepository(db)
	clientConfig := client.NewConfigFromGRPCConfig(grpcServerConfig)
	nftServiceClient, cleanup2 := client2.NewNFTServiceClient(clientConfig)
	payRepository := repository.NewPayRepository(db)
	divideRepository := repository.NewDivideRepository(db)
	purchaseReqServiceClient, cleanup3 := client2.PurchaseReqServiceClient(clientConfig)
	payServiceClient, cleanup4 := client2.NewPayServiceClient(clientConfig)
	application := divide.NewApplication(yeepayClient, walletRepository, orderRepository, nftServiceClient, payRepository, divideRepository, purchaseReqServiceClient, payServiceClient)
	serviceServer := divide2.NewServiceServer(application)
	mainApplication := NewApplication(configConfig, serverServer, serviceServer)
	return mainApplication, func() {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}
}
