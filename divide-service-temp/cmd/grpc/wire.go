//go:build wireinject

package main

import (
	"context"

	"cnb.cool/cymirror/ces-services/common/server"
	"cnb.cool/cymirror/ces-services/divide-service/api"
	"cnb.cool/cymirror/ces-services/divide-service/cmd/grpc/internal"
	"cnb.cool/cymirror/ces-services/divide-service/cmd/grpc/internal/client"
	"cnb.cool/cymirror/ces-services/divide-service/cmd/grpc/internal/config"
	"cnb.cool/cymirror/ces-services/divide-service/internal/application"
	"cnb.cool/cymirror/ces-services/divide-service/internal/infra"

	"github.com/google/wire"
)

func InitializeApplication(ctx context.Context, configPath string) (*Application, func()) {
	panic(wire.Build(
		config.MustLoad,
		config.GetGrpcServerConfig,

		config.ConfigProviderSet,
		application.AppProviderSet,
		api.HandlerProviderSet,
		infra.InfraProviderSet,
		internal.InternalProviderSet,
		//domain.DomainProviderSet,
		client.ClientProviderSet,

		server.InitializeServer,

		NewApplication,
	))
}
