package internal

import (
	"cnb.cool/cymirror/ces-services/divide-service/cmd/grpc/internal/config"
	"cnb.cool/cymirror/ces-services/divide-service/internal/infra/yeepay"
	"github.com/yop-platform/yop-go-sdk/yop/client"
	"github.com/yop-platform/yop-go-sdk/yop/request"
)

func NewYeepayClient(cfg *config.YeepayConfig) *yeepay.Client {
	var priKey = &request.IsvPriKey{Value: cfg.PrivateKey, CertType: request.RSA2048}

	yeepayCfg := &yeepay.Config{
		AppKey:          cfg.App<PERSON>ey,
		MerchantNo:      cfg.MerchantNo,
		PayNotifyURL:    cfg.NotifyURL,
		PayUrlPrefix:    "https://cash.yeepay.com/cashier/std",
		CsNotifyURL:     cfg.CsURL,
		WalletNotifyURL: cfg.WalletURL,
		YeepayPublicKey: cfg.YeepayPublicKey,
	}

	return yeepay.NewClient(pri<PERSON><PERSON>, yeepayCfg, client.DefaultClient)
}
