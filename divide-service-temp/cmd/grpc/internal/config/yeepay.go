package config

// YeepayConfig 易宝支付
type YeepayConfig struct {
	AppKey          string `mapstructure:"app_key"`           // appKey
	MerchantNo      string `mapstructure:"merchant_no"`       // 商户号
	PrivateKey      string `mapstructure:"private_key"`       // 私钥
	YeepayPublicKey string `mapstructure:"yeepay_public_key"` // 易宝支付公钥
	NotifyURL       string `mapstructure:"notify_url"`        // 异步通知地址
	CsURL           string `mapstructure:"cs_url"`            // 清算回调地址
	WalletURL       string `mapstructure:"wallet_url"`        // 钱包回调地址
}

func GetYeepayConfig(cfg *Config) *YeepayConfig {
	if cfg == nil {
		panic("grpc server config is nil")
	}
	return &cfg.Yeepay
}
