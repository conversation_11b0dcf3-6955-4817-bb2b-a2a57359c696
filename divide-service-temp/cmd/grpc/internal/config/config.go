package config

import (
	"cnb.cool/cymirror/ces-services/common/db"
	"fmt"

	"cnb.cool/cymirror/ces-services/common/server/config"
	"github.com/spf13/viper"
)

type Config struct {
	config.GrpcServerConfig `mapstructure:",squash"`
	Database                db.DatabaseConfig `mapstructure:"database"`
	Redis                   db.RedisConfig    `mapstructure:"redis"`
	Ye<PERSON>ay                  <PERSON>ay<PERSON>onfig      `mapstructure:"yeepay"`
}

func MustLoad(path string) *Config {
	v := viper.New()
	v.SetConfigFile(path)

	if err := v.ReadInConfig(); err != nil {
		panic(fmt.Errorf("failed to read config file: %s", err))
	}

	var c Config
	if err := v.Unmarshal(&c); err != nil {
		panic(fmt.Errorf("failed to unmarshal config: %s", err))
	}

	// Validate environment type
	if err := config.ValidateEnv(c.Server.Env); err != nil {
		panic(err)
	}

	return &c
}

func GetGrpcServerConfig(cfg *Config) *config.GrpcServerConfig {
	if cfg == nil {
		panic("grpc server config is nil")
	}
	return &cfg.GrpcServerConfig
}

func GetDBConfig(cfg *Config) *db.DatabaseConfig {
	if cfg == nil {
		panic("database config is nil")
	}
	return &cfg.Database
}

func GetRedisConfig(cfg *Config) *db.RedisConfig {
	if cfg == nil {
		panic("redis config is nil")
	}
	return &cfg.Redis
}
