package client

import (
	kycpb "cnb.cool/cymirror/ces-services/account-service/gen/proto/ces/account/kyc"
	"cnb.cool/cymirror/ces-services/common/client"
	marketpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/market"
	nftpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/nft"
	projectpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/project"
	purchasereqpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/purchasereq"
	paypb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/pay"
	authpb "cnb.cool/cymirror/ces-services/user-service/gen/proto/ces/user/auth"
	profilepb "cnb.cool/cymirror/ces-services/user-service/gen/proto/ces/user/profile"
	whitelistpb "cnb.cool/cymirror/ces-services/whitelist-service/gen/proto/ces/whitelist/whitelist"
)

func NewProjectServiceClient(registryCfg *client.Config) (projectpb.ProjectServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "nft-service", projectpb.NewProjectServiceClient)
}

func NewPrimaryMarketServiceClient(registryCfg *client.Config) (marketpb.PrimaryMarketServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "nft-service", marketpb.NewPrimaryMarketServiceClient)
}

func NewSecondaryMarketServiceClient(registryCfg *client.Config) (marketpb.SecondaryMarketServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "nft-service", marketpb.NewSecondaryMarketServiceClient)
}

func NewKYCServiceClient(registryCfg *client.Config) (kycpb.KYCServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "account-service", kycpb.NewKYCServiceClient)
}

func NewNFTServiceClient(registryCfg *client.Config) (nftpb.NFTServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "nft-service", nftpb.NewNFTServiceClient)
}

func NewProfileServiceClient(registryCfg *client.Config) (profilepb.ProfileServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "user-service", profilepb.NewProfileServiceClient)
}

func PurchaseReqServiceClient(registryCfg *client.Config) (purchasereqpb.PurchaseReqServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "nft-service", purchasereqpb.NewPurchaseReqServiceClient)
}

func NewAuthServiceClient(registryCfg *client.Config) (authpb.AuthServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "user-service", authpb.NewAuthServiceClient)
}

func NewWhitelistServiceClient(registryCfg *client.Config) (whitelistpb.WhitelistServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "whitelist-service", whitelistpb.NewWhitelistServiceClient)
}

func NewPayServiceClient(registryCfg *client.Config) (paypb.PayServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "payment-service", paypb.NewPayServiceClient)
}
