package main

import (
	"cnb.cool/cymirror/ces-services/common/server"
	"cnb.cool/cymirror/ces-services/divide-service/cmd/grpc/internal/config"
	dividepb "cnb.cool/cymirror/ces-services/divide-service/gen/proto/ces/divide"

	"context"
	"google.golang.org/grpc"
)

type Application struct {
	Server *server.Server
}

func NewApplication(
	cfg *config.Config,
	s *server.Server,

	divideServer dividepb.DivideServiceServer,
) *Application {

	s.RegisterServer(func(s *grpc.Server) {
		dividepb.RegisterDivideServiceServer(s, divideServer)
	})

	return &Application{
		Server: s,
	}
}

func (s *Application) Run(ctx context.Context) error {
	return s.Server.Run(ctx)
}
