package divide

import (
	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/common/server"
	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/query"
	dividepb "cnb.cool/cymirror/ces-services/divide-service/gen/proto/ces/divide"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/divide"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/pay"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/wallet"
	"cnb.cool/cymirror/ces-services/divide-service/internal/infra/yeepay"

	"context"
	"strings"
	"time"

	nftpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/nft"
	purchasereqpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/purchasereq"
	paypb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/pay"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"
)

// OrderMatchData 定义订单匹配数据结构
type OrderMatchData struct {
	OrderID   string                 // 订单ID
	RequestNo string                 // 请求号
	UserID    string                 // 用户ID
	Amount    decimal.Decimal        // 金额
	ExtraData map[string]interface{} // 额外数据
}

type Application struct {
	yeepayClient      *yeepay.Client
	walletRepo        wallet.Repo
	orderRepo         order.Repo
	nftServiceClient  nftpb.NFTServiceClient
	payRepo           pay.Repo
	divideRepo        divide.Repo
	purchaseReqClient purchasereqpb.PurchaseReqServiceClient
	payClient         paypb.PayServiceClient
}

func NewApplication(yeepayClient *yeepay.Client, walletRepo wallet.Repo, orderRepo order.Repo, nftServiceClient nftpb.NFTServiceClient, payRepo pay.Repo, divideRepo divide.Repo, purchaseReqClient purchasereqpb.PurchaseReqServiceClient, payClient paypb.PayServiceClient) *Application {
	return &Application{yeepayClient: yeepayClient, walletRepo: walletRepo, orderRepo: orderRepo, nftServiceClient: nftServiceClient, payRepo: payRepo, divideRepo: divideRepo, purchaseReqClient: purchaseReqClient, payClient: payClient}
}
func (a *Application) DivideByOrderID(ctx context.Context, req *dividepb.DivideByOrderIDReq) (*emptypb.Empty, error) {
	zap.L().Info("开始分账", zap.Any("req", req))
	if req.Password != "fanyejixiang" {
		return nil, status.Error(codes.InvalidArgument, "password is invalid")
	}
	if req.Token != "fanyeniubi" {
		return nil, status.Error(codes.InvalidArgument, "token is invalid")
	}
	order, err := a.orderRepo.GetOrderByID(ctx, req.OrderID)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, status.Error(codes.NotFound, "order not found")
	}
	_, err = a.processMarketCsNotify(ctx, order)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (a *Application) DivideMatchOrder(ctx context.Context, req *dividepb.DivideMatchOrderReq) (*emptypb.Empty, error) {
	zap.L().Info("开始处理匹配订单分账", zap.Any("req", req))

	if req.Password != "fanyejixiang" {
		return nil, status.Error(codes.InvalidArgument, "password is invalid")
	}
	if req.Token != "fanyeniubi" {
		return nil, status.Error(codes.InvalidArgument, "token is invalid")
	}

	// 检查请求数据
	if req.Content == nil || len(req.Content) == 0 {
		return nil, status.Error(codes.InvalidArgument, "CSV数据为空")
	}

	// 将字节数据转换为字符串并按行分割
	csvString := string(req.Content)
	lines := strings.Split(csvString, "\n")

	if len(lines) < 2 { // 至少需要标题行和一行数据
		return nil, status.Error(codes.InvalidArgument, "CSV数据格式不正确")
	}

	// 处理标题行
	headers := strings.Split(lines[0], ",")
	headerMap := make(map[string]int)
	for i, header := range headers {
		headerMap[strings.TrimSpace(header)] = i
	}

	// 检查必要的列是否存在
	requiredColumns := []string{"order_id", "request_no", "user_id", "amount"}
	for _, col := range requiredColumns {
		if _, exists := headerMap[col]; !exists {
			return nil, status.Error(codes.InvalidArgument, "CSV缺少必要列: "+col)
		}
	}

	// 处理数据行并转换为结构体
	var orderMatchesData []*OrderMatchData
	for i := 1; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if line == "" {
			continue // 跳过空行
		}

		fields := strings.Split(line, ",")
		if len(fields) < len(requiredColumns) {
			zap.L().Warn("跳过数据不完整的行", zap.Int("行号", i+1), zap.String("行内容", line))
			continue
		}

		// 获取基本字段
		orderID := strings.TrimSpace(fields[headerMap["order_id"]])
		requestNo := strings.TrimSpace(fields[headerMap["request_no"]])
		userID := strings.TrimSpace(fields[headerMap["user_id"]])

		// 转换金额字段
		amountStr := strings.TrimSpace(fields[headerMap["amount"]])
		amount, err := decimal.NewFromString(amountStr)
		if err != nil {
			zap.L().Warn("金额转换失败", zap.String("金额", amountStr), zap.Int("行号", i+1), zap.Error(err))
			continue
		}

		// 创建订单匹配数据结构体
		orderMatch := &OrderMatchData{
			OrderID:   orderID,
			RequestNo: requestNo,
			UserID:    userID,
			Amount:    amount,
			ExtraData: make(map[string]interface{}),
		}

		// 处理可能存在的其他列
		for colName, index := range headerMap {
			// 跳过已经处理过的列
			if colName == "order_id" || colName == "request_no" || colName == "user_id" || colName == "amount" {
				continue
			}

			if index < len(fields) {
				fieldValue := strings.TrimSpace(fields[index])
				// 尝试将数值列转换为decimal类型
				if decimal, err := decimal.NewFromString(fieldValue); err == nil {
					orderMatch.ExtraData[colName] = decimal
				} else {
					orderMatch.ExtraData[colName] = fieldValue
				}
			}
		}

		orderMatchesData = append(orderMatchesData, orderMatch)
	}

	if len(orderMatchesData) == 0 {
		return nil, status.Error(codes.InvalidArgument, "没有有效的订单匹配数据")
	}

	zap.L().Info("已成功解析CSV数据为结构化数据", zap.Int("记录数", len(orderMatchesData)))

	// 循环处理每个订单数据
	for i, orderData := range orderMatchesData {
		// 构建分账详情
		details := []*paypb.DivideRPCReq_DivideDetail{
			{
				Amount:     orderData.Amount.StringFixed(2),
				UserID:     orderData.UserID,
				MemberType: paypb.DivideMemberType_SELLER_SIDE,
			},
		}

		// 构建分账请求
		divideReq := &paypb.DivideRPCReq{
			OrderID:         orderData.OrderID,
			DivideDetails:   details,
			DivideRequestNo: orderData.RequestNo,
		}

		// 调用分账RPC
		_, err := a.payClient.DivideRPC(ctx, divideReq)
		if err != nil {
			zap.L().Error("分账RPC调用失败",
				zap.String("订单ID", orderData.OrderID),
				zap.String("用户ID", orderData.UserID),
				zap.String("金额", orderData.Amount.String()),
				zap.Error(err))
			// 记录错误但继续处理下一条
			continue
		}

		zap.L().Info("订单分账成功",
			zap.String("订单ID", orderData.OrderID),
			zap.String("用户ID", orderData.UserID),
			zap.String("金额", orderData.Amount.String()),
			zap.Int("进度", i+1),
			zap.Int("总数", len(orderMatchesData)))
	}

	return &emptypb.Empty{}, nil
}

func (a *Application) processMarketCsNotify(ctx context.Context, o *order.Order) (*emptypb.Empty, error) {
	// 获取二级订单
	secondaryExtras, err := a.orderRepo.GetSecondaryExtraByOrderID(ctx, o.ID)
	if err != nil {
		return nil, err
	}
	var orderIDs []string
	for _, extra := range secondaryExtras {
		orderIDs = append(orderIDs, extra.ID)
	}
	// 剩余可分账金额
	unSplitAmount, err := decimal.NewFromString(o.Price().String())
	if err != nil || unSplitAmount.Exponent() < -2 {
		zap.L().Error("invalid unSplitAmount", zap.Error(err), zap.String("unSplitAmount", o.Price().String()))
		return nil, server.InternalStatus
	}
	// 订单金额
	orderAmount, err := decimal.NewFromString(o.Price().String())
	if err != nil || orderAmount.Exponent() < -2 {
		zap.L().Error("invalid orderAmount", zap.Error(err), zap.String("orderAmount", o.Price().String()))
		return nil, server.InternalStatus
	}

	// 获取订单
	yeepayOrder, err := a.payRepo.GetYeepayOrderByOrderID(ctx, o.ID)
	if yeepayOrder == nil {
		zap.L().Error("get yeepay order by order id failed", zap.String("order_id", o.ID))
		return nil, server.InternalStatus
	}
	if err != nil {
		return nil, err
	}

	requestNo := strings.ReplaceAll(uuid.New().String(), "-", "")                         // 分账请求号
	userSideDivideLogs := make([]*divide.YeepayDivide, 0, len(secondaryExtras))           // 用户分账记录
	allUserSideAmount := decimal.Zero                                                     // 所有属于用户的分账金额
	yeepayUserSideDivideInfos := make(map[string]*yeepay.Divide, len(userSideDivideLogs)) // 易宝申请分账信息 key: sellerID
	// 多个用户分账
	for _, secondaryExtra := range secondaryExtras {
		// 获取出售用户易宝钱包memberID
		wallet, err := a.walletRepo.GetYeePayWalletByUserID(ctx, secondaryExtra.SellerID())
		if err != nil {
			return nil, err
		}
		if !wallet.IsOpen() {
			zap.L().Error("wallet is not open", zap.String("seller_id", secondaryExtra.SellerID()))
			return nil, server.InternalStatus
		}
		// 用户分账金额 = 藏品金额 * 0.95
		userSideAmount := secondaryExtra.Price().Mul(decimal.NewFromInt(95)).Div(decimal.NewFromInt(100))
		userSideAmount = userSideAmount.RoundDown(2).Round(2) // 向下取整到两位小数

		// 针对金刚经项目修改分账比例，百分之94
		if secondaryExtra.ProjectID() == "2f07d61d-b758-4a29-8418-1a1b06f22bf8" {
			userSideAmount = secondaryExtra.Price().Mul(decimal.NewFromInt(94)).Div(decimal.NewFromInt(100))
			userSideAmount = userSideAmount.RoundDown(2).Round(2) // 向下取整到两位小数
		}

		// 用户侧分账
		userSideDivide, err := divide.NewMemberSideYeepayDivide(wallet.UserID(), requestNo, o.ID, userSideAmount, secondaryExtra.ID)
		if err != nil {
			return nil, err
		}
		allUserSideAmount = allUserSideAmount.Add(userSideAmount) // 项目侧分账金额 = 可分账金额 - 所有用户分账金额
		userSideDivideLogs = append(userSideDivideLogs, userSideDivide)

		// 易宝申请分账信息
		if yeepayUserSideDivideInfo, ok := yeepayUserSideDivideInfos[secondaryExtra.SellerID()]; ok {
			yeepayUserSideDivideInfo.Amount = yeepayUserSideDivideInfo.Amount.Add(userSideAmount)
		} else {
			yeepayUserSideDivideInfo := &yeepay.Divide{
				Amount:     userSideAmount,
				LedgerNo:   *wallet.MemberID(),
				LedgerType: yeepay.DivideTypeMerchant2Member,
			}
			yeepayUserSideDivideInfos[secondaryExtra.SellerID()] = yeepayUserSideDivideInfo
		}
	}

	// 项目侧分账
	// 项目方分账金额 = 剩余可分账金额 - 用户分账金额
	projectSideAmount := unSplitAmount.Sub(allUserSideAmount)
	projectSideDivide, err := divide.NewProjectSideYeepayDivide(requestNo, o.ID, projectSideAmount)
	if err != nil {
		return nil, err
	}
	yeepayProjectSideDivideInfo := &yeepay.Divide{
		Amount:     projectSideDivide.Amount(),
		LedgerNo:   a.yeepayClient.MerchantNo(),
		LedgerType: yeepay.DivideTypeMerchant2Merchant,
	}

	err = db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
		o, err := a.orderRepo.GetOrderByID(ctx, o.ID)
		if err != nil {
			return err
		}
		if !o.Status().Equal(order.StatusSuccess) {
			zap.L().Error("order status not success", zap.String("order_id", o.ID))
			return server.InternalStatus
		}

		// 易宝申请分账请求参数
		yeepayDivides := make([]*yeepay.Divide, 0, len(yeepayUserSideDivideInfos)+1)
		yeepayDivides = append(yeepayDivides, yeepayProjectSideDivideInfo)
		for _, d := range yeepayUserSideDivideInfos {
			yeepayDivides = append(yeepayDivides, d)
		}

		_, err = a.payClient.DivideRPC(ctx, buildDivideRPCReq(o.ID, projectSideAmount, yeepayUserSideDivideInfos, requestNo))
		if err != nil {
			zap.L().Error("apply divide rpc failed", zap.Error(err))
			return err
		}

		//// 根据不同分账返回状态操作
		//// TODO: 优化不为const string
		// switch divideResult.Status {
		//case "SUCCESS":
		//	yeepayOrder.SetDivideStatus(pay.YeepayDivideStatusSuccess)
		//case "FAIL":
		//	yeepayOrder.SetDivideStatus(pay.YeepayDivideStatusFailed)
		//case "PROCESSING":
		//	yeepayOrder.SetDivideStatus(pay.YeepayDivideStatusDividing)
		//}
		csAt, err := time.ParseInLocation(time.DateTime, time.Now().Format(time.DateTime), time.Local)
		if err != nil {
			zap.L().Error("parse cs success date failed", zap.Error(err))
			return err
		}
		yeepayOrder.SetCsAt(csAt)
		yeepayOrder.SetUnSplitAmount(unSplitAmount)
		// 商户手续费，解析为空默认0
		merchantFee, _ := decimal.NewFromString(projectSideAmount.String())
		yeepayOrder.SetMerchantFee(merchantFee)

		// 插入分账记录
		err = a.divideRepo.CreateDivideRecord(ctx, append([]*divide.YeepayDivide{projectSideDivide}, userSideDivideLogs...))
		if err != nil {
			return err
		}

		// 更新状态
		err = a.payRepo.UpdateYeepayOrder(ctx, yeepayOrder)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	// 修改状态
	return &emptypb.Empty{}, nil
}

func buildDivideRPCReq(
	orderID string,
	projectSideAmount decimal.Decimal,
	yeepayUserSideDivideInfos map[string]*yeepay.Divide,
	requestNo string,
) *paypb.DivideRPCReq {
	details := []*paypb.DivideRPCReq_DivideDetail{
		{
			Amount:     projectSideAmount.StringFixed(2),
			UserID:     "",
			MemberType: paypb.DivideMemberType_PROJECT_SIDE,
		},
	}

	for userID, divideInfo := range yeepayUserSideDivideInfos {
		details = append(details, &paypb.DivideRPCReq_DivideDetail{
			Amount:     divideInfo.Amount.StringFixed(2),
			UserID:     userID,
			MemberType: paypb.DivideMemberType_SELLER_SIDE,
		})
	}
	return &paypb.DivideRPCReq{
		OrderID:         orderID,
		DivideDetails:   details,
		DivideRequestNo: requestNo,
	}
}
