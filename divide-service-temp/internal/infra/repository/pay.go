package repository

import (
	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/common/server"
	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/query"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/pay"
	"context"
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var _ pay.Repo = (*PayRepository)(nil)

type PayRepository struct {
	db *db.DB[*query.Query]
}

func NewPayRepository(db *db.DB[*query.Query]) *PayRepository {
	return &PayRepository{db: db}
}

func (p *PayRepository) ApplyYeepayOrderPaySuccess(ctx context.Context, yeepayOrder *pay.Yeepay) error {
	db := p.db.Get(ctx)
	_, err := db.PaymentPayYeepay.WithContext(ctx).
		Select(db.PaymentPayYeepay.PayAt, db.PaymentPayYeepay.YeepayOrderNo).
		Where(db.PaymentPayYeepay.OrderID.Eq(yeepayOrder.OrderID())).
		Updates(yeepayOrder.ToModel())
	if err != nil {
		zap.L().Error("apply pay yeepay order pay success failed", zap.Error(err), zap.Any("order", yeepayOrder))
		return server.InternalStatus
	}
	return nil
}

func (p *PayRepository) CreateYeepayPaymentInfo(ctx context.Context, payOrder *pay.Yeepay) error {
	db := p.db.Get(ctx)
	err := db.PaymentPayYeepay.WithContext(ctx).Create(payOrder.ToModel())
	if err != nil {
		zap.L().Error("create pay yeepay failed", zap.Error(err), zap.Any("pay_order", payOrder))
		return server.InternalStatus
	}
	return nil
}

func (p *PayRepository) GetYeepayOrderByOrderID(ctx context.Context, orderId string) (*pay.Yeepay, error) {
	db := p.db.Get(ctx)
	first, err := db.PaymentPayYeepay.WithContext(ctx).Where(db.PaymentPayYeepay.OrderID.Eq(orderId)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		zap.L().Error("get yeepay order by order id failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	if first == nil {
		return nil, nil
	}
	return pay.NewYeepayFromModel(first), nil
}

func (p *PayRepository) UpdateYeepayOrder(ctx context.Context, o *pay.Yeepay) error {
	db := p.db.Get(ctx)
	_, err := db.PaymentPayYeepay.WithContext(ctx).
		Select(db.PaymentPayYeepay.UserID,
			db.PaymentPayYeepay.OrderID,
			db.PaymentPayYeepay.YeepayOrderNo,
			db.PaymentPayYeepay.PayAmount,
			db.PaymentPayYeepay.MerchantFee,
			db.PaymentPayYeepay.UnSplitAmount,
			db.PaymentPayYeepay.CsAt,
			db.PaymentPayYeepay.PayAt,
			db.PaymentPayYeepay.PayURL,
			db.PaymentPayYeepay.PayWay,
			db.PaymentPayYeepay.DivideType,
			db.PaymentPayYeepay.DivideStatus).
		Where(db.PaymentPayYeepay.ID.Eq(o.ID)).
		Updates(o.ToModel())
	if err != nil {
		zap.L().Error("update yeepay order failed", zap.Error(err))
		return server.InternalStatus
	}
	return nil
}
