package repository

import (
	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/common/server"
	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/query"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/project"
	"context"
	"errors"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	"sort"
	"time"
)

var _ order.Repo = (*OrderRepository)(nil)

type OrderRepository struct {
	db *db.DB[*query.Query]
}

func NewOrderRepository(db *db.DB[*query.Query]) *OrderRepository {
	return &OrderRepository{db: db}
}

func (s *OrderRepository) CreatePrimaryMarketOrderExtra(ctx context.Context, po *order.PrimaryMarket) error {
	orderExtras := make([]*model.PaymentOrderPrimary, 0, len(po.Extra()))
	for _, extra := range po.Extra() {
		orderExtras = append(orderExtras, extra.ToModel())
	}
	err := s.db.Get(ctx).PaymentOrderPrimary.WithContext(ctx).
		Create(orderExtras...)
	if err != nil {
		zap.L().Error("create order extra failed", zap.Error(err))
		return server.InternalStatus
	}
	return nil
}

func (s *OrderRepository) UpdateOrderStatus(ctx context.Context, order *order.Order) error {
	db := s.db.Get(ctx)
	info, err := db.PaymentOrder.WithContext(ctx).
		Select(db.PaymentOrder.Status).
		Where(db.PaymentOrder.ID.Eq(order.ID)).
		Updates(order.ToModel())
	if err != nil {
		zap.L().Error("update order status failed", zap.Error(err), zap.String("order_id", order.ID))
		return server.InternalStatus
	}
	if info.RowsAffected == 0 {
		zap.L().Error("update order status failed", zap.String("order_id", order.ID), zap.Any("order", order))
		return server.InternalStatus
	}
	return nil
}

func (s *OrderRepository) CreateOrder(ctx context.Context, orderDomain *order.Order) (*order.Order, error) {
	orderModel := orderDomain.ToModel()
	err := s.db.Get(ctx).PaymentOrder.WithContext(ctx).Create(orderModel)
	if err != nil {
		zap.L().Error("create order failed", zap.Error(err))
		return nil, server.InternalStatus
	}

	return order.NewFromModel(orderModel), nil
}

func (s *OrderRepository) CreateSecondaryMarketOrderExtra(ctx context.Context, so *order.SecondaryMarket) error {
	orderExtras := make([]*model.PaymentOrderSecondary, 0, len(so.Extra()))
	for _, extra := range so.Extra() {
		orderExtras = append(orderExtras, extra.ToModel())
	}
	err := s.db.Get(ctx).PaymentOrderSecondary.WithContext(ctx).
		Create(orderExtras...)
	if err != nil {
		zap.L().Error("create order extra failed", zap.Error(err))
		return server.InternalStatus
	}
	return nil
}

func (s *OrderRepository) GetOrderByIDWithUserID(ctx context.Context, id string, userID string) (*order.Order, error) {
	db := s.db.Get(ctx)
	first, err := db.PaymentOrder.WithContext(ctx).
		Where(db.PaymentOrder.ID.Eq(id)).
		Where(db.PaymentOrder.UserID.Eq(userID)).
		First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		zap.L().Error("get order by id failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	return order.NewFromModel(first), nil
}

func (s *OrderRepository) GetOrderByID(ctx context.Context, id string) (*order.Order, error) {
	db := s.db.Get(ctx)
	first, err := db.PaymentOrder.WithContext(ctx).
		Where(db.PaymentOrder.ID.Eq(id)).
		First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		zap.L().Error("get order by id failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	return order.NewFromModel(first), nil
}

func (s *OrderRepository) GetOrderRank(ctx context.Context, startTime, endTime int64, limit int) (map[string]int64, error) {
	start := time.Unix(startTime, 0)
	end := time.Unix(endTime, 0)

	db := s.db.Get(ctx)

	type ProjectCount struct {
		ProjectID string `gorm:"column:project_id"`
		Count     int64  `gorm:"column:count"`
	}

	projCountSlice := []ProjectCount{}

	countCol := db.PaymentOrderSecondary.ProjectID.Count()

	err := db.PaymentOrderSecondary.WithContext(ctx).
		Select(db.PaymentOrderSecondary.ProjectID, countCol.As("count")).
		Where(db.PaymentOrderSecondary.CreatedAt.Gte(start), db.PaymentOrderSecondary.CreatedAt.Lte(end)).
		Group(db.PaymentOrderSecondary.ProjectID).
		Order(countCol.Desc()).
		Limit(limit).
		Scan(&projCountSlice)

	if err != nil {
		zap.L().Error("get order rank failed", zap.Error(err))
		return nil, server.InternalStatus
	}

	rankMap := make(map[string]int64, len(projCountSlice))
	for _, r := range projCountSlice {
		rankMap[r.ProjectID] = r.Count
	}

	return rankMap, nil
}

func (s *OrderRepository) UpdateOrder(ctx context.Context, order *order.Order) error {
	db := s.db.Get(ctx)
	_, err := db.PaymentOrder.WithContext(ctx).
		Where(db.PaymentOrder.ID.Eq(order.ID)).
		Updates(order.ToModel())
	if err != nil {
		zap.L().Error("update order failed", zap.Error(err))
		return server.InternalStatus
	}
	return nil
}

func (s *OrderRepository) GetOrderNFTIDs(ctx context.Context, domainOrder *order.Order) ([]string, error) {
	switch domainOrder.OrderType() {
	case order.TypePrimaryMarket:
		return s.getPrimaryMarketOrderNFTIDs(ctx, domainOrder)
	case order.TypeSecondaryMarket:
		return s.getSecondaryMarketOrderNFTIDs(ctx, domainOrder)
	}
	zap.L().Error("GetOrderNFTIDs unsupported order type", zap.Int16("order_type", domainOrder.OrderType().Int16()))
	return nil, server.InternalStatus
}

func (s *OrderRepository) getPrimaryMarketOrderNFTIDs(ctx context.Context, domainOrder *order.Order) ([]string, error) {
	var nftIDs []string
	db := s.db.Get(ctx)
	primaryOrders, err := db.PaymentOrderPrimary.WithContext(ctx).
		Select(db.PaymentOrderPrimary.NftID).
		Where(db.PaymentOrderPrimary.OrderID.Eq(domainOrder.ID)).
		Find()
	if err != nil {
		zap.L().Error("get primary market order nft ids failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	for _, primaryOrder := range primaryOrders {
		nftIDs = append(nftIDs, primaryOrder.NftID)
	}

	return nftIDs, nil
}

func (s *OrderRepository) getSecondaryMarketOrderNFTIDs(ctx context.Context, domainOrder *order.Order) ([]string, error) {
	var nftIDs []string
	db := s.db.Get(ctx)
	secondaryOrders, err := db.PaymentOrderSecondary.WithContext(ctx).
		Select(db.PaymentOrderSecondary.NftID).
		Where(db.PaymentOrderSecondary.OrderID.Eq(domainOrder.ID)).
		Find()
	if err != nil {
		zap.L().Error("get secondary market order nft ids failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	for _, secondaryOrder := range secondaryOrders {
		nftIDs = append(nftIDs, secondaryOrder.NftID)
	}

	return nftIDs, nil
}

func (s *OrderRepository) GetPrimaryOrdersByOrderIDs(ctx context.Context, ids []string) ([]*model.PaymentOrderPrimary, error) {
	db := s.db.Get(ctx)
	orders, err := db.PaymentOrderPrimary.WithContext(ctx).
		Where(db.PaymentOrderPrimary.OrderID.In(ids...)).
		Find()
	if err != nil {
		zap.L().Error("GetPrimaryOrdersByIDs failed", zap.Error(err))
		return nil, err
	}

	return orders, nil
}

func (s *OrderRepository) GetSecondaryOrdersByOrderIDs(ctx context.Context, ids []string) ([]*model.PaymentOrderSecondary, error) {
	db := s.db.Get(ctx)
	orders, err := db.PaymentOrderSecondary.WithContext(ctx).
		Where(db.PaymentOrderSecondary.OrderID.In(ids...)).
		Find()
	if err != nil {
		zap.L().Error("GetSecondaryOrdersByIDs failed", zap.Error(err))
		return nil, err
	}

	return orders, nil
}

func (s *OrderRepository) GetOrderProjectID(ctx context.Context, order *order.Order) (string, error) {
	db := s.db.Get(ctx)
	first, err := db.PaymentOrderPrimary.WithContext(ctx).
		Select(db.PaymentOrderPrimary.ProjectID).
		Where(db.PaymentOrderPrimary.OrderID.Eq(order.ID)).
		First()
	if err != nil {
		zap.L().Error("get order project id failed", zap.Error(err))
		return "", server.InternalStatus
	}
	return first.ProjectID, nil
}

func (s *OrderRepository) GetOrderListingIDs(ctx context.Context, order *order.Order) ([]string, error) {
	db := s.db.Get(ctx)
	listings, err := db.PaymentOrderSecondary.WithContext(ctx).
		Select(db.PaymentOrderSecondary.ListingID).
		Where(db.PaymentOrderSecondary.OrderID.Eq(order.ID)).
		Find()
	if err != nil {
		zap.L().Error("get order listing id failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	ids := make([]string, 0, len(listings))
	for _, l := range listings {
		ids = append(ids, l.ListingID)
	}
	return ids, nil
}

func (s *OrderRepository) GetYeepayPayURL(ctx context.Context, orderID string, userID string) (string, error) {
	db := s.db.Get(ctx)
	first, err := db.PaymentPayYeepay.WithContext(ctx).
		Select(db.PaymentPayYeepay.PayURL).
		Where(db.PaymentPayYeepay.OrderID.Eq(orderID), db.PaymentPayYeepay.UserID.Eq(userID), db.PaymentPayYeepay.PayAt.IsNull()).
		First()
	if err != nil {
		zap.L().Error("GetYeepayPayURL failed", zap.Error(err))
		return "", server.InternalStatus
	}
	return first.PayURL, nil
}

func (s *OrderRepository) GetPrimaryOrderListByUserID(ctx context.Context, userID string, page int64, pageSize int64) ([]*model.PaymentOrderPrimary, int64, error) {
	db := s.db.Get(ctx)
	orders, total, err := db.PaymentOrderPrimary.WithContext(ctx).
		Where(db.PaymentOrderPrimary.UserID.Eq(userID)).
		Order(db.PaymentOrderPrimary.CreatedAt.Desc()).
		FindByPage(int((page-1)*pageSize), int(pageSize))
	if err != nil {
		zap.L().Error("GetPrimaryOrderListByUserID failed", zap.Error(err))
		return nil, 0, err
	}
	return orders, total, nil
}

func (s *OrderRepository) GetSecondaryOrderListByUserID(ctx context.Context, userID string, page int64, pageSize int64) ([]*model.PaymentOrderSecondary, int64, error) {
	db := s.db.Get(ctx)
	orders, total, err := db.PaymentOrderSecondary.WithContext(ctx).
		Where(db.PaymentOrderSecondary.UserID.Eq(userID)).
		Order(db.PaymentOrderSecondary.CreatedAt.Desc()).
		FindByPage(int((page-1)*pageSize), int(pageSize))
	if err != nil {
		zap.L().Error("GetSecondaryOrderListByUserID failed", zap.Error(err))
		return nil, 0, err
	}
	return orders, total, nil
}

func (s *OrderRepository) GetSecondaryOrderListAsSellerWithStatus(ctx context.Context, page, pageSize int32, userID string, status order.Status) ([]*order.SecondaryExtra, int64, error) {
	db := s.db.Get(ctx)

	query := db.PaymentOrderSecondary.WithContext(ctx).
		Select(db.PaymentOrderSecondary.ALL).
		Where(db.PaymentOrderSecondary.SellerID.Eq(userID)).
		Join(db.PaymentOrder, db.PaymentOrder.ID.EqCol(db.PaymentOrderSecondary.OrderID)).
		Where(db.PaymentOrder.Status.Eq(status.Int16())).
		Order(db.PaymentOrderSecondary.CreatedAt.Desc())

	total, err := query.Count()
	if err != nil {
		zap.L().Error("GetSecondaryOrderListAsSellerWithStatus failed", zap.Error(err))
		return nil, 0, err
	}
	query = query.Offset(int((page - 1) * pageSize)).Limit(int(pageSize))
	secondaryOrders, err := query.Find()
	if err != nil {
		zap.L().Error("GetSecondaryOrderListAsSellerWithStatus failed", zap.Error(err))
		return nil, 0, err
	}

	extras := make([]*order.SecondaryExtra, 0, len(secondaryOrders))
	for _, so := range secondaryOrders {
		extras = append(extras, order.NewSecondaryExtraFromModel(so))
	}

	return extras, total, nil
}

func (s *OrderRepository) GetOrdersByUserID(ctx context.Context, userID string, page int64, pageSize int64) ([]*order.Order, int64, error) {
	db := s.db.Get(ctx)

	orders, total, err := db.PaymentOrder.WithContext(ctx).
		Where(db.PaymentOrder.UserID.Eq(userID)).
		Order(db.PaymentOrder.CreatedAt.Desc()).
		FindByPage(int((page-1)*pageSize), int(pageSize))
	if err != nil {
		zap.L().Error("GetOrdersByUserID failed", zap.Error(err))
		return nil, 0, err
	}
	orderList := make([]*order.Order, 0, len(orders))
	for _, od := range orders {
		orderList = append(orderList, order.NewFromModel(od))
	}
	return orderList, total, nil
}

func (s *OrderRepository) GetOrdersByIDs(ctx context.Context, ids []string) ([]*order.Order, error) {
	db := s.db.Get(ctx)
	orders, err := db.PaymentOrder.WithContext(ctx).
		Where(db.PaymentOrder.ID.In(ids...)).
		Find()
	if err != nil {
		zap.L().Error("GetOrdersByIDs failed", zap.Error(err))
		return nil, err
	}

	orderList := make([]*order.Order, 0, len(orders))
	for _, od := range orders {
		orderList = append(orderList, order.NewFromModel(od))
	}
	return orderList, nil
}

func (s *OrderRepository) GetOrdersByIDsWithSort(ctx context.Context, ids []string) ([]*order.Order, error) {
	db := s.db.Get(ctx)
	orders, err := db.PaymentOrder.WithContext(ctx).
		Where(db.PaymentOrder.ID.In(ids...)).
		Find()
	if err != nil {
		zap.L().Error("GetOrdersByIDs failed", zap.Error(err))
		return nil, err
	}

	idxMap := make(map[string]int, len(ids))
	for i, id := range ids {
		// 构建一个映射表，用于记录每个 id 在传入的 ids 列表中的索引
		idxMap[id] = i
	}
	sort.Slice(orders, func(i, j int) bool {
		return idxMap[orders[i].ID] < idxMap[orders[j].ID]
	})

	res := make([]*order.Order, 0, len(orders))
	for _, od := range orders {
		res = append(res, order.NewFromModel(od))
	}
	return res, nil
}

func (s *OrderRepository) GetOrdersByIDsAndStatus(ctx context.Context, ids []string, status order.Status, page int64, pageSize int64) ([]*order.Order, int64, error) {
	db := s.db.Get(ctx)
	orders, total, err := db.PaymentOrder.WithContext(ctx).
		Where(
			field.And(
				db.PaymentOrder.ID.In(ids...),
				db.PaymentOrder.Status.Eq(status.Int16()),
			),
		).
		FindByPage(int((page-1)*pageSize), int(pageSize))
	if err != nil {
		zap.L().Error("GetOrdersByIDs failed", zap.Error(err))
		return nil, 0, err
	}

	orderList := make([]*order.Order, 0, len(orders))
	for _, od := range orders {
		orderList = append(orderList, order.NewFromModel(od))
	}
	return orderList, total, nil
}

func (s *OrderRepository) GetOrdersByIDsAndStatusBySort(ctx context.Context, ids []string, status order.Status, page int64, pageSize int64, sortF string, isAsc bool) ([]*order.Order, int64, error) {
	db := s.db.Get(ctx)
	sortField := field.NewField(db.PaymentOrder.TableName(), sortF).Desc()
	if isAsc {
		sortField = field.NewField(db.PaymentOrder.TableName(), sortF).Asc()
	}
	orders, total, err := db.PaymentOrder.WithContext(ctx).
		Where(
			field.And(
				db.PaymentOrder.ID.In(ids...),
				db.PaymentOrder.Status.Eq(status.Int16()),
			),
		).
		Order(sortField).
		FindByPage(int((page-1)*pageSize), int(pageSize))
	if err != nil {
		zap.L().Error("GetOrdersByIDs failed", zap.Error(err))
		return nil, 0, err
	}

	orderList := make([]*order.Order, 0, len(orders))
	for _, od := range orders {
		orderList = append(orderList, order.NewFromModel(od))
	}
	return orderList, total, nil
}

func (s *OrderRepository) GetSecondaryExtraByOrderID(ctx context.Context, orderID string) ([]*order.SecondaryExtra, error) {
	db := s.db.Get(ctx)
	secondaryExtras, err := db.PaymentOrderSecondary.WithContext(ctx).
		Where(db.PaymentOrderSecondary.OrderID.Eq(orderID)).
		Find()
	if err != nil {
		zap.L().Error("GetSecondaryExtraByOrderID failed", zap.Error(err))
		return nil, server.InternalStatus
	}

	result := make([]*order.SecondaryExtra, 0, len(secondaryExtras))
	for _, secondaryExtra := range secondaryExtras {
		result = append(result, order.NewSecondaryExtraFromModel(secondaryExtra))
	}
	return result, nil

}

func (s *OrderRepository) GetSecondaryExtraByOrderIDs(ctx context.Context, orderIDs []string) ([]*order.SecondaryExtra, error) {
	db := s.db.Get(ctx)
	secondaryExtras, err := db.PaymentOrderSecondary.WithContext(ctx).
		Where(db.PaymentOrderSecondary.OrderID.In(orderIDs...)).
		Find()
	if err != nil {
		zap.L().Error("GetSecondaryExtraByOrderIDs failed", zap.Error(err))
		return nil, server.InternalStatus
	}

	result := make([]*order.SecondaryExtra, 0, len(secondaryExtras))
	for _, secondaryExtra := range secondaryExtras {
		result = append(result, order.NewSecondaryExtraFromModel(secondaryExtra))
	}
	return result, nil
}

func (s *OrderRepository) GetPrimaryOrderByID(ctx context.Context, id string) (*order.PrimaryExtra, error) {
	db := s.db.Get(ctx)
	primaryOrder, err := db.PaymentOrderPrimary.WithContext(ctx).
		Where(db.PaymentOrderPrimary.ID.Eq(id)).
		First()
	if err != nil {
		zap.L().Error("GetPrimaryOrderByID failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	return order.NewPrimaryExtraFromModel(primaryOrder), nil
}

func (s *OrderRepository) GetSecondaryOrderByID(ctx context.Context, id string) (*order.SecondaryExtra, error) {
	db := s.db.Get(ctx)
	secondaryOrder, err := db.PaymentOrderSecondary.WithContext(ctx).
		Where(db.PaymentOrderSecondary.ID.Eq(id)).
		First()
	if err != nil {
		zap.L().Error("GetSecondaryOrderByID failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	return order.NewSecondaryExtraFromModel(secondaryOrder), nil
}

func (s *OrderRepository) GetSecondaryOrderByNFTIDWithStatus(ctx context.Context, page, pageSize int32, sortF string, isAsc bool, nftID string, status order.Status) ([]*order.SecondaryExtra, int64, error) {
	db := s.db.Get(ctx)

	// 构建基础查询
	query := db.PaymentOrderSecondary.WithContext(ctx).
		Select(db.PaymentOrderSecondary.ALL).
		Where(db.PaymentOrderSecondary.NftID.Eq(nftID)).
		Join(db.PaymentOrder, db.PaymentOrder.ID.EqCol(db.PaymentOrderSecondary.OrderID)).
		Where(db.PaymentOrder.Status.Eq(status.Int16())).
		Where(db.PaymentOrder.Type.Eq(order.TypeSecondaryMarket.Int16()))

	// 获取总数
	total, err := query.Count()
	if err != nil {
		zap.L().Error("get order count failed", zap.Error(err))
		return nil, 0, server.InternalStatus
	}

	// 添加排序
	switch sortF {
	case "created_at":
		if isAsc {
			query = query.Order(db.PaymentOrderSecondary.CreatedAt.Asc())
		} else {
			query = query.Order(db.PaymentOrderSecondary.CreatedAt.Desc())
		}
	case "price":
		if isAsc {
			query = query.Order(db.PaymentOrderSecondary.Price.Asc())
		} else {
			query = query.Order(db.PaymentOrderSecondary.Price.Desc())
		}
	default:
		zap.L().Error("invalid sort field", zap.String("sort", sortF))
		return nil, 0, server.InternalStatus
	}

	// 添加分页
	query = query.Offset(int((page - 1) * pageSize)).Limit(int(pageSize))

	// 执行查询
	secondaryOrders, err := query.Find()
	if err != nil {
		zap.L().Error("get secondary orders failed", zap.Error(err))
		return nil, 0, server.InternalStatus
	}

	// 转换为领域对象
	extras := make([]*order.SecondaryExtra, 0, len(secondaryOrders))
	for _, so := range secondaryOrders {
		extras = append(extras, order.NewSecondaryExtraFromModel(so))
	}

	return extras, total, nil
}

func (s *OrderRepository) GetSecondaryOrderByProjectIDWithStatus(ctx context.Context, page, pageSize int32, sortF string, isAsc bool, projectID string, status order.Status) ([]*order.SecondaryExtra, int64, error) {
	db := s.db.Get(ctx)

	query := db.PaymentOrderSecondary.WithContext(ctx).
		Select(db.PaymentOrderSecondary.ALL).
		Where(db.PaymentOrderSecondary.ProjectID.Eq(projectID)).
		Join(db.PaymentOrder, db.PaymentOrder.ID.EqCol(db.PaymentOrderSecondary.OrderID)).
		Where(db.PaymentOrder.Status.Eq(status.Int16())).
		Where(db.PaymentOrder.Type.Eq(order.TypeSecondaryMarket.Int16()))

	total, err := query.Count()
	if err != nil {
		zap.L().Error("get order count failed", zap.Error(err))
		return nil, 0, server.InternalStatus
	}

	switch sortF {
	case "created_at":
		if isAsc {
			query = query.Order(db.PaymentOrderSecondary.CreatedAt.Asc())
		} else {
			query = query.Order(db.PaymentOrderSecondary.CreatedAt.Desc())
		}
	case "price":
		if isAsc {
			query = query.Order(db.PaymentOrderSecondary.Price.Asc())
		} else {
			query = query.Order(db.PaymentOrderSecondary.Price.Desc())
		}
	}

	query = query.Offset(int((page - 1) * pageSize)).Limit(int(pageSize))

	secondaryOrders, err := query.Find()
	if err != nil {
		zap.L().Error("get secondary orders failed", zap.Error(err))
		return nil, 0, server.InternalStatus
	}

	extras := make([]*order.SecondaryExtra, 0, len(secondaryOrders))
	for _, so := range secondaryOrders {
		extras = append(extras, order.NewSecondaryExtraFromModel(so))
	}

	return extras, total, nil
}

func (s *OrderRepository) ApplyPayNotify(ctx context.Context, orderDomain *order.Order) error {
	db := s.db.Get(ctx)
	info, err := db.PaymentOrder.WithContext(ctx).
		Select(db.PaymentOrder.Status, db.PaymentOrder.PayTime).
		Where(db.PaymentOrder.ID.Eq(orderDomain.ID)).
		Where(db.PaymentOrder.Status.Eq(order.StatusPaying.Int16())).
		Updates(orderDomain.ToModel())
	if err != nil {
		zap.L().Error("apply pay notify failed", zap.Error(err), zap.String("order_id", orderDomain.ID))
		return server.InternalStatus
	}
	if info.RowsAffected == 0 {
		zap.L().Error("apply pay notify failed", zap.String("order_id", orderDomain.ID), zap.Any("order", orderDomain))
		return server.InternalStatus
	}
	return nil
}

func (s *OrderRepository) CancelOrder(ctx context.Context, orderDomain *order.Order) error {
	db := s.db.Get(ctx)
	_, err := db.PaymentOrder.WithContext(ctx).
		Where(db.PaymentOrder.ID.Eq(orderDomain.ID)).
		Where(db.PaymentOrder.UserID.Eq(orderDomain.UserID())).
		Where(db.PaymentOrder.Status.In(order.StatusWaitPay.Int16(), order.StatusPaying.Int16())).
		Update(db.PaymentOrder.Status, order.StatusCancel.Int16())
	if err != nil {
		zap.L().Error("CancelOrder failed", zap.Error(err))
		return server.InternalStatus
	}
	return nil
}

func (s *OrderRepository) GetUnsuccessfulOrdersByUserIDAndTime(ctx context.Context, userID string, startTime, endTime time.Time) ([]*order.Order, error) {
	db := s.db.Get(ctx)
	orders, err := db.PaymentOrder.WithContext(ctx).
		Where(db.PaymentOrder.UserID.Eq(userID)).
		Where(db.PaymentOrder.Status.NotIn(order.StatusSuccess.Int16(), order.StatusReduceStock.Int16(), order.StatusCancel.Int16())).
		Where(db.PaymentOrder.CreatedAt.Gte(startTime), db.PaymentOrder.CreatedAt.Lte(endTime)).
		Find()
	if err != nil {
		zap.L().Error("GetUnsuccessfulOrdersByUserIDAndTime failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	orderList := make([]*order.Order, 0, len(orders))
	for _, od := range orders {
		orderList = append(orderList, order.NewFromModel(od))
	}
	return orderList, nil
}

func (s *OrderRepository) GetAllTradeAmounts(ctx context.Context) (decimal.Decimal, error) {
	db := s.db.Get(ctx)
	var total decimal.NullDecimal
	err := db.PaymentOrder.WithContext(ctx).
		Select(db.PaymentOrder.Price.Sum().As("total")).
		Where(db.PaymentOrder.Status.Eq(order.StatusSuccess.Int16())).
		Scan(&total)
	if err != nil {
		zap.L().Error("orderRepo.GetAllTradeAmounts failed", zap.Error(err))
		return decimal.Zero, server.InternalStatus
	}
	if total.Valid {
		return total.Decimal, nil
	}
	// 如果 total 不合法，返回零值
	return decimal.Zero, nil
}

func (s *OrderRepository) GetTradeAmountsRangeByTime(ctx context.Context, startTime, endTime time.Time) (decimal.Decimal, error) {
	db := s.db.Get(ctx)
	var total decimal.NullDecimal
	err := db.PaymentOrder.WithContext(ctx).
		Select(db.PaymentOrder.Price.Sum().As("total")).
		Where(db.PaymentOrder.Status.Eq(order.StatusSuccess.Int16())).
		Where(db.PaymentOrder.CreatedAt.Between(startTime, endTime)).
		Scan(&total)
	if err != nil {
		zap.L().Error("orderRepo.GetAllTradeAmounts failed", zap.Error(err))
		return decimal.Zero, server.InternalStatus
	}
	if total.Valid {
		return total.Decimal, nil
	}
	// 如果 total 不合法，返回零值
	return decimal.Zero, nil
}

func (s *OrderRepository) GetProjectTradeAmounts(ctx context.Context, projectID string) (decimal.Decimal, error) {
	db := s.db.Get(ctx)
	var total decimal.NullDecimal
	err := db.PaymentOrderSecondary.WithContext(ctx).
		Select(db.PaymentOrderSecondary.Price.Sum().As("total")).
		LeftJoin(db.PaymentOrder, db.PaymentOrder.ID.EqCol(db.PaymentOrderSecondary.OrderID)).
		Where(
			db.PaymentOrder.Status.Eq(order.StatusSuccess.Int16()),
			db.PaymentOrderSecondary.ProjectID.Eq(projectID),
		).
		Scan(&total)
	if err != nil {
		zap.L().Error("GetProjectTradeAmountsRangeByTime failed", zap.Error(err))
		return decimal.Zero, server.InternalStatus
	}
	if total.Valid {
		return total.Decimal, nil
	}
	return decimal.Zero, nil
}

func (s *OrderRepository) GetProjectTradeAmountsRangeByTime(ctx context.Context, projectID string, startTime, endTime time.Time) (decimal.Decimal, error) {
	db := s.db.Get(ctx)
	var total decimal.NullDecimal
	err := db.PaymentOrderSecondary.WithContext(ctx).
		Select(db.PaymentOrderSecondary.Price.Sum().As("total")).
		LeftJoin(db.PaymentOrder, db.PaymentOrder.ID.EqCol(db.PaymentOrderSecondary.OrderID)).
		Where(
			db.PaymentOrder.Status.Eq(order.StatusSuccess.Int16()),
			db.PaymentOrder.CreatedAt.Between(startTime, endTime),
			db.PaymentOrderSecondary.ProjectID.Eq(projectID),
		).
		Scan(&total)
	if err != nil {
		zap.L().Error("GetProjectTradeAmountsRangeByTime failed", zap.Error(err))
		return decimal.Zero, server.InternalStatus
	}
	if total.Valid {
		return total.Decimal, nil
	}
	return decimal.Zero, nil
}

// GetProjectsTradeAmountsRangeByTime 获取多个项目在指定时间范围内的交易额
func (s *OrderRepository) GetProjectsTradeAmountsRangeByTime(ctx context.Context, projectIDs []string, startTime, endTime time.Time, isAsc bool) ([]*project.ProjectWithAmount, error) {
	db := s.db.Get(ctx)

	var results []*project.ProjectWithAmount
	sort := db.PaymentOrderSecondary.Price.Sum().Desc()
	if isAsc {
		sort = db.PaymentOrderSecondary.Price.Sum().Asc()
	}

	err := db.PaymentOrderSecondary.WithContext(ctx).
		Select(db.PaymentOrderSecondary.ProjectID, db.PaymentOrderSecondary.Price.Sum().As("amount")).
		LeftJoin(db.PaymentOrder, db.PaymentOrder.ID.EqCol(db.PaymentOrderSecondary.OrderID)).
		Where(
			db.PaymentOrder.Status.Eq(order.StatusSuccess.Int16()),
			db.PaymentOrderSecondary.ProjectID.In(projectIDs...),
			db.PaymentOrder.CreatedAt.Between(startTime, endTime),
		).
		Group(db.PaymentOrderSecondary.ProjectID).
		Order(sort).
		Scan(&results)
	if err != nil {
		zap.L().Error("GetProjectsTradeAmountsRangeByTime failed", zap.Error(err))
		return nil, server.InternalStatus
	}

	return results, nil
}

func (s *OrderRepository) GetOrderByOrderNo(ctx context.Context, orderNo string) (*order.Order, error) {
	d := s.db.Get(ctx)
	res, err := d.PaymentOrder.WithContext(ctx).Where(d.PaymentOrder.OrderNo.Eq(orderNo)).First()
	if err != nil {
		zap.L().Error("GetOrderByOrderNo failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	return order.NewFromModel(res), nil
}

func (s *OrderRepository) GetUniqueOrderIDsByNftID(ctx context.Context, nftID string, sortF string, isAsc bool, page, pageSize int64) ([]string, int64, error) {
	d := s.db.Get(ctx)

	// 构建排序字段
	sortField := field.NewField(d.PaymentOrderSecondary.TableName(), sortF).Desc()
	if isAsc {
		sortField = field.NewField(d.PaymentOrderSecondary.TableName(), sortF).Asc()
	}

	// 查询唯一的 orderID
	var orderIDs []string
	q := d.PaymentOrderSecondary.WithContext(ctx).
		Select(d.PaymentOrderSecondary.OrderID.Distinct()).
		Where(d.PaymentOrderSecondary.NftID.Eq(nftID)).
		Order(sortField)

	// 获取总数
	total, err := q.Count()
	if err != nil {
		zap.L().Error("GetUniqueOrderIDsByNftID failed to count", zap.Error(err))
		return nil, 0, server.InternalStatus
	}

	// 分页查询
	err = q.Offset(int((page - 1) * pageSize)).Limit(int(pageSize)).Scan(&orderIDs)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		zap.L().Error("GetUniqueOrderIDsByNftID failed to query", zap.Error(err))
		return nil, 0, server.InternalStatus
	}

	return orderIDs, total, nil
}

func (s *OrderRepository) GetUniqueOrderIDsByNftIDWithCriteria(ctx context.Context, nftID string, criteria *order.SecondaryOrderCriteria) ([]string, int64, error) {
	d := s.db.Get(ctx)

	// 构建排序字段
	sortField := field.NewField(d.PaymentOrderSecondary.TableName(), criteria.SortField).Desc()
	if criteria.IsAsc {
		sortField = field.NewField(d.PaymentOrderSecondary.TableName(), criteria.SortField).Asc()
	}

	// 查询唯一的 orderID
	var orderIDs []string
	q := d.PaymentOrderSecondary.WithContext(ctx).
		Select(d.PaymentOrderSecondary.OrderID.Distinct(),
			field.NewField(d.PaymentOrderSecondary.TableName(), criteria.SortField),
		).
		Where(d.PaymentOrderSecondary.NftID.Eq(nftID))

	// 构建查询条件
	if criteria.UserID != nil {
		q.Where(d.PaymentOrderSecondary.UserID.Eq(*criteria.UserID))
	}
	/* 由于使用了 distinct， sellerID 不能作为查询条件
	if criteria.SellerID != nil {
		q.Where(d.PaymentOrderSecondary.SellerID.Eq(*criteria.SellerID))
	}
	*/
	if criteria.StartTime != nil {
		q = q.Where(d.PaymentOrderSecondary.CreatedAt.Gte(*criteria.StartTime))
	}
	if criteria.EndTime != nil {
		q = q.Where(d.PaymentOrderSecondary.CreatedAt.Lte(*criteria.EndTime))
	}
	if criteria.MinPrice != nil {
		q = q.Where(d.PaymentOrderSecondary.Price.Gte(*criteria.MinPrice))
	}
	if criteria.MaxPrice != nil {
		q = q.Where(d.PaymentOrderSecondary.Price.Lte(*criteria.MaxPrice))
	}
	q = q.Order(sortField)

	// 分页查询
	res, total, err := q.FindByPage(int((criteria.Page-1)*criteria.PageSize), int(criteria.PageSize))

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		zap.L().Error("GetUniqueOrderIDsByNftID failed to query", zap.Error(err))
		return nil, 0, server.InternalStatus
	}
	for _, r := range res {
		orderIDs = append(orderIDs, r.OrderID)
	}
	return orderIDs, total, nil
}

func (s *OrderRepository) GetOrdersWithSecondaryExtrasByOrderIDs(ctx context.Context, orderIDs []string) ([]*order.OrderWithSecondaryExtra, error) {
	db := s.db.Get(ctx)

	// 查询订单信息
	orders, err := db.PaymentOrder.WithContext(ctx).
		Where(db.PaymentOrder.ID.In(orderIDs...)).
		Find()
	if err != nil {
		zap.L().Error("GetOrdersWithSecondaryExtras failed to query orders", zap.Error(err))
		return nil, server.InternalStatus
	}

	// 查询对应的 extras 信息
	secondaryExtras, err := db.PaymentOrderSecondary.WithContext(ctx).
		Where(db.PaymentOrderSecondary.OrderID.In(orderIDs...)).
		Find()
	if err != nil {
		zap.L().Error("GetOrdersWithSecondaryExtras failed to query secondary extras", zap.Error(err))
		return nil, server.InternalStatus
	}

	// 构建 orderID 到 extras 的映射
	extrasMap := make(map[string][]*order.SecondaryExtra)
	for _, extra := range secondaryExtras {
		extrasMap[extra.OrderID] = append(extrasMap[extra.OrderID], order.NewSecondaryExtraFromModel(extra))
	}

	// 构建结果
	result := make([]*order.OrderWithSecondaryExtra, 0, len(orders))
	for _, od := range orders {
		result = append(result, &order.OrderWithSecondaryExtra{
			Order:  order.NewFromModel(od),
			Extras: extrasMap[od.ID],
		})
	}

	return result, nil
}

func (s *OrderRepository) GetOrdersByCriteria(ctx context.Context, criteria *order.OrderCriteria) ([]*order.Order, int64, error) {
	d := s.db.Get(ctx)

	// 构建排序字段
	sortField := field.NewField(d.PaymentOrder.TableName(), criteria.SortField).Desc()
	if criteria.IsAsc {
		sortField = field.NewField(d.PaymentOrder.TableName(), criteria.SortField).Asc()
	}

	q := d.PaymentOrder.WithContext(ctx)

	// 构建查询条件
	if criteria.UserID != nil {
		q = q.Where(d.PaymentOrder.UserID.Eq(*criteria.UserID))
	}
	if criteria.Status != nil {
		q = q.Where(d.PaymentOrder.Status.Eq(criteria.Status.Int16()))
	}
	if criteria.OrderType != nil {
		q = q.Where(d.PaymentOrder.Type.Eq(criteria.OrderType.Int16()))
	}
	if criteria.PayType != nil {
		q = q.Where(d.PaymentOrder.PayType.Eq(criteria.PayType.Int16()))
	}
	if criteria.StartTime != nil {
		q = q.Where(d.PaymentOrder.CreatedAt.Gte(*criteria.StartTime))
	}
	if criteria.EndTime != nil {
		q = q.Where(d.PaymentOrder.CreatedAt.Lte(*criteria.EndTime))
	}
	if criteria.MinPrice != nil {
		q = q.Where(d.PaymentOrder.Price.Gte(*criteria.MinPrice))
	}
	if criteria.MaxPrice != nil {
		q = q.Where(d.PaymentOrder.Price.Lte(*criteria.MaxPrice))
	}

	orders, cnt, err := q.Order(sortField).FindByPage(int((criteria.Page-1)*criteria.PageSize), int(criteria.PageSize))
	if err != nil {
		zap.L().Error("GetOrdersByCriteria failed", zap.Error(err))
		return nil, 0, server.InternalStatus
	}

	orderList := make([]*order.Order, 0, len(orders))
	for _, od := range orders {
		orderList = append(orderList, order.NewFromModel(od))
	}
	return orderList, cnt, nil
}

func (s *OrderRepository) GetOrdersByListingIDs(ctx context.Context, listingIDs []string) (map[string]*order.Order, error) {
	d := s.db.Get(ctx)

	type scanRes struct {
		model.PaymentOrder
		ListingID string `gorm:"column:listing_id"`
	}

	var resultRows []scanRes

	err := d.PaymentOrderSecondary.WithContext(ctx).
		Select(
			d.PaymentOrderSecondary.OrderID,
			d.PaymentOrder.OrderNo,
			d.PaymentOrderSecondary.ListingID,
		).
		Where(d.PaymentOrderSecondary.ListingID.In(listingIDs...)).
		LeftJoin(
			d.PaymentOrder,
			d.PaymentOrder.ID.EqCol(d.PaymentOrderSecondary.OrderID),
		).
		Scan(&resultRows) // 注意要传指针
	if err != nil {
		zap.L().Error("GetOrdersByListingIDs failed", zap.Error(err))
		return nil, server.InternalStatus
	}

	// 转成 map[ListingID]*order.Order
	ordersByListing := make(map[string]*order.Order, len(resultRows))
	for _, row := range resultRows {
		ordersByListing[row.ListingID] = order.NewFromModel(&row.PaymentOrder)
	}

	return ordersByListing, nil
}

func (s *OrderRepository) GetOrderAmountByUserID(ctx context.Context, userID string, startTime, endTime int64) (decimal.Decimal, error) {
	d := s.db.Get(ctx)

	// 转换毫秒时间戳为时间对象
	startTimeObj := time.UnixMilli(startTime)
	endTimeObj := time.UnixMilli(endTime)

	// 使用decimal.NullDecimal来处理NULL值的情况
	var result decimal.NullDecimal
	err := d.PaymentOrder.WithContext(ctx).
		Select(d.PaymentOrder.Price.Sum().As("total_amount")).
		Where(d.PaymentOrder.UserID.Eq(userID)).
		Where(d.PaymentOrder.Status.Eq(order.StatusSuccess.Int16())).
		Where(d.PaymentOrder.CreatedAt.Gte(startTimeObj)).
		Where(d.PaymentOrder.CreatedAt.Lte(endTimeObj)).
		Scan(&result)
	if err != nil {
		zap.L().Error("GetOrderAmountByUserID failed", zap.Error(err),
			zap.String("user_id", userID),
			zap.Time("start_time", startTimeObj),
			zap.Time("end_time", endTimeObj))
		return decimal.Zero, server.InternalStatus
	}

	// 如果结果无效（即为NULL），则返回零值
	if !result.Valid {
		return decimal.Zero, nil
	}

	return result.Decimal, nil
}

func (s *OrderRepository) BatchGetOrderAmountByUserID(ctx context.Context, userIDs []string, startTime, endTime int64) (map[string]decimal.Decimal, error) {
	d := s.db.Get(ctx)

	type userAmount struct {
		UserID      string              `gorm:"column:user_id"`
		TotalAmount decimal.NullDecimal `gorm:"column:total_amount"`
	}

	startTimeObj := time.UnixMilli(startTime)
	endTimeObj := time.UnixMilli(endTime)

	var results []userAmount
	err := d.PaymentOrder.WithContext(ctx).
		Select(d.PaymentOrder.UserID, d.PaymentOrder.Price.Sum().As("total_amount")).
		Where(d.PaymentOrder.UserID.In(userIDs...)).
		Where(d.PaymentOrder.Status.Eq(order.StatusSuccess.Int16())).
		Where(d.PaymentOrder.CreatedAt.Gte(startTimeObj)).
		Where(d.PaymentOrder.CreatedAt.Lte(endTimeObj)).
		Where(d.PaymentOrder.Type.Eq(order.TypeSecondaryMarket.Int16())).
		Group(d.PaymentOrder.UserID).
		Scan(&results)
	if err != nil {
		zap.L().Error("BatchGetOrderAmountByUserID failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	orderAmounts := make(map[string]decimal.Decimal, len(userIDs))
	for _, userID := range userIDs {
		orderAmounts[userID] = decimal.Zero // 默认0
	}
	for _, r := range results {
		if r.TotalAmount.Valid {
			orderAmounts[r.UserID] = r.TotalAmount.Decimal
		}
	}
	return orderAmounts, nil
}

// GetPrimaryOrdersByUserIDAndProjectID 根据用户ID和项目ID获取一级市场订单
func (s *OrderRepository) GetPrimaryOrdersByUserIDAndProjectID(ctx context.Context, userID string, projectID string) ([]*model.PaymentOrderPrimary, error) {
	db := s.db.Get(ctx)

	// 查询符合条件的一级市场订单
	// 通过联表查询获取同时满足用户ID、项目ID和状态条件的一级市场订单
	primaryOrders, err := db.PaymentOrderPrimary.WithContext(ctx).
		Select(db.PaymentOrderPrimary.ALL).
		Where(db.PaymentOrderPrimary.ProjectID.Eq(projectID)).
		Join(db.PaymentOrder, db.PaymentOrder.ID.EqCol(db.PaymentOrderPrimary.OrderID)).
		Where(db.PaymentOrder.UserID.Eq(userID)).
		Where(db.PaymentOrder.Type.Eq(order.TypePrimaryMarket.Int16())).
		Where(
			// 状态为已支付或支付中但未过期
			db.PaymentOrder.Status.In(
				order.StatusSuccess.Int16(),
				order.StatusPaying.Int16(),
			),
		).
		Find()

	if err != nil {
		zap.L().Error("GetPrimaryOrdersByUserIDAndProjectID failed", zap.Error(err),
			zap.String("user_id", userID),
			zap.String("project_id", projectID))
		return nil, server.InternalStatus
	}

	return primaryOrders, nil
}
func (s *OrderRepository) GetOrdersByTime(ctx context.Context, startTime int64, endTime int64) ([]*order.Order, error) {
	d := s.db.Get(ctx)

	orders, err := d.PaymentOrder.WithContext(ctx).
		Where(d.PaymentOrder.Status.Eq(order.StatusSuccess.Int16())).
		Where(d.PaymentOrder.CreatedAt.Gte(time.UnixMilli(startTime))).
		Where(d.PaymentOrder.CreatedAt.Lt(time.UnixMilli(endTime))).
		Where(d.PaymentOrder.Type.Eq(order.TypeSecondaryMarket.Int16())).
		Find()
	if err != nil {
		zap.L().Error("GetOrdersByUserIDs failed", zap.Error(err))
		return nil, server.InternalStatus
	}

	orderList := make([]*order.Order, 0, len(orders))
	for _, od := range orders {
		orderList = append(orderList, order.NewFromModel(od))
	}
	return orderList, nil
}
func (s *OrderRepository) GetSecondaryOrderByOrderID(ctx context.Context, orderIDs []string) ([]*order.SecondaryExtra, error) {
	d := s.db.Get(ctx)

	find, err := d.PaymentOrderSecondary.WithContext(ctx).Where(d.PaymentOrderSecondary.OrderID.In(orderIDs...)).Find()

	if err != nil {
		zap.L().Error("GetSecondaryOrderByOrderID failed", zap.Error(err))
		return nil, server.InternalStatus
	}

	result := make([]*order.SecondaryExtra, 0, len(find))
	for _, r := range find {
		result = append(result, order.NewSecondaryExtraFromModel(r))
	}

	return result, nil
}

func (s *OrderRepository) GetPrimaryExtraByOrderID(ctx context.Context, orderID string) ([]*order.PrimaryExtra, error) {
	db := s.db.Get(ctx)
	models, err := db.PaymentOrderPrimary.WithContext(ctx).
		Where(db.PaymentOrderPrimary.OrderID.Eq(orderID)).
		Find()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		zap.L().Error("GetPrimaryExtraByOrderID failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	domainExtras := make([]*order.PrimaryExtra, 0, len(models))
	for _, extra := range models {
		domainExtras = append(domainExtras, order.NewPrimaryExtraFromModel(extra))
	}
	return domainExtras, nil
}
