package repository

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/common/server"
	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/query"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/wallet"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var _ wallet.Repo = (*WalletRepository)(nil)

type WalletRepository struct {
	db *db.DB[*query.Query]
}

func NewWalletRepository(db *db.DB[*query.Query]) *WalletRepository {
	return &WalletRepository{db: db}
}

var (
	lastTimestamp int64 = -1
	sequence      int64 = 0
	workerID      int64 = 1 // 可以根据实际需求配置
	twepoch       int64 = 1514736000000
)

// 生成下一个ID
func generateNextID() (int64, error) {
	timestamp := timeGen()

	// 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过
	if timestamp < lastTimestamp {
		return 0, fmt.Errorf("clock moved backwards, refusing to generate id for %d milliseconds", lastTimestamp-timestamp)
	}

	// 如果是同一时间生成的，则进行毫秒内序列
	if lastTimestamp == timestamp {
		sequence = sequence + 1
		if sequence > 999 { // 毫秒内序列溢出
			sequence = 0
			timestamp = tilNextMillis(lastTimestamp)
		}
	} else {
		// 时间戳改变，毫秒内序列+1
		sequence = sequence + 1
		if sequence > 999 {
			sequence = 0
		}
	}

	lastTimestamp = timestamp

	// 生成ID
	var builder strings.Builder
	builder.WriteString(fmt.Sprintf("%d", workerID))
	builder.WriteString(fmt.Sprintf("%d", timestamp-twepoch))

	// 格式化序列号，保证3位
	if sequence < 10 {
		builder.WriteString(fmt.Sprintf("00%d", sequence))
	} else if sequence < 100 {
		builder.WriteString(fmt.Sprintf("0%d", sequence))
	} else {
		builder.WriteString(fmt.Sprintf("%d", sequence))
	}

	id, err := strconv.ParseInt(builder.String(), 10, 64)
	if err != nil {
		return 0, fmt.Errorf("parse id failed: %v", err)
	}

	return id, nil
}

// 获取下一个毫秒级时间戳
func tilNextMillis(lastTimestamp int64) int64 {
	timestamp := timeGen()
	for timestamp <= lastTimestamp {
		timestamp = timeGen()
	}
	return timestamp
}

// 获取当前毫秒级时间戳
func timeGen() int64 {
	return time.Now().UnixMilli()
}

func (w *WalletRepository) GetYeePayWalletByUserID(ctx context.Context, id string) (*wallet.YeePay, error) {
	q := w.db.Get(ctx)
	yeepayWallet, err := q.PaymentUserYeepayWallet.WithContext(ctx).
		Where(q.PaymentUserYeepayWallet.UserID.Eq(id)).
		First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		zap.L().Error("get YeePayWallet failed", zap.Error(err))
		return nil, server.InternalStatus
	}

	return wallet.NewYeePayWalletFromModel(yeepayWallet), nil
}

func (w *WalletRepository) BatchGetYeePayWalletsByUserIDs(ctx context.Context, ids []string) (map[string]*wallet.YeePay, error) {
	if len(ids) == 0 {
		return make(map[string]*wallet.YeePay), nil
	}

	q := w.db.Get(ctx)
	wallets, err := q.PaymentUserYeepayWallet.WithContext(ctx).
		Where(q.PaymentUserYeepayWallet.UserID.In(ids...)).
		Find()
	if err != nil {
		zap.L().Error("batch get YeePayWallets failed", zap.Error(err), zap.Strings("user_ids", ids))
		return nil, server.InternalStatus
	}

	result := make(map[string]*wallet.YeePay, len(wallets))
	for _, w := range wallets {
		result[w.UserID] = wallet.NewYeePayWalletFromModel(w)
	}

	return result, nil
}

func (w *WalletRepository) InsertYeePayWallet(ctx context.Context, wallet *wallet.YeePay) error {
	q := w.db.Get(ctx)
	err := q.PaymentUserYeepayWallet.WithContext(ctx).Create(wallet.ToModel())

	if err != nil && !errors.Is(err, gorm.ErrDuplicatedKey) {
		zap.L().Error("insert YeePayWallet failed", zap.Error(err))
		return server.InternalStatus
	}

	return nil
}

func (w *WalletRepository) ApplyWalletCallback(ctx context.Context, wallet *wallet.YeePay) error {
	q := w.db.Get(ctx)
	info, err := q.PaymentUserYeepayWallet.WithContext(ctx).
		Select(
			q.PaymentUserYeepayWallet.BusinessNo,
			q.PaymentUserYeepayWallet.MemberID,
		).
		Where(q.PaymentUserYeepayWallet.ExternalUserID.Eq(wallet.ExternalUserID())).
		Updates(wallet.ToModel())
	if err != nil {
		zap.L().Error("update YeePayWallet failed", zap.Error(err))
		return server.InternalStatus
	}
	if info.RowsAffected == 0 {
		zap.L().Error("update YeePayWallet failed", zap.String("memberID", *wallet.MemberID()), zap.String("externalUserID", wallet.ExternalUserID()))
		return server.InternalStatus
	}
	return nil
}

func (w *WalletRepository) GetYeePayWalletByUserIDs(ctx context.Context, ids []string) ([]*wallet.YeePay, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	db := w.db.Get(ctx)
	result, err := db.PaymentUserYeepayWallet.WithContext(ctx).
		Where(db.PaymentUserYeepayWallet.UserID.In(ids...)).
		Find()
	if err != nil {
		zap.L().Error("GetYeePayWalletByUserIDs failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	yeepayWallets := make([]*wallet.YeePay, 0, len(result))
	for _, r := range result {
		yeepayWallets = append(yeepayWallets, wallet.NewYeePayWalletFromModel(r))
	}
	return yeepayWallets, nil
}
