package repository

import (
	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/query"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/refund"
	"context"
)

var _ refund.Repo = (*RefundRepository)(nil)

type RefundRepository struct {
	db *db.DB[*query.Query]
}

func NewRefundRepository(db *db.DB[*query.Query]) *RefundRepository {
	return &RefundRepository{db: db}
}

func (r *RefundRepository) CreateRefund(ctx context.Context, refund *refund.Refund) error {
	db := r.db.Get(ctx)
	err := db.PaymentPayYeepayRefund.WithContext(ctx).Create(refund.ToModel())
	if err != nil {
		return err
	}
	return nil
}
