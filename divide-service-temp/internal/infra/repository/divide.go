package repository

import (
	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/common/server"
	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/query"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/divide"
	"context"
	"go.uber.org/zap"
)

var _ divide.Repo = (*DivideRepository)(nil)

type DivideRepository struct {
	db *db.DB[*query.Query]
}

func NewDivideRepository(db *db.DB[*query.Query]) *DivideRepository {
	return &DivideRepository{db: db}
}

func (d *DivideRepository) CreateDivideRecord(ctx context.Context, divides []*divide.YeepayDivide) error {
	db := d.db.Get(ctx)
	models := make([]*model.PaymentPayYeepayDivide, 0, len(divides))
	for _, d := range divides {
		models = append(models, d.ToModel())
	}
	err := db.PaymentPayYeepayDivide.WithContext(ctx).
		Create(models...)
	if err != nil {
		zap.L().Error("create repository record failed", zap.Error(err))
		return server.InternalStatus
	}
	return nil
}

func (d *DivideRepository) GetDivideFeeByOrderIDs(ctx context.Context, orderIDs []string) ([]*divide.YeepayDivide, error) {
	db := d.db.Get(ctx)
	divides, err := db.PaymentPayYeepayDivide.WithContext(ctx).
		Where(db.PaymentPayYeepayDivide.OrderID.In(orderIDs...)).
		Where(db.PaymentPayYeepayDivide.MemberType.Eq(divide.MemberTypeSellerSide.Int16())).
		Find()
	if err != nil {
		zap.L().Error("get repository fee by time failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	result := make([]*divide.YeepayDivide, 0, len(divides))
	for _, d := range divides {
		result = append(result, divide.NewDivideFromModel(d))
	}
	return result, nil
}
