package infra

import (
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/divide"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/pay"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/refund"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/wallet"
	"cnb.cool/cymirror/ces-services/divide-service/internal/infra/repository"
	"github.com/google/wire"
)

// InfraProviderSet Infrastructure providers
var InfraProviderSet = wire.NewSet(
	repository.NewOrderRepository,
	wire.Bind(new(order.Repo), new(*repository.OrderRepository)),
	repository.NewPayRepository,
	wire.Bind(new(pay.Repo), new(*repository.PayRepository)),
	repository.NewWalletRepository,
	wire.Bind(new(wallet.Repo), new(*repository.WalletRepository)),
	repository.NewRefundRepository,
	wire.Bind(new(refund.Repo), new(*repository.RefundRepository)),
	repository.NewDivideRepository,
	wire.Bind(new(divide.Repo), new(*repository.DivideRepository)),
)
