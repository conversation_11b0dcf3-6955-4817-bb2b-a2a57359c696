package yeepay

import (
	"cnb.cool/cymirror/ces-services/common/server"
	"context"
	"github.com/yop-platform/yop-go-sdk/yop/constants"
	"github.com/yop-platform/yop-go-sdk/yop/request"
	"go.uber.org/zap"
	"strings"
)

type WalletRegisterLoginCmd struct {
	UserID        string // 用户id
	Name          string // 姓名
	CertificateNo string // 身份证号
	Mobile        string // 手机号
	RedirectURL   string // 重定向地址
	RequestNo     string // 请求号
}

type walletRegisterLoginResp struct {
	Url string `json:"url"` // 验密签约确认控件URL
}

type WalletRegisterLoginResult struct {
	Url string `json:"url"` // 验密签约确认控件URL
}

// WalletRegisterLogin 钱包注册登录 https://open.yeepay.com/docs-v2/apis/docking-wallet/post__rest__v2.0__m-wallet__wallet__index
func (c *Client) WalletRegisterLogin(ctx context.Context, cmd *WalletRegisterLoginCmd) (*WalletRegisterLoginResult, error) {
	cfg := c.yeePayConfig
	var yopRequest = request.NewYopRequest(constants.POST_HTTP_METHOD, "/rest/v2.0/m-wallet/wallet/index")
	yopRequest.AppId = cfg.AppKey
	yopRequest.AddParam("parentMerchantNo", cfg.MerchantNo)
	yopRequest.AddParam("merchantNo", cfg.MerchantNo)
	yopRequest.AddParam("merchantUserNo", strings.ReplaceAll(cmd.UserID, "-", ""))
	yopRequest.AddParam("name", cmd.Name)                   // 姓名
	yopRequest.AddParam("certificateType", "IDENTITY_CARD") // 身份证
	yopRequest.AddParam("certificateNo", cmd.CertificateNo) // 身份证号
	// yopRequest.AddParam("mobile", cmd.Mobile)               // 手机号
	yopRequest.AddParam("returnUrl", cmd.RedirectURL)     // 钱包首页返回商户页面地址
	yopRequest.AddParam("notifyUrl", cfg.WalletNotifyURL) // 回调地址
	yopRequest.AddParam("requestNo", cmd.RequestNo)       // 请求号
	yopRequest.IsvPriKey = *c.priKey

	response, err := c.client.Request(yopRequest)
	if err != nil {
		zap.L().Error("wallet register login request failed", zap.Error(err), zap.Any("cmd", cmd))
		return nil, server.InternalStatus
	}

	var resp walletRegisterLoginResp
	resultMap := response.Result.(map[string]any)
	resp.Url, _ = resultMap["url"].(string)
	zap.L().Info("wallet register login result", zap.String("content", string(response.Content)), zap.Any("cmd", cmd))

	return &WalletRegisterLoginResult{Url: resp.Url}, nil
}

type GetUserWalletInfoCmd struct {
	MerchantUserNo string // 用户侧商户用户 id
}

type GetUserWalletInfoResp struct {
	Balance string `json:"balance"` // 余额
}

type GetUserWalletInfoResult struct {
	Balance string // 余额
}

// GetUserWalletInfo 获取用户钱包信息 https://open.yeepay.com/docs/apis/bzshsfk/get__rest__v1.0__m-wallet__account__query
func (c *Client) GetUserWalletInfo(cmd *GetUserWalletInfoCmd) (*GetUserWalletInfoResult, error) {
	cfg := c.yeePayConfig
	var yopRequest = request.NewYopRequest(constants.GET_HTTP_METHOD, "/rest/v1.0/m-wallet/account/query")
	yopRequest.AppId = cfg.AppKey

	yopRequest.AddParam("merchantUserNo", strings.ReplaceAll(cmd.MerchantUserNo, "-", "")) // 用户侧商户用户 id
	yopRequest.AddParam("merchantNo", cfg.MerchantNo)                                      // 商户编号
	yopRequest.AddParam("parentMerchantNo", cfg.MerchantNo)                                // 发起方商编 与商编相同
	yopRequest.IsvPriKey = *c.priKey

	response, err := c.client.Request(yopRequest)
	if err != nil {
		zap.L().Error("wallet get wallet balance request failed", zap.Error(err))
	}
	var resp GetUserWalletInfoResp
	resultMap := response.Result.(map[string]any)
	resp.Balance, _ = resultMap["balance"].(string)

	return &GetUserWalletInfoResult{Balance: resp.Balance}, nil
}

type GetUserWalletMemberAccountInfoCmd struct {
	UserID string // 商户侧用户 id
}

type etUserWalletMemberAccountInfoResp struct {
	MemberNo string `json:"memberNo"` // 会员号
}

type GetUserWalletMemberAccountInfoResult struct {
	MemberID string // 会员号
}

// GetUserWalletMemberAccountInfo 钱包账户信息查询 https://open.yeepay.com/docs/apis/bzshsfk/get__rest__v1.0__m-wallet__member__query
func (c *Client) GetUserWalletMemberAccountInfo(ctx context.Context, cmd *GetUserWalletMemberAccountInfoCmd) (*GetUserWalletMemberAccountInfoResult, error) {
	cfg := c.yeePayConfig
	var yopRequest = request.NewYopRequest(constants.GET_HTTP_METHOD, "/rest/v1.0/m-wallet/member/query")
	yopRequest.AppId = cfg.AppKey

	yopRequest.AddParam("externalUserId", strings.ReplaceAll(cmd.UserID, "-", "")) // 用户侧商户用户 id
	yopRequest.AddParam("merchantNo", cfg.MerchantNo)                              // 商户编号
	yopRequest.AddParam("parentMerchantNo", cfg.MerchantNo)                        // 发起方商编 与商编相同
	yopRequest.IsvPriKey = *c.priKey

	response, err := c.client.Request(yopRequest)
	if err != nil {
		zap.L().Error("wallet get wallet balance request failed", zap.Error(err))
	}
	var resp etUserWalletMemberAccountInfoResp
	resultMap := response.Result.(map[string]any)
	resp.MemberNo, _ = resultMap["memberNo"].(string)

	zap.L().Info("get user wallet member account info result", zap.Any("cmd", cmd), zap.Any("resp", response.Content))

	return &GetUserWalletMemberAccountInfoResult{MemberID: resp.MemberNo}, nil
}
