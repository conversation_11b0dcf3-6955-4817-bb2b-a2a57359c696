package yeepay

import (
	"cnb.cool/cymirror/ces-services/common/server"
	"github.com/shopspring/decimal"
	"github.com/yop-platform/yop-go-sdk/yop/constants"
	"github.com/yop-platform/yop-go-sdk/yop/request"
	"go.uber.org/zap"
)

const APISuccessStatusString = "SUCCESS"
const APIFailStatusString = "FAILED"

var (
	// RefundStatusSuccess 退款成功
	RefundStatusSuccess = RefundStatus{value: APISuccessStatusString}
	// RefundStatusFail 退款失败
	RefundStatusFail = RefundStatus{value: "FAIL"}
)

type RefundStatus struct {
	value string
}

// RefundCmd 退款 Command
type RefundCmd struct {
	OrderID         string          // 商户请求收款的交易单号
	RefundRequestID string          // 退款请求号
	RefundAmount    decimal.Decimal // 退款金额
}

// RefundResult 退款结果
type RefundResult struct {
	Status string `json:"status"` // PROCESSING：退款处理中 SUCCESS：退款成功 FAILED：退款失败 CANCEL:退款关闭,商户通知易宝结束该笔退款后返回该状态
}

type refundResp struct {
	Code   string `json:"code"`   // OPR00000表示易宝受理成功
	Status string `json:"status"` // PROCESSING：退款处理中 SUCCESS：退款成功 FAILED：退款失败 CANCEL:退款关闭,商户通知易宝结束该笔退款后返回该状态
}

// Refund 退款
// https://open.yeepay.com/docs/apis/bzshsfk/post__rest__v1.0__trade__refund
func (c *Client) Refund(cmd *RefundCmd) (*RefundResult, error) {
	if cmd.RefundAmount.Exponent() < -2 {
		zap.L().Error("refund amount is too small", zap.String("amount", cmd.RefundAmount.String()))
		return nil, server.InternalStatus
	}

	cfg := c.yeePayConfig
	var yopRequest = request.NewYopRequest(constants.POST_HTTP_METHOD, "/rest/v1.0/trade/refund")
	yopRequest.AppId = cfg.AppKey
	yopRequest.AddParam("merchantNo", cfg.MerchantNo)              // 商户号
	yopRequest.AddParam("orderId", cmd.OrderID)                    // 商户请求收款的交易单号
	yopRequest.AddParam("refundRequestId", cmd.RefundRequestID)    // 退款请求号
	yopRequest.AddParam("refundAmount", cmd.RefundAmount.String()) // 退款金额
	yopRequest.IsvPriKey = *c.priKey

	response, err := c.client.Request(yopRequest)
	if err != nil {
		zap.L().Error("yeepay refund request failed", zap.Error(err))
		return nil, err
	}

	var resp refundResp
	resultMap := response.Result.(map[string]any)
	resp.Status, _ = resultMap["status"].(string)

	zap.L().Info("yeepay refund result", zap.Any("cmd", cmd), zap.Any("resp", response))

	return &RefundResult{
		Status: resp.Status,
	}, nil
}
