package yeepay

import (
	"cnb.cool/cymirror/ces-services/common/server"
	"github.com/bytedance/sonic"
	"github.com/shopspring/decimal"
	"github.com/yop-platform/yop-go-sdk/yop/client"
	"github.com/yop-platform/yop-go-sdk/yop/constants"
	"github.com/yop-platform/yop-go-sdk/yop/request"
	"go.uber.org/zap"
)

var (
	// DivideTypeMerchant2Merchant 分账给商户
	DivideTypeMerchant2Merchant = DivideType{value: "MERCHANT2MERCHANT"}
	// DivideTypeMerchant2Member 分账给个人会员
	DivideTypeMerchant2Member = DivideType{value: "MERCHANT2MEMBER"}
)

type DivideType struct {
	value string
}

func (l DivideType) String() string {
	return l.value
}

// Divide 分账
type Divide struct {
	Amount     decimal.Decimal // 分账金额，必填，两位小数，不可为0
	LedgerNo   string          // 分账接收方编号，必填。分账属性为分账给商户时，为接收分账资金的易宝商户编号；分账属性为分账给个人会员时，为接收分账资金的易宝会员
	LedgerType DivideType      // 分账属性，非必填。可选项如下：MERCHANT2MERCHANT（分账给商户），MERCHANT2MEMBER（分账给个人会员）。不填默认分账给商户
}

type ApplyDivideCmd struct {
	OrderID         string    // 商户请求收款的交易单号
	DivideRequestID string    // 商户分账请求号
	Divide          []*Divide // 分账详情
}

type applyDivideDetail struct {
	LedgerNoFrom     string `json:"ledgerNoFrom,omitempty"`     // 分账发起方编号，非必填。不填默认为收款商编，长度不能超过32字节。
	LedgerNo         string `json:"ledgerNo"`                   // 分账接收方编号，必填。分账属性为分账给商户时，为接收分账资金的易宝商户编号；分账属性为分账给个人会员时，为接收分账资金的易宝会员，长度不能超过32字节
	Amount           string `json:"amount"`                     // 分账金额，必填，两位小数，不可为0
	LedgerType       string `json:"ledgerType"`                 // 分账属性，非必填。可选项如下：MERCHANT2MERCHANT（分账给商户），MERCHANT2MEMBER（分账给个人会员）。不填默认分账给商户
	DivideDetailDesc string `json:"divideDetailDesc,omitempty"` // 分账说明，非必填，长度不能超过128字节。
}

type ApplyDivideResult struct {
	DivideRequestId   string // 分账请求id
	Status            string // 分账状态 PROCESSING:处理中（正常情况下不会出现，如长期状态未闭环，可联系易宝人工介入处理） SUCCESS:分账成功 FAIL:分账失败
	DivideSuccessDate string // 分账成功时间
	CreateDate        string // 分账申请时间
}

type applyDivideResp struct {
	DivideRequestId   string `json:"divideRequestId"`   // 分账请求id
	Status            string `json:"status"`            // 分账状态 PROCESSING:处理中（正常情况下不会出现，如长期状态未闭环，可联系易宝人工介入处理） SUCCESS:分账成功 FAIL:分账失败
	DivideSuccessDate string `json:"divideSuccessDate"` // 分账成功时间
	CreateDate        string `json:"createDate"`        // 分账申请时间
}

// ApplyDivide 申请分账 https://open.yeepay.com/docs/apis/share/post__rest__v1.0__divide__apply
func (c *Client) ApplyDivide(cmd *ApplyDivideCmd) (*ApplyDivideResult, error) {
	var divideDetails []*applyDivideDetail
	for _, d := range cmd.Divide {
		// 确保金额是小数点后两位
		if d.Amount.Exponent() < -2 {
			zap.L().Error("apply repository amount is too large", zap.String("amount", d.Amount.String()))
			return nil, server.InternalStatus
		}
		divideDetails = append(divideDetails, &applyDivideDetail{
			LedgerNo:   d.LedgerNo,
			Amount:     d.Amount.String(),
			LedgerType: d.LedgerType.String(),
		})
	}
	divideDetailsJSON, err := sonic.MarshalString(divideDetails)
	if err != nil {
		zap.L().Error("marshal repository details error", zap.Error(err))
		return nil, server.InternalStatus
	}

	cfg := c.yeePayConfig
	var yopRequest = request.NewYopRequest(constants.POST_HTTP_METHOD, "/rest/v1.0/repository/apply")
	yopRequest.AppId = cfg.AppKey

	yopRequest.AddParam("parentMerchantNo", cfg.MerchantNo)
	yopRequest.AddParam("merchantNo", cfg.MerchantNo)           // 商户编号
	yopRequest.AddParam("orderId", cmd.OrderID)                 // 商户请求收款的交易单号
	yopRequest.AddParam("divideRequestId", cmd.DivideRequestID) // 商户分账请求号
	yopRequest.AddParam("divideDetail", divideDetailsJSON)      // 分账详情
	yopRequest.IsvPriKey = *c.priKey

	response, err := client.DefaultClient.Request(yopRequest)
	if err != nil {
		zap.L().Error("yeepay apply repository request failed", zap.Error(err))
		return nil, err
	}

	var resp applyDivideResp
	resultMap := response.Result.(map[string]any)
	resp.DivideRequestId, _ = resultMap["divideRequestId"].(string)     // 分账请求号
	resp.Status, _ = resultMap["status"].(string)                       // SUCCESS（成功）
	resp.DivideSuccessDate, _ = resultMap["divideSuccessDate"].(string) // 分账完成时间
	resp.CreateDate, _ = resultMap["createDate"].(string)               // 分账申请时间

	zap.L().Info("apply repository result", zap.Any("resp", resp), zap.String("content", string(response.Content)), zap.Any("cmd", cmd))

	return &ApplyDivideResult{
		DivideRequestId:   resp.DivideRequestId,
		Status:            resp.Status,
		DivideSuccessDate: resp.DivideSuccessDate,
		CreateDate:        resp.CreateDate,
	}, nil
}
