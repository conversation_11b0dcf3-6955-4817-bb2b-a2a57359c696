package yeepay

import (
	"github.com/yop-platform/yop-go-sdk/yop/client"
	"github.com/yop-platform/yop-go-sdk/yop/request"
)

type Client struct {
	priKey       *request.IsvPriKey
	yeePayConfig *Config
	client       client.YopClient
}

func NewClient(priKey *request.IsvPriKey, yeePayConfig *Config, client client.YopClient) *Client {
	return &Client{priKey: priKey, yeePayConfig: yeePayConfig, client: client}
}

func (c *Client) MerchantNo() string {
	return c.yeePayConfig.MerchantNo
}
