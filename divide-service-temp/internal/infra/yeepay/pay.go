package yeepay

import (
	"cnb.cool/cymirror/ces-services/common/server"
	"crypto"
	"github.com/bytedance/sonic"
	"github.com/shopspring/decimal"
	"github.com/yop-platform/yop-go-sdk/yop/constants"
	"github.com/yop-platform/yop-go-sdk/yop/request"
	"github.com/yop-platform/yop-go-sdk/yop/utils"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"time"
)

type tradeOrderPayerInfo struct {
	CardName string `json:"cardName"`         // 身份证姓名
	IDCardNo string `json:"idCardNo"`         // 身份证号
	UserID   string `json:"userID,omitempty"` // 易宝钱包侧用户id
}

func (t *tradeOrderPayerInfo) JSON() (string, error) {
	payerInfoJsonString, err := sonic.MarshalString(t)
	if err != nil {
		zap.L().Error("marshal trade order payer info failed", zap.Error(err))
		return "", server.InternalStatus
	}
	return payerInfoJsonString, nil
}

// tradeOrderResp 交易下单返回
type tradeOrderResp struct {
	Token         string `json:"token"`         // 交易下单返回的 token
	UniqueOrderNo string `json:"uniqueOrderNo"` // 易宝收款订单号
}

// TradeOrderResult 交易下单结果
type TradeOrderResult struct {
	Token         string // 交易下单返回的 token
	UniqueOrderNo string // 易宝收款订单号
}

// QuickTradeOrderCmd 易宝快捷支付交易下单 Command
type QuickTradeOrderCmd struct {
	OrderID      string          // 订单id
	OrderAmount  decimal.Decimal // 订单金额
	RedirectURL  string          // 重定向地址
	ExpiredTime  time.Time       // 订单过期时间
	GoodsName    string          // 商品名称
	RealName     string          // 真实姓名
	IDCardNo     string          // 身份证号
	IsNeedDivide bool            // 是否需要分账
}

// QuickTradeOrder 易宝快捷支付交易下单 https://open.yeepay.com/docs/apis/hb-installment/post__rest__v1.0__trade__order
func (c *Client) QuickTradeOrder(cmd *QuickTradeOrderCmd) (*TradeOrderResult, error) {
	payerInfo, err := (&tradeOrderPayerInfo{
		CardName: cmd.RealName,
		IDCardNo: cmd.IDCardNo,
	}).JSON()
	if err != nil {
		return nil, err
	}

	cfg := c.yeePayConfig
	var yopRequest = request.NewYopRequest(constants.POST_HTTP_METHOD, "/rest/v1.0/trade/order")
	yopRequest.AppId = cfg.AppKey
	yopRequest.AddParam("merchantNo", cfg.MerchantNo)                         // 商户号
	yopRequest.AddParam("orderId", cmd.OrderID)                               // 订单号
	yopRequest.AddParam("orderAmount", cmd.OrderAmount.String())              // 订单金额
	yopRequest.AddParam("redirectURL", cmd.RedirectURL)                       // 重定向地址
	yopRequest.AddParam("notifyUrl", cfg.PayNotifyURL)                        // 回调地址
	yopRequest.AddParam("goodsName", cmd.GoodsName)                           // 商品名字
	yopRequest.AddParam("expiredTime", cmd.ExpiredTime.Format(time.DateTime)) // 过期时间
	yopRequest.AddParam("payerInfo", payerInfo)                               // 支付人信息json
	if cmd.IsNeedDivide {
		yopRequest.AddParam("fundProcessType", "DELAY_SETTLE") // 分账类型
		yopRequest.AddParam("csUrl", cfg.CsNotifyURL)          // 清算回调
	}
	yopRequest.IsvPriKey = *c.priKey

	response, err := c.client.Request(yopRequest)
	if err != nil {
		zap.L().Error("yeepay trade order failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	var resp tradeOrderResp
	resultMap := response.Result.(map[string]any)
	resp.Token, _ = resultMap["token"].(string)

	zap.L().Info("yeepay quick trade order result", zap.Any("cmd", cmd), zap.ByteString("resp", response.Content), zap.Any("req", yopRequest.Params))

	return &TradeOrderResult{Token: resp.Token}, nil
}

// WalletTradeOrderCmd 易宝钱包支付交易下单 Command
type WalletTradeOrderCmd struct {
	OrderID      string          // 订单id
	OrderAmount  decimal.Decimal // 订单金额
	RedirectURL  string          // 重定向地址
	ExpiredTime  time.Time       // 订单过期时间
	GoodsName    string          // 商品名称
	RealName     string          // 真实姓名
	IDCardNo     string          // 身份证号
	YeepayUserID string          // 易宝钱包侧用户id
	IsNeedDivide bool            // 是否需要分账
}

// WalletTradeOrder 易宝钱包支付交易下单 https://open.yeepay.com/docs/apis/hb-installment/post__rest__v1.0__trade__order
func (c *Client) WalletTradeOrder(cmd *WalletTradeOrderCmd) (*TradeOrderResult, error) {
	payerInfo, err := (&tradeOrderPayerInfo{
		CardName: cmd.RealName,
		IDCardNo: cmd.IDCardNo,
		UserID:   cmd.YeepayUserID,
	}).JSON()
	if err != nil {
		return nil, err
	}

	cfg := c.yeePayConfig
	var yopRequest = request.NewYopRequest(constants.POST_HTTP_METHOD, "/rest/v1.0/trade/order")
	yopRequest.AppId = cfg.AppKey
	yopRequest.AddParam("merchantNo", cfg.MerchantNo)                         // 商户号
	yopRequest.AddParam("orderId", cmd.OrderID)                               // 订单号
	yopRequest.AddParam("orderAmount", cmd.OrderAmount.String())              // 订单金额
	yopRequest.AddParam("redirectURL", cmd.RedirectURL)                       // 重定向地址
	yopRequest.AddParam("notifyUrl", cfg.PayNotifyURL)                        // 回调地址
	yopRequest.AddParam("goodsName", cmd.GoodsName)                           // 商品名字
	yopRequest.AddParam("expiredTime", cmd.ExpiredTime.Format(time.DateTime)) // 过期时间
	yopRequest.AddParam("payerInfo", payerInfo)                               // 支付人信息json
	if cmd.IsNeedDivide {
		yopRequest.AddParam("fundProcessType", "DELAY_SETTLE") // 分账类型
		yopRequest.AddParam("csUrl", cfg.CsNotifyURL)          // 清算回调
	}
	yopRequest.IsvPriKey = *c.priKey

	response, err := c.client.Request(yopRequest)
	if err != nil {
		zap.L().Error("yeepay trade order failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	var resp tradeOrderResp
	resultMap := response.Result.(map[string]any)
	resp.Token, _ = resultMap["token"].(string)
	resp.UniqueOrderNo, _ = resultMap["uniqueOrderNo"].(string)

	zap.L().Info("yeepay wallet trade order result", zap.Any("cmd", cmd), zap.ByteString("resp", response.Content), zap.Any("req", yopRequest.Params))

	return &TradeOrderResult{Token: resp.Token}, nil
}

// AggregatePayCmd 聚合支付 Command
type AggregatePayCmd struct {
	Token        string // 交易下单返回的 token
	UserIP       string // 用户 IP 地址
	RedirectURL  string // 重定向地址
	IsNeedDivide bool   // 是否需要分账
}

// aggregatePayResp 聚合支付请求返回结果 https://open.yeepay.com/docs/apis/bzshsfk/options__rest__v1.0__aggpay__pre-pay
type aggregatePayResp struct {
	UniqueOrderNo string `json:"uniqueOrderNo"` // 易宝收款订单号
	OrderId       string `json:"orderId"`       // 商户收款请求号
	PrePayTn      string `json:"prePayTn"`      // 预支付标识信息
}

// AggregatePayResult 聚合支付结果
type AggregatePayResult struct {
	UniqueOrderNo string // 易宝收款订单号
	OrderId       string // 商户收款请求号
	PrePayTn      string // 预支付标识信息
}

// AggregatePay 聚合支付
func (c *Client) AggregatePay(req *AggregatePayCmd) (*AggregatePayResult, error) {
	cfg := c.yeePayConfig
	// https://open.yeepay.com/docs/apis/bzshsfk/options__rest__v1.0__aggpay__pre-pay
	var yopRequest = request.NewYopRequest(constants.POST_HTTP_METHOD, "/rest/v1.0/aggpay/pre-pay")
	yopRequest.AppId = cfg.AppKey
	yopRequest.AddParam("token", req.Token)
	yopRequest.AddParam("payWay", "USER_SCAN")          // USER_SCAN 用户扫码支付
	yopRequest.AddParam("channel", "ALIPAY")            // ALIPAY 支付宝
	yopRequest.AddParam("scene", "OFFLINE")             // 添加场景参数，设置为 "OFFLINE" (线下)
	yopRequest.AddParam("userIp", req.UserIP)           // 添加用户 IP 地址
	yopRequest.AddParam("redirectURL", req.RedirectURL) // 添加重定向地址
	yopRequest.AddParam("notifyURL", cfg.PayNotifyURL)  // 添加异步通知 URL
	if req.IsNeedDivide {
		yopRequest.AddParam("csUrl", cfg.CsNotifyURL) // 清算回调

	}
	yopRequest.IsvPriKey = *c.priKey

	response, err := c.client.Request(yopRequest)
	if err != nil {
		zap.L().Error("yeepay aggregate pay failed", zap.Error(err))
		return nil, err
	}
	var resp aggregatePayResp
	resultMap := response.Result.(map[string]any)
	resp.PrePayTn, _ = resultMap["prePayTn"].(string)
	resp.UniqueOrderNo, _ = resultMap["uniqueOrderNo"].(string)
	resp.OrderId, _ = resultMap["orderId"].(string)

	zap.L().Info("yeepay aggregate pay result", zap.Any("cmd", req), zap.Any("resp", response.Content))

	return &AggregatePayResult{
		UniqueOrderNo: resp.UniqueOrderNo,
		OrderId:       resp.OrderId,
		PrePayTn:      resp.PrePayTn,
	}, nil
}

// BuildPayURLCmd 构建支付 URL Command
type BuildPayURLCmd struct {
	Token    string // 交易下单返回的 token
	UserID   string // 用户 ID
	MemberID string // 易宝钱包侧用户id 钱包支付时必填
}

type buildPayURLExt struct {
	WalletMemberNo string `json:"walletMemberNo"` // 易宝钱包侧用户id 钱包支付时必填
	LimitPayType   string `json:"limitPayType"`   // 限时支付类型 WALLET_PAY 钱包支付
}

func (b *buildPayURLExt) JSON() (string, error) {
	payerInfoJsonString, err := sonic.MarshalString(b)
	if err != nil {
		zap.L().Error("marshal build pay url ext failed", zap.Error(err))
		return "", server.InternalStatus
	}
	return payerInfoJsonString, nil
}

// BuildPayURL 构建收银台支付 URL
// 参考文档1: https://open.yeepay.com/docs/products/bzshsfk/others/5f4ca4f830b67d001bd7b21e
// 参考文档2: https://open.yeepay.com/docs/products/e-bank/others/5eaaa1d1ee8fe8001a2f62ea/5eaaa1dbf77dfa001a91f81e
func (c *Client) BuildPayURL(cmd *BuildPayURLCmd) (string, error) {
	cfg := c.yeePayConfig
	// Build parameters string
	paramBuilder := strings.Builder{}
	paramBuilder.WriteString("appKey=")
	paramBuilder.WriteString(cfg.AppKey)
	paramBuilder.WriteString("&merchantNo=")
	paramBuilder.WriteString(cfg.MerchantNo)
	paramBuilder.WriteString("&token=")
	paramBuilder.WriteString(cmd.Token)
	paramBuilder.WriteString("&timestamp=")
	paramBuilder.WriteString(strconv.FormatInt(time.Now().Unix(), 10))
	paramBuilder.WriteString("&directPayType=")
	paramBuilder.WriteString("&cardType=")
	paramBuilder.WriteString("&userNo=")
	paramBuilder.WriteString(strings.ReplaceAll(cmd.UserID, "-", ""))
	paramBuilder.WriteString("&userType=USER_ID")
	// ext 钱包支付时候必填
	paramBuilder.WriteString("&ext=")
	if cmd.MemberID != "" {
		extJsonString, err := (&buildPayURLExt{WalletMemberNo: cmd.MemberID, LimitPayType: "WALLET_PAY"}).JSON()
		if err != nil {
			return "", err
		}
		paramBuilder.WriteString(extJsonString)
	}

	queryStr := paramBuilder.String()

	// Get signature
	sign, err := utils.RsaSignBase64(queryStr, c.priKey.Value, crypto.SHA256)
	if err != nil {
		zap.L().Error("yeepay build pay url failed", zap.Error(err))
		return "", server.InternalStatus
	}

	// Build final URL
	urlBuilder := strings.Builder{}
	urlBuilder.WriteString(cfg.PayUrlPrefix)
	urlBuilder.WriteString("?sign=")
	urlBuilder.WriteString(sign)
	urlBuilder.WriteString("$")      // Java SDK 源码 yop-java-sdk-biz-3.2.25-jdk18json.jar!\com\yeepay\g3\sdk\yop\utils\DigitalEnvelopeUtils.java:87
	urlBuilder.WriteString("SHA256") // Java SDK 源码 yop-java-sdk-biz-3.2.25-jdk18json.jar!\com\yeepay\g3\sdk\yop\utils\DigitalEnvelopeUtils.java:87
	urlBuilder.WriteString("&")
	urlBuilder.WriteString(queryStr)

	return urlBuilder.String(), nil
}

// WalletTradePayCmd 钱包交易支付 Command
type WalletTradePayCmd struct {
	Token        string // 交易下单返回的 token
	UserIP       string // 用户 IP 地址
	RedirectURL  string // 重定向地址
	IsNeedDivide bool   // 是否需要分账
	UserID       string // 商户用户ID
	PayAmount    string // 支付金额
	ProductName  string // 商品名称
	OrderID      string // 订单id
}

// walletTradePayResp 钱包交易支付返回结果
type walletTradePayResp struct {
	UniqueOrderNo string `json:"uniqueOrderNo"` // 易宝收款订单号
	CashierUrl    string `json:"cashierUrl"`    // 收银台地址
}

// WalletTradePayResult 钱包交易支付结果
type WalletTradePayResult struct {
	UniqueOrderNo string // 易宝收款订单号
	CashierUrl    string // 收银台地址
}

// WalletTradePay 钱包交易支付 https://open.yeepay.com/docs/apis/fwssfk/post__rest__v1.0__m-wallet__trade__order
func (c *Client) WalletTradePay(cmd *WalletTradePayCmd) (*WalletTradePayResult, error) {
	cfg := c.yeePayConfig

	var yopRequest = request.NewYopRequest(constants.POST_HTTP_METHOD, "/rest/v1.0/m-wallet/trade/order")
	yopRequest.AppId = cfg.AppKey

	yopRequest.AddParam("requestNo", cmd.OrderID)                                  // 必填 商户请求流水号
	yopRequest.AddParam("parentMerchantNo", cfg.MerchantNo)                        // 必填 用户归属的平台商编号
	yopRequest.AddParam("merchantNo", cfg.MerchantNo)                              // 必填 商户编号（收单商户）
	yopRequest.AddParam("merchantUserNo", strings.ReplaceAll(cmd.UserID, "-", "")) // 必填 商户用户ID
	yopRequest.AddParam("payAmount", cmd.PayAmount)                                // 必填
	yopRequest.AddParam("notifyUrl", cfg.PayNotifyURL)                             // 必填
	yopRequest.AddParam("returnUrl", cmd.RedirectURL)                              // 必填
	yopRequest.AddParam("productName", cmd.ProductName)                            // 必填
	if cmd.IsNeedDivide {
		yopRequest.AddParam("fundProcessType", "DELAY_SETTLE") // 分账类型
	}
	yopRequest.IsvPriKey = *c.priKey

	response, err := c.client.Request(yopRequest)
	if err != nil {
		zap.L().Error("yeepay wallet trade pay failed", zap.Error(err))
		return nil, err
	}

	var resp walletTradePayResp
	resultMap := response.Result.(map[string]any)
	resp.UniqueOrderNo, _ = resultMap["uniqueOrderNo"].(string)
	resp.CashierUrl, _ = resultMap["cashierUrl"].(string)

	zap.L().Info("yeepay wallet trade pay result", zap.Any("cmd", cmd), zap.Any("resp", response.Content))

	return &WalletTradePayResult{
		UniqueOrderNo: resp.UniqueOrderNo,
		CashierUrl:    resp.CashierUrl,
	}, nil
}
