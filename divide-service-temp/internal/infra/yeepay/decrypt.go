package yeepay

import (
	"cnb.cool/cymirror/ces-services/common/server"
	"github.com/yop-platform/yop-go-sdk/yop/utils"
	"go.uber.org/zap"
	"net/url"
)

func (c *Client) Decrypt(cipher string) (string, error) {
	values, err := url.ParseQuery(cipher)
	if err != nil {
		zap.L().Error("parse response query failed", zap.Error(err), zap.String("cipher", cipher))
		return "", server.InternalStatus
	}
	cipher = values.Get("response")
	if cipher == "" {
		zap.L().Error("response parameter is empty", zap.String("original_cipher", cipher))
		return "", server.InternalStatus
	}

	cfg := c.yeePayConfig
	content, err := utils.DecryptCallback(cfg.YeepayPublic<PERSON>ey, c.priKey.Value, cipher)
	if err != nil {
		zap.L().Error("yeepay decrypt failed", zap.Error(err), zap.String("cipher", cipher))
		return "", server.InternalStatus
	}
	return content, nil
}
