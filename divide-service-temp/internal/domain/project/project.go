package project

import (
	projectpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/project"
	"github.com/shopspring/decimal"
)

type Project struct {
	ID     string          // 项目ID
	name   string          // 项目名称
	price  decimal.Decimal // 价格
	canBuy bool            // 是否可以购买
	imgURL string          // 图片地址
}

func NewProjectFromCanBuyProjectRPCResp(resp *projectpb.CanBuyProjectResp) *Project {
	price, _ := decimal.NewFromString(resp.Price)
	return &Project{
		ID:     resp.Id,
		name:   resp.Name,
		price:  price,
		canBuy: resp.CanBuy,
		imgURL: resp.HeroImageUrl,
	}
}

func (p *Project) Name() string {
	return p.name
}

func (p *Project) Price() decimal.Decimal {
	return p.price
}

func (p *Project) CanBuy() bool {
	return p.canBuy
}

func (p *Project) ImgURL() string {
	return p.imgURL
}

type ProjectWithAmount struct {
	ProjectID string
	Amount    decimal.NullDecimal
}
