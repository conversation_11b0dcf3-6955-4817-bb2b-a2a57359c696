package pay

import "cnb.cool/cymirror/ces-services/divide-service/internal/domain/order"

var (
	// YeepayDivideStatusNoDivide 不分账
	YeepayDivideStatusNoDivide = YeepayDivideStatus{value: 1}
	// YeepayDivideStatusWaitDivide 待分账
	YeepayDivideStatusWaitDivide = YeepayDivideStatus{value: 2}
	// YeepayDivideStatusSuccess 分账成功
	YeepayDivideStatusSuccess = YeepayDivideStatus{value: 3}
	// YeepayDivideStatusFailed 分账失败
	YeepayDivideStatusFailed = YeepayDivideStatus{value: 4}
	// YeepayDivideStatusDividing 分账中
	YeepayDivideStatusDividing = YeepayDivideStatus{value: 5}
)

type YeepayDivideStatus struct {
	value int16
}

func NewYeepayDivideStatusFromOrderType(orderType order.Type) YeepayDivideStatus {
	switch orderType {
	case order.TypePrimaryMarket:
		return YeepayDivideStatusNoDivide
	case order.TypeSecondaryMarket:
		return YeepayDivideStatusWaitDivide
	default:
		return YeepayDivideStatusNoDivide
	}
}

func NewYeepayDivideStatus(value int16) YeepayDivideStatus {
	switch value {
	case 1:
		return YeepayDivideStatusNoDivide
	case 2:
		return YeepayDivideStatusWaitDivide
	case 3:
		return YeepayDivideStatusSuccess
	case 4:
		return YeepayDivideStatusFailed
	case 5:
		return YeepayDivideStatusDividing

	default:
		return YeepayDivideStatusNoDivide
	}
}

func (d YeepayDivideStatus) Int16() int16 {
	return d.value
}
