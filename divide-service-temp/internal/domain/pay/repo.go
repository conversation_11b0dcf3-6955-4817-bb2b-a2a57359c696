package pay

import (
	"context"
)

type Repo interface {
	// CreateYeepayPaymentInfo 创建易宝支付信息
	CreateYeepayPaymentInfo(ctx context.Context, order *Yeepay) error
	// ApplyYeepayOrderPaySuccess 设置易宝支付订单支付时间，易宝支付流水号
	ApplyYeepayOrderPaySuccess(ctx context.Context, yeepayOrder *Yeepay) error
	// GetYeepayOrderByOrderID 根据订单ID获取易宝支付订单
	GetYeepayOrderByOrderID(ctx context.Context, orderId string) (*Yeepay, error)
	// UpdateYeepayOrder 更新易宝支付订单
	UpdateYeepayOrder(ctx context.Context, o *Yeepay) error
}
