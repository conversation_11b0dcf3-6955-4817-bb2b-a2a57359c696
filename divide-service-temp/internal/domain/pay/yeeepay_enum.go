package pay

import "cnb.cool/cymirror/ces-services/divide-service/internal/domain/order"

var (
	// TypeUnknown 未知
	TypeUnknown = YeepayPayWay{0}
	// TypeYeePayQuick 易宝快捷支付
	TypeYeePayQuick = YeepayPayWay{1}
	// TypeYeePayWallet 易宝钱包支付
	TypeYeePayWallet = YeepayPayWay{2}
)

type YeepayPayWay struct {
	value int16
}

func NewYeepayPayWay(value int16) YeepayPayWay {
	switch value {
	case 1:
		return TypeYeePayQuick
	case 2:
		return TypeYeePayWallet
	default:
		return TypeUnknown

	}
}

func (y YeepayPayWay) Int16() int16 {
	return y.value
}

var (
	// DivideTypeRealTime 不需要分账
	DivideTypeRealTime = YeepayDivideType{1}
	// DivideTypeDelaySettle 需要分账
	DivideTypeDelaySettle = YeepayDivideType{2}
)

type YeepayDivideType struct {
	value int16
}

func NewYeepayDivideType(value int16) YeepayDivideType {
	switch value {
	case 1:
		return DivideTypeRealTime
	case 2:
		return DivideTypeDelaySettle
	default:
		return DivideTypeRealTime
	}
}

func NewYeepayDivideTypeFromOrderType(orderType order.Type) YeepayDivideType {
	switch orderType {
	case order.TypePrimaryMarket:
		return DivideTypeRealTime
	case order.TypeSecondaryMarket:
		return DivideTypeDelaySettle
	case order.TypePurchaseRequest:
		return DivideTypeDelaySettle
	default:
		return DivideTypeDelaySettle
	}
}

func (y YeepayDivideType) Int16() int16 {
	return y.value
}

func (y YeepayDivideType) Equal(t YeepayDivideType) bool {
	return y.value == t.value
}
