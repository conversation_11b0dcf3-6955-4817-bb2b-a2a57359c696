package pay

import (
	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/order"
	"github.com/shopspring/decimal"
	"gorm.io/plugin/optimisticlock"
	"time"
)

type Yeepay struct {
	ID            string                 // id
	userID        string                 // 平台用户id
	orderID       string                 // 订单id
	yeepayOrderNo string                 // 第三方订单号
	payAmount     decimal.Decimal        // 交易金额
	merchantFee   *decimal.Decimal       // 商户手续费
	unSplitAmount *decimal.Decimal       // 剩余可分账金额
	csAt          *time.Time             // 清算时间
	payAt         *time.Time             // 支付时间
	payURL        string                 // 支付链接
	payWay        YeepayPayWay           // 易宝支付方式
	divideType    YeepayDivideType       // 分账类型
	createdAt     time.Time              // 创建时间
	updatedAt     time.Time              // 更新时间
	version       optimisticlock.Version // 乐观锁
	divideStatus  YeepayDivideStatus     // 分账状态
}

func NewYeepayFromModel(first *model.PaymentPayYeepay) *Yeepay {
	return &Yeepay{
		ID:            first.ID,
		userID:        first.UserID,
		orderID:       first.OrderID,
		yeepayOrderNo: first.YeepayOrderNo,
		payAmount:     first.PayAmount,
		merchantFee:   first.MerchantFee,
		unSplitAmount: first.UnSplitAmount,
		csAt:          first.CsAt,
		payAt:         first.PayAt,
		payURL:        first.PayURL,
		payWay:        NewYeepayPayWay(first.PayWay),
		divideType:    NewYeepayDivideType(first.DivideType),
		createdAt:     first.CreatedAt,
		updatedAt:     first.UpdatedAt,
		version:       first.Version,
		divideStatus:  NewYeepayDivideStatus(first.DivideStatus),
	}
}

func (y *Yeepay) ToModel() *model.PaymentPayYeepay {
	return &model.PaymentPayYeepay{
		ID:            y.ID,
		UserID:        y.userID,
		OrderID:       y.orderID,
		YeepayOrderNo: y.yeepayOrderNo,
		PayAmount:     y.payAmount,
		MerchantFee:   y.merchantFee,
		UnSplitAmount: y.unSplitAmount,
		CsAt:          y.csAt,
		PayAt:         y.payAt,
		PayURL:        y.payURL,
		PayWay:        y.payWay.Int16(),
		DivideType:    y.divideType.Int16(),
		Version:       y.version,
		DivideStatus:  y.divideStatus.Int16(),
	}
}

func NewYeepay(o *order.Order, yeepayOrderNo, payURL string,
	payWay YeepayPayWay, divideType YeepayDivideType) *Yeepay {
	return &Yeepay{
		userID:        o.UserID(),
		orderID:       o.ID,
		yeepayOrderNo: yeepayOrderNo,
		payAmount:     o.Price(),
		payURL:        payURL,
		payWay:        payWay,
		divideType:    divideType,
	}
}

func (y *Yeepay) UserID() string {
	return y.userID
}

func (y *Yeepay) OrderID() string {
	return y.orderID
}

func (y *Yeepay) YeepayOrderNo() string {
	return y.yeepayOrderNo
}

func (y *Yeepay) PayAmount() decimal.Decimal {
	return y.payAmount
}

func (y *Yeepay) MerchantFee() *decimal.Decimal {
	return y.merchantFee
}

func (y *Yeepay) UnSplitAmount() *decimal.Decimal {
	return y.unSplitAmount
}

func (y *Yeepay) CsAt() *time.Time {
	return y.csAt
}

func (y *Yeepay) PayAt() *time.Time {
	return y.payAt
}

func (y *Yeepay) PayURL() string {
	return y.payURL
}

func (y *Yeepay) PayWay() YeepayPayWay {
	return y.payWay
}

func (y *Yeepay) DivideType() YeepayDivideType {
	return y.divideType
}

func (y *Yeepay) CreatedAt() time.Time {
	return y.createdAt
}

func (y *Yeepay) UpdatedAt() time.Time {
	return y.updatedAt
}

func (y *Yeepay) SetUserID(userID string) {
	y.userID = userID
}

func (y *Yeepay) SetOrderID(orderID string) {
	y.orderID = orderID
}

func (y *Yeepay) SetYeepayOrderNo(yeepayOrderNo string) {
	y.yeepayOrderNo = yeepayOrderNo
}

func (y *Yeepay) SetPayAmount(payAmount decimal.Decimal) {
	y.payAmount = payAmount
}

func (y *Yeepay) SetMerchantFee(merchantFee decimal.Decimal) {
	y.merchantFee = &merchantFee
}

func (y *Yeepay) SetUnSplitAmount(unSplitAmount decimal.Decimal) {
	y.unSplitAmount = &unSplitAmount
}

func (y *Yeepay) SetCsAt(csAt time.Time) {
	y.csAt = &csAt
}

func (y *Yeepay) SetPayAt(payAt time.Time) {
	y.payAt = &payAt
}

func (y *Yeepay) SetPayURL(payURL string) {
	y.payURL = payURL
}

func (y *Yeepay) SetPayWay(payWay YeepayPayWay) {
	y.payWay = payWay
}

func (y *Yeepay) SetDivideType(divideType YeepayDivideType) {
	y.divideType = divideType
}

func (y *Yeepay) SetCreatedAt(createdAt time.Time) {
	y.createdAt = createdAt
}

func (y *Yeepay) SetUpdatedAt(updatedAt time.Time) {
	y.updatedAt = updatedAt
}

func (y *Yeepay) SetDivideStatus(status YeepayDivideStatus) {
	y.divideStatus = status
}
