package refund

import (
	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/plugin/optimisticlock"
	"time"
)

type Refund struct {
	ID           string                 // id
	orderID      string                 // 订单id
	refundAmount decimal.Decimal        // 退款金额
	status       Status                 // 状态
	createdAt    time.Time              // 创建时间
	updatedAt    time.Time              // 更新时间
	version      optimisticlock.Version // 乐观锁
	remark       string                 // 备注
	refundReqNo  string                 // 退款请求号
}

func (r *Refund) ToModel() *model.PaymentPayYeepayRefund {
	return &model.PaymentPayYeepayRefund{
		ID:           r.ID,
		OrderID:      r.orderID,
		RefundAmount: r.refundAmount,
		Status:       r.status.Int16(),
		CreatedAt:    r.createdAt,
		UpdatedAt:    r.updatedAt,
		DeletedAt:    gorm.DeletedAt{},
		Version:      r.version,
		Remark:       r.remark,
		RefundReqNo:  r.refundReqNo,
	}
}

func NewRefund(orderID string, refundAmount decimal.Decimal, remark string, refundReqNo string) *Refund {
	return &Refund{
		ID:           "",
		orderID:      orderID,
		refundAmount: refundAmount,
		status:       StatusProcessing,
		createdAt:    time.Time{},
		updatedAt:    time.Time{},
		version:      optimisticlock.Version{},
		remark:       remark,
		refundReqNo:  refundReqNo,
	}
}
