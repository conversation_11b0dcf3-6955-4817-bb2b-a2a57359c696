package refund

import "cnb.cool/cymirror/ces-services/divide-service/internal/infra/yeepay"

var (
	// StatusProcessing 处理中
	StatusProcessing = Status{value: 1}
	// StatusSuccess 成功
	StatusSuccess = Status{value: 2}
	// StatusFail 失败
	StatusFail = Status{value: 3}
)

type Status struct {
	value int16
}

func (s Status) Int16() int16 {
	return s.value
}

func NewStatusFromYeepayResult(result *yeepay.RefundResult) Status {
	switch result.Status {
	case "SUCCESS":
		return StatusSuccess
	case "FAILED":
		return StatusFail
	default:
		return StatusProcessing
	}
}
