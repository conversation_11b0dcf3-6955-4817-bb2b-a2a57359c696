package divide

import "github.com/shopspring/decimal"

type Service struct {
}

func NewService() *Service {
	return &Service{}
}

func (s *Service) GetPrimaryUserSideDivideAmount(primaryExtraDivides []*Secondary, price decimal.Decimal) decimal.Decimal {
	// 默认用户收入分成比例 95%
	userRevenueShare := decimal.NewFromInt32(95).Div(decimal.NewFromInt32(100))
	for _, d := range primaryExtraDivides {
		userRevenueShare = userRevenueShare.Sub(d.RevenueShare())
	}

	if userRevenueShare.LessThanOrEqual(decimal.Zero) {
		return decimal.Zero
	}
	return price.Mul(userRevenueShare).RoundDown(2)
}
