package divide

import (
	"cnb.cool/cymirror/ces-services/common/server"
	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/pay"
	"cnb.cool/cymirror/ces-services/divide-service/internal/infra/yeepay"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/plugin/optimisticlock"
	"time"
)

var (
	// MemberTypeProjectSide 项目方
	MemberTypeProjectSide = MemberType{value: 1}
	// MemberTypeSellerSide 卖家用户
	MemberTypeSellerSide = MemberType{value: 2}
)

type MemberType struct {
	value int16
}

func (m MemberType) Int16() int16 {
	return m.value
}

func NewMemberTypeFromInt16(value int16) MemberType {
	switch value {
	case 1:
		return MemberTypeProjectSide
	case 2:
		return MemberTypeSellerSide
	default:
		return MemberTypeProjectSide
	}
}

func (m MemberType) ToYeepayMemberType() yeepay.DivideType {
	switch m {
	case MemberTypeProjectSide:
		return yeepay.DivideTypeMerchant2Merchant
	case MemberTypeSellerSide:
		return yeepay.DivideTypeMerchant2Member
	default:
		return yeepay.DivideTypeMerchant2Merchant
	}
}

type YeepayDivide struct {
	ID                    string                 // id
	userID                string                 // 分账易宝用户 Member对象 的 id；若是商户本身时，传入0；
	memberType            MemberType             // 商户类型[1:项目方, 2:卖家用户]；
	requestNo             string                 // 分账流水号,幂等；
	orderID               string                 // 订单id；
	amount                decimal.Decimal        // 分账金额；
	createdAt             time.Time              // 创建时间；
	updatedAt             time.Time              // 更新时间；
	version               optimisticlock.Version // 乐观锁；
	divideStatus          pay.YeepayDivideStatus // 分账状态
	orderSecondaryExtraID string                 // 订单二级额外信息表id
}

func NewProjectSideYeepayDivide(requestNo, orderID string, amount decimal.Decimal) (*YeepayDivide, error) {
	if amount.Exponent() < -2 {
		zap.L().Error("amount is invalid", zap.String("amount", amount.String()), zap.Int32("exponent", amount.Exponent()))
		return nil, server.InternalStatus
	}
	return &YeepayDivide{
		userID:     "0",
		memberType: MemberTypeProjectSide,
		requestNo:  requestNo,
		orderID:    orderID,
		amount:     amount,
	}, nil
}

func NewMemberSideYeepayDivide(memberID, requestNo, orderID string, amount decimal.Decimal, orderSecondaryExtraID string) (*YeepayDivide, error) {
	if amount.Exponent() < -2 {
		zap.L().Error("amount is too large", zap.String("amount", amount.String()), zap.Int32("exponent", amount.Exponent()))
		return nil, server.InternalStatus
	}
	return &YeepayDivide{
		userID:                memberID,
		memberType:            MemberTypeSellerSide,
		requestNo:             requestNo,
		orderID:               orderID,
		amount:                amount,
		orderSecondaryExtraID: orderSecondaryExtraID,
	}, nil
}

func (d *YeepayDivide) ToModel() *model.PaymentPayYeepayDivide {
	return &model.PaymentPayYeepayDivide{
		ID:                    d.ID,
		MemberID:              d.userID,
		MemberType:            d.memberType.Int16(),
		RequestNo:             d.requestNo,
		OrderID:               d.orderID,
		Amount:                d.amount,
		CreatedAt:             d.createdAt,
		UpdatedAt:             d.updatedAt,
		Version:               d.version,
		OrderSecondaryExtraID: d.orderSecondaryExtraID,
	}
}

func (d *YeepayDivide) UserID() string {
	return d.userID
}

func (d *YeepayDivide) MemberType() MemberType {
	return d.memberType
}

func (d *YeepayDivide) RequestNo() string {
	return d.requestNo
}

func (d *YeepayDivide) OrderID() string {
	return d.orderID
}

func (d *YeepayDivide) Amount() decimal.Decimal {
	return d.amount
}

func (d *YeepayDivide) CreatedAt() time.Time {
	return d.createdAt
}

func (d *YeepayDivide) UpdatedAt() time.Time {
	return d.updatedAt
}

func (d *YeepayDivide) Version() optimisticlock.Version {
	return d.version
}

func (d *YeepayDivide) DivideStatus() pay.YeepayDivideStatus {
	return d.divideStatus
}

func (d *YeepayDivide) OrderSecondaryExtraID() string {
	return d.orderSecondaryExtraID
}

func (d *YeepayDivide) SetMemberID(memberID string) {
	d.userID = memberID
}

func (d *YeepayDivide) SetMemberType(memberType MemberType) {
	d.memberType = memberType
}

func (d *YeepayDivide) SetRequestNo(requestNo string) {
	d.requestNo = requestNo
}

func (d *YeepayDivide) SetOrderID(orderID string) {
	d.orderID = orderID
}

func (d *YeepayDivide) SetAmount(amount decimal.Decimal) {
	d.amount = amount
}

func (d *YeepayDivide) SetDivideStatus(status pay.YeepayDivideStatus) {
	d.divideStatus = status
}

func (d *YeepayDivide) SetOrderSecondaryExtraID(orderSecondaryExtraID string) {
	d.orderSecondaryExtraID = orderSecondaryExtraID
}

func (d *YeepayDivide) ToYeepayDivideInfra(merchantNo string) *yeepay.Divide {
	divide := &yeepay.Divide{
		Amount:     d.amount.Copy(),
		LedgerNo:   "",
		LedgerType: yeepay.DivideType{},
	}
	if d.memberType == MemberTypeSellerSide {
		divide.LedgerType = yeepay.DivideTypeMerchant2Member
		divide.LedgerNo = d.userID
	} else {
		divide.LedgerType = yeepay.DivideTypeMerchant2Merchant
		divide.LedgerNo = merchantNo
	}
	return divide
}

func NewDivideFromModel(m *model.PaymentPayYeepayDivide) *YeepayDivide {
	return &YeepayDivide{
		ID:                    m.ID,
		userID:                m.MemberID,
		memberType:            NewMemberTypeFromInt16(m.MemberType),
		requestNo:             m.RequestNo,
		orderID:               m.OrderID,
		amount:                m.Amount,
		createdAt:             m.CreatedAt,
		updatedAt:             m.UpdatedAt,
		version:               m.Version,
		orderSecondaryExtraID: m.OrderSecondaryExtraID,
	}
}
