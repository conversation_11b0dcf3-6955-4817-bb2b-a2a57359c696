package wallet

import (
	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
	"github.com/google/uuid"
	"strings"
	"time"
)

type YeePay struct {
	ID             string    // id
	userID         string    // 用户id
	memberID       *string   // 易宝钱包用户id
	name           string    // 姓名
	idCard         string    // 身份证号
	createdTime    time.Time // 创建时间
	updatedTime    time.Time // 更新时间
	businessNo     string    // 易宝唯一订单号
	externalUserID string    // 元镜userID
}

func NewEmptyYeePayWallet(userID string, name string, idCard string) *YeePay {
	return &YeePay{
		userID:         userID,
		name:           name,
		idCard:         idCard,
		externalUserID: strings.ReplaceAll(uuid.New().String(), "-", ""),
	}
}

func NewYeePayWallet(businessNo string, merchantUserNo string, walletUserNo string) *YeePay {
	return &YeePay{
		businessNo:     businessNo,
		externalUserID: merchantUserNo,
		memberID:       &walletUserNo,
	}
}

func NewYeePayWalletFromModel(model *model.PaymentUserYeepayWallet) *YeePay {
	if model == nil {
		return nil
	}
	return &YeePay{
		ID:             model.ID,
		userID:         model.UserID,
		memberID:       model.MemberID,
		name:           model.Name,
		idCard:         model.IDCard,
		createdTime:    model.CreatedTime,
		updatedTime:    model.UpdatedTime,
		externalUserID: model.ExternalUserID,
	}
}

func (y *YeePay) ToModel() *model.PaymentUserYeepayWallet {
	return &model.PaymentUserYeepayWallet{
		ID:             y.ID,
		UserID:         y.userID,
		MemberID:       y.memberID,
		Name:           y.name,
		IDCard:         y.idCard,
		CreatedTime:    y.createdTime,
		UpdatedTime:    y.updatedTime,
		ExternalUserID: y.externalUserID,
	}
}

func (y *YeePay) UserID() string {
	return y.userID
}

func (y *YeePay) MemberID() *string {
	return y.memberID
}

func (y *YeePay) Name() string {
	return y.name
}

func (y *YeePay) IDCard() string {
	return y.idCard
}

func (y *YeePay) CreatedTime() time.Time {
	return y.createdTime
}

func (y *YeePay) UpdatedTime() time.Time {
	return y.updatedTime
}

func (y *YeePay) BusinessNo() string {
	return y.businessNo
}

func (y *YeePay) ExternalUserID() string {
	return y.externalUserID
}

func (y *YeePay) SetUserID(userID string) {
	y.userID = userID
}

func (y *YeePay) SetMemberID(memberID string) {
	y.memberID = &memberID
}

func (y *YeePay) SetName(name string) {
	y.name = name
}

func (y *YeePay) SetIDCard(idCard string) {
	y.idCard = idCard
}

func (y *YeePay) SetCreatedTime(createdTime time.Time) {
	y.createdTime = createdTime
}

func (y *YeePay) SetUpdatedTime(updatedTime time.Time) {
	y.updatedTime = updatedTime
}

func (y *YeePay) SetBusinessNo(businessNo string) {
	y.businessNo = businessNo
}

func (y *YeePay) IsOpen() bool {
	if y == nil {
		return false
	}
	return y.memberID != nil
}

func (y *YeePay) SetExternalUserID(externalUserID string) {
	y.externalUserID = externalUserID
}
