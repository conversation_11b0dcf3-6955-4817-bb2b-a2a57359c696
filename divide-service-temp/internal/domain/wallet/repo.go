package wallet

import (
	"context"
)

type Repo interface {
	// GetYeePayWalletByUserID 根据用户 ID 获取 Yeepay 钱包
	GetYeePayWalletByUserID(ctx context.Context, id string) (*YeePay, error)
	// BatchGetYeePayWalletsByUserIDs 批量根据用户ID获取Yeepay钱包
	BatchGetYeePayWalletsByUserIDs(ctx context.Context, ids []string) (map[string]*YeePay, error)
	// InsertYeePayWallet 插入 Yeepay 钱包
	InsertYeePayWallet(ctx context.Context, wallet *YeePay) error
	// ApplyWalletCallback 处理 Yeepay 钱包回调
	ApplyWalletCallback(ctx context.Context, wallet *YeePay) error
	// GetYeePayWalletByUserIDs 根据用户 IDs 获取 Yeepay 钱包
	GetYeePayWalletByUserIDs(ctx context.Context, ids []string) ([]*YeePay, error)
}
