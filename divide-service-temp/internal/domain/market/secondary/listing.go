package secondary

import (
	marketpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/market"
	nftpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/nft"
	"github.com/shopspring/decimal"
)

type Listing struct {
	ID        string // 市场在售id
	nftID     string // nft id
	projectID string // 项目id
	price     decimal.Decimal
	sellerID  string // 卖家id
	imgURL    string // 图片url
	canBuy    bool   // 是否可以购买
	name      string // 名称
}

func NewListingFromCanBuyListingRPC(listing *marketpb.CanBuyListingRPCResp) *Listing {
	decimalPrice, _ := decimal.NewFromString(listing.Price)
	return &Listing{
		ID:        listing.Id,
		nftID:     listing.NftID,
		projectID: listing.ProjectID,
		price:     decimalPrice,
		sellerID:  listing.SellerID,
		imgURL:    listing.ImgURL,
		canBuy:    listing.CanBuy,
		name:      listing.Name,
	}
}

func NewListingFromGetListingsOrderByPriceAndLockRPC(listing *nftpb.GetListingsOrderByPriceAndLockRPCResp_Listing) *Listing {
	decimalPrice, _ := decimal.NewFromString(listing.Price)
	return &Listing{
		ID:        listing.Id,
		nftID:     listing.NftID,
		projectID: listing.ProjectID,
		price:     decimalPrice,
		sellerID:  listing.SellerID,
		imgURL:    listing.ImgURL,
		canBuy:    true,
		name:      listing.Name,
	}
}

func (l *Listing) NftID() string {
	return l.nftID
}

func (l *Listing) ProjectID() string {
	return l.projectID
}

func (l *Listing) Price() decimal.Decimal {
	return l.price
}

func (l *Listing) SellerID() string {
	return l.sellerID
}

func (l *Listing) ImgURL() string {
	return l.imgURL
}

func (l *Listing) CanBuy(userID string) bool {
	return l.canBuy && l.sellerID != userID
}

func (l *Listing) Name() string {
	return l.name
}
