package order

import (
	"time"

	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/market/secondary"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/plugin/optimisticlock"
)

type SecondaryExtra struct {
	ID        string          // id
	orderID   string          // 订单id
	projectID string          // 项目id
	nftID     string          // nft id
	userID    string          // 用户id
	price     decimal.Decimal // 价格
	listingID string          // 市场在售id

	createdAt time.Time // 创建时间
	updatedAt time.Time // 更新时间
	version   int64     // 版本
	sellerID  string    // 卖家id
}

func (e *SecondaryExtra) ToModel() *model.PaymentOrderSecondary {
	return &model.PaymentOrderSecondary{
		ID:        e.ID,
		OrderID:   e.orderID,
		UserID:    e.userID,
		ProjectID: e.projectID,
		CreatedAt: e.createdAt,
		UpdatedAt: e.updatedAt,
		Version:   optimisticlock.Version{Int64: e.version, Valid: true},
		NftID:     e.nftID,
		Price:     e.price,
		ListingID: e.listingID,
		SellerID:  e.sellerID,
	}
}

func NewSecondaryExtraFromModel(e *model.PaymentOrderSecondary) *SecondaryExtra {
	return &SecondaryExtra{
		ID:        e.ID,
		orderID:   e.OrderID,
		projectID: e.ProjectID,
		nftID:     e.NftID,
		userID:    e.UserID,
		price:     e.Price,
		listingID: e.ListingID,
		createdAt: e.CreatedAt,
		updatedAt: e.UpdatedAt,
		version:   e.Version.Int64,
		sellerID:  e.SellerID,
	}
}

type SecondaryMarket struct {
	*Order
	extra []*SecondaryExtra
}

func NewBaseSecondaryMarket(userID string) *SecondaryMarket {
	orderID, _ := uuid.NewV7()

	o := &SecondaryMarket{
		Order: &Order{
			ID:          orderID.String(),
			status:      StatusReduceStock,
			orderNo:     generateOrderNo(userID, orderID.String(), TypeSecondaryMarket),
			userID:      userID,
			orderType:   TypeSecondaryMarket,
			title:       "",
			expiredTime: time.Now().Add(time.Minute * 3), // TODO: 动态过期时间
			imgURL:      "",
			price:       decimal.Decimal{},
		},
	}
	return o
}

func NewSecondaryMarket(userID string, listing *secondary.Listing) *SecondaryMarket {
	orderID, _ := uuid.NewV7()

	o := &SecondaryMarket{
		Order: &Order{
			ID:          orderID.String(),
			status:      StatusReduceStock,
			orderNo:     generateOrderNo(userID, orderID.String(), TypeSecondaryMarket),
			userID:      userID,
			orderType:   TypeSecondaryMarket,
			title:       listing.Name(),
			expiredTime: time.Now().Add(time.Minute * 3), // TODO: 动态过期时间
			imgURL:      "",
			price:       decimal.Decimal{},
		},
	}
	o.SetOrderInfo(listing.Name(), listing.ImgURL())
	o.SetPrice(listing.Price())
	return o
}

func (s *SecondaryMarket) SetSecondaryExtra(l *secondary.Listing) {
	s.extra = append(s.extra, &SecondaryExtra{
		orderID:   s.ID,
		projectID: l.ProjectID(),
		nftID:     l.NftID(),
		userID:    s.userID,
		price:     l.Price(),
		listingID: l.ID,
		sellerID:  l.SellerID(),
	})
}

func (s *SecondaryMarket) SetSecondaryExtras(listings []*secondary.Listing) {
	for _, l := range listings {
		s.extra = append(s.extra, &SecondaryExtra{
			orderID:   s.ID,
			projectID: l.ProjectID(),
			nftID:     l.NftID(),
			userID:    s.userID,
			price:     l.Price(),
			listingID: l.ID,
			sellerID:  l.SellerID(),
		})
	}
}

func (s *SecondaryMarket) Extra() []*SecondaryExtra {
	return s.extra
}
