package order

import (
	"fmt"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"strings"
	"time"

	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
	"github.com/shopspring/decimal"
	"gorm.io/plugin/optimisticlock"
)

type Order struct {
	ID          string          // 订单id
	status      Status          // 订单状态
	orderNo     string          // 订单号
	userID      string          // 用户id
	orderType   Type            // 订单类型
	title       string          // 订单标题
	createdAt   time.Time       // 创建时间
	updatedAt   time.Time       // 更新时间
	version     int64           // 版本
	payType     PayType         // 支付类型
	payTime     *time.Time      // 支付时间
	expiredTime time.Time       // 过期时间
	imgURL      string          // 图片链接
	price       decimal.Decimal // 价格
	payID       *string         // 对应支付表的id
}

func NewFromModel(orderModel *model.PaymentOrder) *Order {
	if orderModel == nil {
		return nil
	}
	return &Order{
		ID:          orderModel.ID,
		status:      NewStatus(orderModel.Status),
		orderNo:     orderModel.OrderNo,
		userID:      orderModel.UserID,
		orderType:   NewType(orderModel.Type),
		title:       orderModel.Title,
		createdAt:   orderModel.CreatedAt,
		updatedAt:   orderModel.UpdatedAt,
		version:     orderModel.Version.Int64,
		payType:     NewPayType(orderModel.PayType),
		payTime:     orderModel.PayTime,
		expiredTime: orderModel.ExpiredTime,
		imgURL:      orderModel.ImgURL,
		price:       orderModel.Price,
		payID:       orderModel.PayID,
	}
}

func (o *Order) ToModel() *model.PaymentOrder {
	if o == nil {
		return nil
	}
	return &model.PaymentOrder{
		ID:          o.ID,
		Status:      o.status.Int16(),
		OrderNo:     o.orderNo,
		UserID:      o.userID,
		Type:        o.orderType.Int16(),
		Title:       o.title,
		Version:     optimisticlock.Version{Int64: o.version, Valid: true},
		PayType:     o.payType.Int16(),
		PayTime:     o.payTime,
		ExpiredTime: o.expiredTime,
		ImgURL:      o.imgURL,
		Price:       o.price,
		PayID:       o.payID,
	}
}

func (o *Order) SetOrderInfo(
	title string,
	imgURL string,
) {
	o.title = title
	o.imgURL = imgURL
}

func (o *Order) IsExpired() bool {
	return o.expiredTime.Before(time.Now())
}

func (o *Order) CanCancel() error {
	if o == nil {
		return status.Error(codes.InvalidArgument, "order not found")
	}
	if !o.Status().Equal(StatusPaying) && !o.Status().Equal(StatusWaitPay) {
		return status.Error(codes.InvalidArgument, "order status can not cancel")
	}
	return nil
}

func (o *Order) CanRefund() bool {
	if o == nil {
		return false
	}
	if !o.Status().Equal(StatusSuccess) {
		return false
	}
	return true
}

func generateOrderNo(userID, orderID string, orderType Type) string {
	return fmt.Sprintf("%s%s%s%d", orderType.OrderNoPrefix(),
		strings.ToUpper(userID[:8]),
		strings.ToUpper(orderID[:8]),
		time.Now().Unix())
}

type OrderWithSecondaryExtra struct {
	Order  *Order
	Extras []*SecondaryExtra
}
