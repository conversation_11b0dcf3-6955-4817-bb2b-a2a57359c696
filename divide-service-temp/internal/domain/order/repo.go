package order

import (
	"context"
	"time"

	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
	"github.com/shopspring/decimal"

	"cnb.cool/cymirror/ces-services/divide-service/internal/domain/project"
)

type Repo interface {
	// CreateOrder 创建订单
	CreateOrder(ctx context.Context, order *Order) (*Order, error)
	// UpdateOrderStatus 更新订单状态
	UpdateOrderStatus(ctx context.Context, order *Order) error
	// UpdateOrder 更新订单
	UpdateOrder(ctx context.Context, order *Order) error

	// CreatePrimaryMarketOrderExtra 创建一级市场订单额外信息
	CreatePrimaryMarketOrderExtra(ctx context.Context, po *PrimaryMarket) error
	// CreateSecondaryMarketOrderExtra 创建二级市场订单额外信息
	CreateSecondaryMarketOrderExtra(ctx context.Context, order *SecondaryMarket) error

	// GetPrimaryOrderByID 根据extra id获取一级市场订单
	GetPrimaryOrderByID(ctx context.Context, id string) (*PrimaryExtra, error)
	// GetSecondaryOrderByID 根据extra id获取二级市场订单
	GetSecondaryOrderByID(ctx context.Context, id string) (*SecondaryExtra, error)

	// GetOrderByIDWithUserID 根据订单id获取订单
	GetOrderByIDWithUserID(ctx context.Context, id string, userID string) (*Order, error)
	// GetOrderByID 根据订单id获取订单
	GetOrderByID(ctx context.Context, id string) (*Order, error)
	// GetOrderRank 根据时间范围获取二级市场成交量排行
	GetOrderRank(ctx context.Context, startTime, endTime int64, limit int) (map[string]int64, error)
	// GetSecondaryOrderByNFTIDWithStatus 根据nft id获取对应status的二级市场订单
	GetSecondaryOrderByNFTIDWithStatus(ctx context.Context, page, pageSize int32, sortF string, isAsc bool, nftID string, status Status) ([]*SecondaryExtra, int64, error)
	// GetSecondaryOrderByProjectIDWithStatus 根据项目id获取对应status的二级市场订单
	GetSecondaryOrderByProjectIDWithStatus(ctx context.Context, page, pageSize int32, sortF string, isAsc bool, projectID string, status Status) ([]*SecondaryExtra, int64, error)
	// GetOrderNFTIDs 根据订单类型获取nft ids
	GetOrderNFTIDs(ctx context.Context, domainOrder *Order) ([]string, error)
	// GetPrimaryOrdersByOrderIDs 根据主orderID批量查询一级市场订单
	GetPrimaryOrdersByOrderIDs(ctx context.Context, ids []string) ([]*model.PaymentOrderPrimary, error)
	// GetSecondaryOrdersByOrderIDs 根据主orderID批量查询二级市场订单
	GetSecondaryOrdersByOrderIDs(ctx context.Context, ids []string) ([]*model.PaymentOrderSecondary, error)
	// GetOrderProjectID 根据订单ID获取项目ID
	GetOrderProjectID(ctx context.Context, o *Order) (string, error)
	// GetOrderListingIDs 根据订单ID获取二级市场在售IDs
	GetOrderListingIDs(ctx context.Context, o *Order) ([]string, error)
	// GetYeepayPayURL 根据订单ID和用户ID获取未支付的易宝支付链接
	GetYeepayPayURL(ctx context.Context, orderID string, userID string) (string, error)

	// GetPrimaryOrderListByUserID 根据用户id获取一级市场订单额外信息列表
	GetPrimaryOrderListByUserID(ctx context.Context, userID string, page int64, pageSize int64) ([]*model.PaymentOrderPrimary, int64, error)
	// GetSecondaryOrderListByUserID 根据用户id获取二级市场订单额外信息列表(作为买家)
	GetSecondaryOrderListByUserID(ctx context.Context, userID string, page int64, pageSize int64) ([]*model.PaymentOrderSecondary, int64, error)
	// GetSecondaryOrderListAsSellerWithStatus 根据用户id获取二级市场订单额外信息列表(作为卖家)
	GetSecondaryOrderListAsSellerWithStatus(ctx context.Context, page, pageSize int32, userID string, status Status) ([]*SecondaryExtra, int64, error)
	// GetOrdersByIDs 根据订单ID批量查询订单
	GetOrdersByIDs(ctx context.Context, ids []string) ([]*Order, error)
	// GetOrdersByIDsWithSort 根据订单ID批量查询订单，并且遵循传入时的顺序返回
	GetOrdersByIDsWithSort(ctx context.Context, ids []string) ([]*Order, error)
	// GetOrdersByIDsAndStatus 根据订单ID与状态批量查询订单
	GetOrdersByIDsAndStatus(ctx context.Context, ids []string, status Status, page int64, pageSize int64) ([]*Order, int64, error)
	// GetOrdersByIDsAndStatusBySort 根据订单ID与状态批量查询订单并sort
	GetOrdersByIDsAndStatusBySort(ctx context.Context, ids []string, status Status, page int64, pageSize int64, sortF string, isAsc bool) ([]*Order, int64, error)
	// GetOrderByOrderNo 根据订单号获取订单
	GetOrderByOrderNo(ctx context.Context, orderNo string) (*Order, error)
	// GetUniqueOrderIDsByNftID 根据nftID获取订单ID
	GetUniqueOrderIDsByNftID(ctx context.Context, nftID string, sortF string, isAsc bool, page, pageSize int64) ([]string, int64, error)
	// GetUniqueOrderIDsByNftIDWithCriteria 根据nftID和条件获取订单ID
	GetUniqueOrderIDsByNftIDWithCriteria(ctx context.Context, nftID string, criteria *SecondaryOrderCriteria) ([]string, int64, error)
	// GetOrdersByCriteria 根据条件查询订单
	GetOrdersByCriteria(ctx context.Context, criteria *OrderCriteria) ([]*Order, int64, error)

	// GetOrdersByUserID 通过uid查询订单
	GetOrdersByUserID(ctx context.Context, userID string, page int64, pageSize int64) ([]*Order, int64, error)
	// GetSecondaryExtraByOrderID 根据订单ID批量查询二级市场订单额外信息
	GetSecondaryExtraByOrderID(ctx context.Context, orderID string) ([]*SecondaryExtra, error)
	// GetSecondaryExtraByOrderIDs 根据订单IDs批量查询二级市场订单额外信息
	GetSecondaryExtraByOrderIDs(ctx context.Context, orderIDs []string) ([]*SecondaryExtra, error)

	// ApplyPayNotify 应用支付通知,更新订单状态和支付时间
	ApplyPayNotify(ctx context.Context, order *Order) error
	// CancelOrder 取消订单
	CancelOrder(ctx context.Context, order *Order) error
	// GetUnsuccessfulOrdersByUserIDAndTime 根据用户ID和时间范围获取未支付成功且未取消的订单
	GetUnsuccessfulOrdersByUserIDAndTime(ctx context.Context, userID string, startTime, endTime time.Time) ([]*Order, error)

	// GetAllTradeAmounts 获得二级市场总交易额
	GetAllTradeAmounts(ctx context.Context) (decimal.Decimal, error)
	// GetTradeAmountsRangeByTime 获得二级市场指定时间内的交易额
	GetTradeAmountsRangeByTime(ctx context.Context, startTime, endTime time.Time) (decimal.Decimal, error)
	// GetProjectTradeAmounts 获得指定项目二级市场总交易额
	GetProjectTradeAmounts(ctx context.Context, projectID string) (decimal.Decimal, error)
	// GetProjectTradeAmountsRangeByTime 获得指定项目二级市场指定时间内的交易额
	GetProjectTradeAmountsRangeByTime(ctx context.Context, projectID string, startTime, endTime time.Time) (decimal.Decimal, error)
	// GetProjectsTradeAmountsRangeByTime 获得多个项目二级市场总交易额
	GetProjectsTradeAmountsRangeByTime(ctx context.Context, projectIDs []string, startTime, endTime time.Time, isAsc bool) ([]*project.ProjectWithAmount, error)
	// GetOrdersByListingIDs 根据listingID批量查询订单
	GetOrdersByListingIDs(ctx context.Context, listingIDs []string) (map[string]*Order, error)
	// GetOrderAmountByUserID 根据用户ID获取订单总金额
	GetOrderAmountByUserID(ctx context.Context, userID string, startTime, endTime int64) (decimal.Decimal, error)
	// BatchGetOrderAmountByUserID 根据用户ID批量获取订单总金额
	BatchGetOrderAmountByUserID(ctx context.Context, userIDs []string, startTime, endTime int64) (map[string]decimal.Decimal, error)

	// GetPrimaryOrdersByUserIDAndProjectID 根据用户ID和项目ID获取一级市场订单
	GetPrimaryOrdersByUserIDAndProjectID(ctx context.Context, userID string, projectID string) ([]*model.PaymentOrderPrimary, error)

	// GetOrdersByTime 根据用户ID获取订单ID
	GetOrdersByTime(ctx context.Context, startTime int64, endTime int64) ([]*Order, error)
	// GetPrimaryExtraByOrderID 根据订单ID获取一级市场订单额外信息
	GetPrimaryExtraByOrderID(ctx context.Context, orderID string) ([]*PrimaryExtra, error)

	// GetSecondaryOrderByOrderID 根据订单ID获取二级市场订单
	GetSecondaryOrderByOrderID(ctx context.Context, orderIDs []string) ([]*SecondaryExtra, error)
}
