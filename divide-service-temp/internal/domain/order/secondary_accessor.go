package order

import (
	"github.com/shopspring/decimal"
	"time"
)

func (e *SecondaryExtra) OrderID() string {
	return e.orderID
}

func (e *SecondaryExtra) ProjectID() string {
	return e.projectID
}

func (e *SecondaryExtra) NftID() string {
	return e.nftID
}

func (e *SecondaryExtra) UserID() string {
	return e.userID
}

func (e *SecondaryExtra) Price() decimal.Decimal {
	return e.price
}

func (e *SecondaryExtra) ListingID() string {
	return e.listingID
}

func (e *SecondaryExtra) CreatedAt() time.Time {
	return e.createdAt
}

func (e *SecondaryExtra) UpdatedAt() time.Time {
	return e.updatedAt
}

func (e *SecondaryExtra) Version() int64 {
	return e.version
}

func (e *SecondaryExtra) SellerID() string {
	return e.sellerID
}
