package order

import (
	nftpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/nft"
	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
)

var (
	// StatusReduceStock 扣减库存
	StatusReduceStock = Status{value: 1}
	// StatusWaitPay 待支付
	StatusWaitPay = Status{value: 2}
	// StatusPaying 支付中
	StatusPaying = Status{value: 3}
	// StatusSuccess 成功
	StatusSuccess = Status{value: 4}
	// StatusFail 失败
	StatusFail = Status{value: 5}
	// StatusCancel 取消
	StatusCancel = Status{value: 6}
	// StatusTimeout 超时
	StatusTimeout = Status{value: 7}
)

type Status struct {
	value int16
}

func NewStatus(value int16) Status {
	switch value {
	case 1:
		return StatusReduceStock
	case 2:
		return StatusWaitPay
	case 3:
		return StatusPaying
	case 4:
		return StatusSuccess
	case 5:
		return StatusFail
	case 6:
		return StatusCancel
	case 7:
		return StatusTimeout
	default:
		return StatusReduceStock
	}
}

func (s Status) Int16() int16 {
	return s.value
}

func (s Status) Equal(status Status) bool {
	return s.value == status.value
}

var (
	// TypePrimaryMarket 一级市场购买订单
	TypePrimaryMarket = Type{value: 1}
	// TypeSecondaryMarket 二级市场购买订单
	TypeSecondaryMarket = Type{value: 2}
	// TypePurchaseRequest 求购订单
	TypePurchaseRequest = Type{value: 3}
)

type Type struct {
	value int16
}

func NewType(value int16) Type {
	switch value {
	case 1:
		return TypePrimaryMarket
	case 2:
		return TypeSecondaryMarket
	case 3:
		return TypePurchaseRequest
	default:
		return TypePrimaryMarket
	}
}

func (t Type) Int16() int16 {
	return t.value
}

func (t Type) OrderNoPrefix() string {
	switch t {
	case TypePrimaryMarket:
		return "PM"
	case TypeSecondaryMarket:
		return "SM"
	case TypePurchaseRequest:
		return "PR"
	default:
		return ""
	}
}

func (t Type) ToNFTPB() nftpb.MarketType {
	switch t {
	case TypePrimaryMarket:
		return nftpb.MarketType_MARKET_TYPE_PRIMARY
	case TypeSecondaryMarket:
		return nftpb.MarketType_MARKET_TYPE_SECONDARY
	default:
		return nftpb.MarketType_MARKET_TYPE_PRIMARY
	}
}

func (t Type) ToOrderPB() orderpb.OrderType {
	switch t {
	case TypePrimaryMarket:
		return orderpb.OrderType_PRIMARY_MARKET
	case TypeSecondaryMarket:
		return orderpb.OrderType_SECONDARY_MARKET
	case TypePurchaseRequest:
		return orderpb.OrderType_PURCHASE_REQ
	default:
		return orderpb.OrderType_PURCHASE_REQ
	}
}

func (t Type) Equal(orderType Type) bool {
	return t.value == orderType.value
}

var (
	// PayTypeUnknown 未知
	PayTypeUnknown = PayType{value: 0}
	// PayTypeYeePayQuick 易宝快捷
	PayTypeYeePayQuick = PayType{value: 1}
	// PayTypeYeePayWallet 易宝钱包
	PayTypeYeePayWallet = PayType{value: 2}
)

type PayType struct {
	value int16
}

func NewPayType(value int16) PayType {
	switch value {
	case 0:
		return PayTypeUnknown
	case 1:
		return PayTypeYeePayQuick
	case 2:
		return PayTypeYeePayWallet
	default:
		return PayTypeUnknown
	}
}

func (p PayType) Int16() int16 {
	return p.value
}

func (p PayType) ToOrderPB() orderpb.PayType {
	switch p {
	case PayTypeYeePayQuick:
		return orderpb.PayType_YEEPAY_QUICK
	case PayTypeYeePayWallet:
		return orderpb.PayType_YEEPAY_WALLET
	default:
		return orderpb.PayType_YEEPAY_QUICK
	}
}
