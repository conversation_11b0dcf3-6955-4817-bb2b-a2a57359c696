syntax = "proto3";

package ces.divide.divide;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

option go_package = "/dividepb";

service DivideService {
  rpc DivideByOrderID(DivideByOrderIDReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/divide/order"
      body: "*"
    };
  }
  rpc DivideMatchOrder(DivideMatchOrderReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/divide/match"
      body: "*"
    };
  }
}

message DivideByOrderIDReq {
  string orderID = 1;
  string password = 2;
  string token = 3;
}

message DivideMatchOrderReq {
  bytes content = 1;
  string password = 2;
  string token = 3;
}