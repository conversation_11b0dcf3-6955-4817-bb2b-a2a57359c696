// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: ces/divide/divide.proto

package dividepb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DivideByOrderIDReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderID       string                 `protobuf:"bytes,1,opt,name=orderID,proto3" json:"orderID,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	Token         string                 `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DivideByOrderIDReq) Reset() {
	*x = DivideByOrderIDReq{}
	mi := &file_ces_divide_divide_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DivideByOrderIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DivideByOrderIDReq) ProtoMessage() {}

func (x *DivideByOrderIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_divide_divide_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DivideByOrderIDReq.ProtoReflect.Descriptor instead.
func (*DivideByOrderIDReq) Descriptor() ([]byte, []int) {
	return file_ces_divide_divide_proto_rawDescGZIP(), []int{0}
}

func (x *DivideByOrderIDReq) GetOrderID() string {
	if x != nil {
		return x.OrderID
	}
	return ""
}

func (x *DivideByOrderIDReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *DivideByOrderIDReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type DivideMatchOrderReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       []byte                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	Token         string                 `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DivideMatchOrderReq) Reset() {
	*x = DivideMatchOrderReq{}
	mi := &file_ces_divide_divide_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DivideMatchOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DivideMatchOrderReq) ProtoMessage() {}

func (x *DivideMatchOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_divide_divide_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DivideMatchOrderReq.ProtoReflect.Descriptor instead.
func (*DivideMatchOrderReq) Descriptor() ([]byte, []int) {
	return file_ces_divide_divide_proto_rawDescGZIP(), []int{1}
}

func (x *DivideMatchOrderReq) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *DivideMatchOrderReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *DivideMatchOrderReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

var File_ces_divide_divide_proto protoreflect.FileDescriptor

const file_ces_divide_divide_proto_rawDesc = "" +
	"\n" +
	"\x17ces/divide/divide.proto\x12\x11ces.divide.divide\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a.protoc-gen-openapiv2/options/annotations.proto\"`\n" +
	"\x12DivideByOrderIDReq\x12\x18\n" +
	"\aorderID\x18\x01 \x01(\tR\aorderID\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x14\n" +
	"\x05token\x18\x03 \x01(\tR\x05token\"a\n" +
	"\x13DivideMatchOrderReq\x12\x18\n" +
	"\acontent\x18\x01 \x01(\fR\acontent\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x14\n" +
	"\x05token\x18\x03 \x01(\tR\x05token2\xef\x01\n" +
	"\rDivideService\x12m\n" +
	"\x0fDivideByOrderID\x12%.ces.divide.divide.DivideByOrderIDReq\x1a\x16.google.protobuf.Empty\"\x1b\x82\xd3\xe4\x93\x02\x15:\x01*\"\x10/v1/divide/order\x12o\n" +
	"\x10DivideMatchOrder\x12&.ces.divide.divide.DivideMatchOrderReq\x1a\x16.google.protobuf.Empty\"\x1b\x82\xd3\xe4\x93\x02\x15:\x01*\"\x10/v1/divide/matchB\vZ\t/dividepbb\x06proto3"

var (
	file_ces_divide_divide_proto_rawDescOnce sync.Once
	file_ces_divide_divide_proto_rawDescData []byte
)

func file_ces_divide_divide_proto_rawDescGZIP() []byte {
	file_ces_divide_divide_proto_rawDescOnce.Do(func() {
		file_ces_divide_divide_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_ces_divide_divide_proto_rawDesc), len(file_ces_divide_divide_proto_rawDesc)))
	})
	return file_ces_divide_divide_proto_rawDescData
}

var file_ces_divide_divide_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_ces_divide_divide_proto_goTypes = []any{
	(*DivideByOrderIDReq)(nil),  // 0: ces.divide.divide.DivideByOrderIDReq
	(*DivideMatchOrderReq)(nil), // 1: ces.divide.divide.DivideMatchOrderReq
	(*emptypb.Empty)(nil),       // 2: google.protobuf.Empty
}
var file_ces_divide_divide_proto_depIdxs = []int32{
	0, // 0: ces.divide.divide.DivideService.DivideByOrderID:input_type -> ces.divide.divide.DivideByOrderIDReq
	1, // 1: ces.divide.divide.DivideService.DivideMatchOrder:input_type -> ces.divide.divide.DivideMatchOrderReq
	2, // 2: ces.divide.divide.DivideService.DivideByOrderID:output_type -> google.protobuf.Empty
	2, // 3: ces.divide.divide.DivideService.DivideMatchOrder:output_type -> google.protobuf.Empty
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_ces_divide_divide_proto_init() }
func file_ces_divide_divide_proto_init() {
	if File_ces_divide_divide_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_ces_divide_divide_proto_rawDesc), len(file_ces_divide_divide_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_ces_divide_divide_proto_goTypes,
		DependencyIndexes: file_ces_divide_divide_proto_depIdxs,
		MessageInfos:      file_ces_divide_divide_proto_msgTypes,
	}.Build()
	File_ces_divide_divide_proto = out.File
	file_ces_divide_divide_proto_goTypes = nil
	file_ces_divide_divide_proto_depIdxs = nil
}
