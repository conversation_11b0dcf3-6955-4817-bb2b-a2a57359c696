// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: ces/divide/divide.proto

package dividepb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DivideService_DivideByOrderID_FullMethodName  = "/ces.divide.divide.DivideService/DivideByOrderID"
	DivideService_DivideMatchOrder_FullMethodName = "/ces.divide.divide.DivideService/DivideMatchOrder"
)

// DivideServiceClient is the client API for DivideService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DivideServiceClient interface {
	DivideByOrderID(ctx context.Context, in *DivideByOrderIDReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	DivideMatchOrder(ctx context.Context, in *DivideMatchOrderReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type divideServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDivideServiceClient(cc grpc.ClientConnInterface) DivideServiceClient {
	return &divideServiceClient{cc}
}

func (c *divideServiceClient) DivideByOrderID(ctx context.Context, in *DivideByOrderIDReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DivideService_DivideByOrderID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *divideServiceClient) DivideMatchOrder(ctx context.Context, in *DivideMatchOrderReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, DivideService_DivideMatchOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DivideServiceServer is the server API for DivideService service.
// All implementations should embed UnimplementedDivideServiceServer
// for forward compatibility.
type DivideServiceServer interface {
	DivideByOrderID(context.Context, *DivideByOrderIDReq) (*emptypb.Empty, error)
	DivideMatchOrder(context.Context, *DivideMatchOrderReq) (*emptypb.Empty, error)
}

// UnimplementedDivideServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDivideServiceServer struct{}

func (UnimplementedDivideServiceServer) DivideByOrderID(context.Context, *DivideByOrderIDReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DivideByOrderID not implemented")
}
func (UnimplementedDivideServiceServer) DivideMatchOrder(context.Context, *DivideMatchOrderReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DivideMatchOrder not implemented")
}
func (UnimplementedDivideServiceServer) testEmbeddedByValue() {}

// UnsafeDivideServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DivideServiceServer will
// result in compilation errors.
type UnsafeDivideServiceServer interface {
	mustEmbedUnimplementedDivideServiceServer()
}

func RegisterDivideServiceServer(s grpc.ServiceRegistrar, srv DivideServiceServer) {
	// If the following call pancis, it indicates UnimplementedDivideServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DivideService_ServiceDesc, srv)
}

func _DivideService_DivideByOrderID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DivideByOrderIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DivideServiceServer).DivideByOrderID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DivideService_DivideByOrderID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DivideServiceServer).DivideByOrderID(ctx, req.(*DivideByOrderIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DivideService_DivideMatchOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DivideMatchOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DivideServiceServer).DivideMatchOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DivideService_DivideMatchOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DivideServiceServer).DivideMatchOrder(ctx, req.(*DivideMatchOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DivideService_ServiceDesc is the grpc.ServiceDesc for DivideService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DivideService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ces.divide.divide.DivideService",
	HandlerType: (*DivideServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DivideByOrderID",
			Handler:    _DivideService_DivideByOrderID_Handler,
		},
		{
			MethodName: "DivideMatchOrder",
			Handler:    _DivideService_DivideMatchOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ces/divide/divide.proto",
}
