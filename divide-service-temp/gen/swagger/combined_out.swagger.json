{"swagger": "2.0", "info": {"title": "manage服务", "version": "1.0.0"}, "tags": [{"name": "DivideService"}], "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/divide/order": {"post": {"operationId": "DivideService_DivideByOrderID", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/divideDivideByOrderIDReq"}}], "tags": ["DivideService"]}}, "/v1/divide/match": {"post": {"operationId": "DivideService_DivideMatchOrder", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/divideDivideMatchOrderReq"}}], "tags": ["DivideService"]}}}, "definitions": {"divideDivideByOrderIDReq": {"type": "object", "properties": {"orderID": {"type": "string"}, "password": {"type": "string"}, "token": {"type": "string"}}}, "divideDivideMatchOrderReq": {"type": "object", "properties": {"content": {"type": "string", "format": "byte"}, "password": {"type": "string"}, "token": {"type": "string"}}}}, "securityDefinitions": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "security": [{"ApiKeyAuth": []}], "externalDocs": {"description": "注意：200 响应中不包含code msg 等统一返回值，仅代表data", "url": "none"}}