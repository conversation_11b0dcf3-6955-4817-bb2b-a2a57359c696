// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
)

func newPaymentUserYeepayWallet(db *gorm.DB, opts ...gen.DOOption) paymentUserYeepayWallet {
	_paymentUserYeepayWallet := paymentUserYeepayWallet{}

	_paymentUserYeepayWallet.paymentUserYeepayWalletDo.UseDB(db, opts...)
	_paymentUserYeepayWallet.paymentUserYeepayWalletDo.UseModel(&model.PaymentUserYeepayWallet{})

	tableName := _paymentUserYeepayWallet.paymentUserYeepayWalletDo.TableName()
	_paymentUserYeepayWallet.ALL = field.NewAsterisk(tableName)
	_paymentUserYeepayWallet.ID = field.NewString(tableName, "id")
	_paymentUserYeepayWallet.UserID = field.NewString(tableName, "user_id")
	_paymentUserYeepayWallet.MemberID = field.NewString(tableName, "member_id")
	_paymentUserYeepayWallet.Name = field.NewString(tableName, "name")
	_paymentUserYeepayWallet.IDCard = field.NewString(tableName, "id_card")
	_paymentUserYeepayWallet.CreatedTime = field.NewTime(tableName, "created_time")
	_paymentUserYeepayWallet.UpdatedTime = field.NewTime(tableName, "updated_time")
	_paymentUserYeepayWallet.ExternalUserID = field.NewString(tableName, "external_user_id")
	_paymentUserYeepayWallet.BusinessNo = field.NewString(tableName, "business_no")

	_paymentUserYeepayWallet.fillFieldMap()

	return _paymentUserYeepayWallet
}

type paymentUserYeepayWallet struct {
	paymentUserYeepayWalletDo paymentUserYeepayWalletDo

	ALL            field.Asterisk
	ID             field.String // 主键
	UserID         field.String // 用户id
	MemberID       field.String // 钱包账户id
	Name           field.String // 姓名
	IDCard         field.String // 身份证号
	CreatedTime    field.Time   // 创建时间
	UpdatedTime    field.Time   // 更新时间
	ExternalUserID field.String // 元镜用户id
	BusinessNo     field.String // 易宝唯一订单号

	fieldMap map[string]field.Expr
}

func (p paymentUserYeepayWallet) Table(newTableName string) *paymentUserYeepayWallet {
	p.paymentUserYeepayWalletDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p paymentUserYeepayWallet) As(alias string) *paymentUserYeepayWallet {
	p.paymentUserYeepayWalletDo.DO = *(p.paymentUserYeepayWalletDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *paymentUserYeepayWallet) updateTableName(table string) *paymentUserYeepayWallet {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.UserID = field.NewString(table, "user_id")
	p.MemberID = field.NewString(table, "member_id")
	p.Name = field.NewString(table, "name")
	p.IDCard = field.NewString(table, "id_card")
	p.CreatedTime = field.NewTime(table, "created_time")
	p.UpdatedTime = field.NewTime(table, "updated_time")
	p.ExternalUserID = field.NewString(table, "external_user_id")
	p.BusinessNo = field.NewString(table, "business_no")

	p.fillFieldMap()

	return p
}

func (p *paymentUserYeepayWallet) WithContext(ctx context.Context) IPaymentUserYeepayWalletDo {
	return p.paymentUserYeepayWalletDo.WithContext(ctx)
}

func (p paymentUserYeepayWallet) TableName() string { return p.paymentUserYeepayWalletDo.TableName() }

func (p paymentUserYeepayWallet) Alias() string { return p.paymentUserYeepayWalletDo.Alias() }

func (p paymentUserYeepayWallet) Columns(cols ...field.Expr) gen.Columns {
	return p.paymentUserYeepayWalletDo.Columns(cols...)
}

func (p *paymentUserYeepayWallet) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *paymentUserYeepayWallet) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 9)
	p.fieldMap["id"] = p.ID
	p.fieldMap["user_id"] = p.UserID
	p.fieldMap["member_id"] = p.MemberID
	p.fieldMap["name"] = p.Name
	p.fieldMap["id_card"] = p.IDCard
	p.fieldMap["created_time"] = p.CreatedTime
	p.fieldMap["updated_time"] = p.UpdatedTime
	p.fieldMap["external_user_id"] = p.ExternalUserID
	p.fieldMap["business_no"] = p.BusinessNo
}

func (p paymentUserYeepayWallet) clone(db *gorm.DB) paymentUserYeepayWallet {
	p.paymentUserYeepayWalletDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p paymentUserYeepayWallet) replaceDB(db *gorm.DB) paymentUserYeepayWallet {
	p.paymentUserYeepayWalletDo.ReplaceDB(db)
	return p
}

type paymentUserYeepayWalletDo struct{ gen.DO }

type IPaymentUserYeepayWalletDo interface {
	gen.SubQuery
	Debug() IPaymentUserYeepayWalletDo
	WithContext(ctx context.Context) IPaymentUserYeepayWalletDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPaymentUserYeepayWalletDo
	WriteDB() IPaymentUserYeepayWalletDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPaymentUserYeepayWalletDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPaymentUserYeepayWalletDo
	Not(conds ...gen.Condition) IPaymentUserYeepayWalletDo
	Or(conds ...gen.Condition) IPaymentUserYeepayWalletDo
	Select(conds ...field.Expr) IPaymentUserYeepayWalletDo
	Where(conds ...gen.Condition) IPaymentUserYeepayWalletDo
	Order(conds ...field.Expr) IPaymentUserYeepayWalletDo
	Distinct(cols ...field.Expr) IPaymentUserYeepayWalletDo
	Omit(cols ...field.Expr) IPaymentUserYeepayWalletDo
	Join(table schema.Tabler, on ...field.Expr) IPaymentUserYeepayWalletDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentUserYeepayWalletDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPaymentUserYeepayWalletDo
	Group(cols ...field.Expr) IPaymentUserYeepayWalletDo
	Having(conds ...gen.Condition) IPaymentUserYeepayWalletDo
	Limit(limit int) IPaymentUserYeepayWalletDo
	Offset(offset int) IPaymentUserYeepayWalletDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentUserYeepayWalletDo
	Unscoped() IPaymentUserYeepayWalletDo
	Create(values ...*model.PaymentUserYeepayWallet) error
	CreateInBatches(values []*model.PaymentUserYeepayWallet, batchSize int) error
	Save(values ...*model.PaymentUserYeepayWallet) error
	First() (*model.PaymentUserYeepayWallet, error)
	Take() (*model.PaymentUserYeepayWallet, error)
	Last() (*model.PaymentUserYeepayWallet, error)
	Find() ([]*model.PaymentUserYeepayWallet, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentUserYeepayWallet, err error)
	FindInBatches(result *[]*model.PaymentUserYeepayWallet, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PaymentUserYeepayWallet) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPaymentUserYeepayWalletDo
	Assign(attrs ...field.AssignExpr) IPaymentUserYeepayWalletDo
	Joins(fields ...field.RelationField) IPaymentUserYeepayWalletDo
	Preload(fields ...field.RelationField) IPaymentUserYeepayWalletDo
	FirstOrInit() (*model.PaymentUserYeepayWallet, error)
	FirstOrCreate() (*model.PaymentUserYeepayWallet, error)
	FindByPage(offset int, limit int) (result []*model.PaymentUserYeepayWallet, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPaymentUserYeepayWalletDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p paymentUserYeepayWalletDo) Debug() IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Debug())
}

func (p paymentUserYeepayWalletDo) WithContext(ctx context.Context) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p paymentUserYeepayWalletDo) ReadDB() IPaymentUserYeepayWalletDo {
	return p.Clauses(dbresolver.Read)
}

func (p paymentUserYeepayWalletDo) WriteDB() IPaymentUserYeepayWalletDo {
	return p.Clauses(dbresolver.Write)
}

func (p paymentUserYeepayWalletDo) Session(config *gorm.Session) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Session(config))
}

func (p paymentUserYeepayWalletDo) Clauses(conds ...clause.Expression) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p paymentUserYeepayWalletDo) Returning(value interface{}, columns ...string) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p paymentUserYeepayWalletDo) Not(conds ...gen.Condition) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p paymentUserYeepayWalletDo) Or(conds ...gen.Condition) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p paymentUserYeepayWalletDo) Select(conds ...field.Expr) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p paymentUserYeepayWalletDo) Where(conds ...gen.Condition) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p paymentUserYeepayWalletDo) Order(conds ...field.Expr) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p paymentUserYeepayWalletDo) Distinct(cols ...field.Expr) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p paymentUserYeepayWalletDo) Omit(cols ...field.Expr) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p paymentUserYeepayWalletDo) Join(table schema.Tabler, on ...field.Expr) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p paymentUserYeepayWalletDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p paymentUserYeepayWalletDo) RightJoin(table schema.Tabler, on ...field.Expr) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p paymentUserYeepayWalletDo) Group(cols ...field.Expr) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p paymentUserYeepayWalletDo) Having(conds ...gen.Condition) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p paymentUserYeepayWalletDo) Limit(limit int) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p paymentUserYeepayWalletDo) Offset(offset int) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p paymentUserYeepayWalletDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p paymentUserYeepayWalletDo) Unscoped() IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Unscoped())
}

func (p paymentUserYeepayWalletDo) Create(values ...*model.PaymentUserYeepayWallet) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p paymentUserYeepayWalletDo) CreateInBatches(values []*model.PaymentUserYeepayWallet, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p paymentUserYeepayWalletDo) Save(values ...*model.PaymentUserYeepayWallet) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p paymentUserYeepayWalletDo) First() (*model.PaymentUserYeepayWallet, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentUserYeepayWallet), nil
	}
}

func (p paymentUserYeepayWalletDo) Take() (*model.PaymentUserYeepayWallet, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentUserYeepayWallet), nil
	}
}

func (p paymentUserYeepayWalletDo) Last() (*model.PaymentUserYeepayWallet, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentUserYeepayWallet), nil
	}
}

func (p paymentUserYeepayWalletDo) Find() ([]*model.PaymentUserYeepayWallet, error) {
	result, err := p.DO.Find()
	return result.([]*model.PaymentUserYeepayWallet), err
}

func (p paymentUserYeepayWalletDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentUserYeepayWallet, err error) {
	buf := make([]*model.PaymentUserYeepayWallet, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p paymentUserYeepayWalletDo) FindInBatches(result *[]*model.PaymentUserYeepayWallet, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p paymentUserYeepayWalletDo) Attrs(attrs ...field.AssignExpr) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p paymentUserYeepayWalletDo) Assign(attrs ...field.AssignExpr) IPaymentUserYeepayWalletDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p paymentUserYeepayWalletDo) Joins(fields ...field.RelationField) IPaymentUserYeepayWalletDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p paymentUserYeepayWalletDo) Preload(fields ...field.RelationField) IPaymentUserYeepayWalletDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p paymentUserYeepayWalletDo) FirstOrInit() (*model.PaymentUserYeepayWallet, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentUserYeepayWallet), nil
	}
}

func (p paymentUserYeepayWalletDo) FirstOrCreate() (*model.PaymentUserYeepayWallet, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentUserYeepayWallet), nil
	}
}

func (p paymentUserYeepayWalletDo) FindByPage(offset int, limit int) (result []*model.PaymentUserYeepayWallet, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p paymentUserYeepayWalletDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p paymentUserYeepayWalletDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p paymentUserYeepayWalletDo) Delete(models ...*model.PaymentUserYeepayWallet) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *paymentUserYeepayWalletDo) withDO(do gen.Dao) *paymentUserYeepayWalletDo {
	p.DO = *do.(*gen.DO)
	return p
}
