// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
)

func newPaymentPayYeepayRefund(db *gorm.DB, opts ...gen.DOOption) paymentPayYeepayRefund {
	_paymentPayYeepayRefund := paymentPayYeepayRefund{}

	_paymentPayYeepayRefund.paymentPayYeepayRefundDo.UseDB(db, opts...)
	_paymentPayYeepayRefund.paymentPayYeepayRefundDo.UseModel(&model.PaymentPayYeepayRefund{})

	tableName := _paymentPayYeepayRefund.paymentPayYeepayRefundDo.TableName()
	_paymentPayYeepayRefund.ALL = field.NewAsterisk(tableName)
	_paymentPayYeepayRefund.ID = field.NewString(tableName, "id")
	_paymentPayYeepayRefund.OrderID = field.NewString(tableName, "order_id")
	_paymentPayYeepayRefund.RefundAmount = field.NewField(tableName, "refund_amount")
	_paymentPayYeepayRefund.Status = field.NewInt16(tableName, "status")
	_paymentPayYeepayRefund.CreatedAt = field.NewTime(tableName, "created_at")
	_paymentPayYeepayRefund.UpdatedAt = field.NewTime(tableName, "updated_at")
	_paymentPayYeepayRefund.DeletedAt = field.NewField(tableName, "deleted_at")
	_paymentPayYeepayRefund.Version = field.NewField(tableName, "version")
	_paymentPayYeepayRefund.Remark = field.NewString(tableName, "remark")
	_paymentPayYeepayRefund.RefundReqNo = field.NewString(tableName, "refund_req_no")

	_paymentPayYeepayRefund.fillFieldMap()

	return _paymentPayYeepayRefund
}

type paymentPayYeepayRefund struct {
	paymentPayYeepayRefundDo paymentPayYeepayRefundDo

	ALL          field.Asterisk
	ID           field.String // id
	OrderID      field.String // 订单id
	RefundAmount field.Field  // 退款金额
	Status       field.Int16  // 状态
	CreatedAt    field.Time   // 创建时间
	UpdatedAt    field.Time   // 更新时间
	DeletedAt    field.Field  // 软删除
	Version      field.Field  // 乐观锁
	Remark       field.String // 备注
	RefundReqNo  field.String // 退款流水号

	fieldMap map[string]field.Expr
}

func (p paymentPayYeepayRefund) Table(newTableName string) *paymentPayYeepayRefund {
	p.paymentPayYeepayRefundDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p paymentPayYeepayRefund) As(alias string) *paymentPayYeepayRefund {
	p.paymentPayYeepayRefundDo.DO = *(p.paymentPayYeepayRefundDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *paymentPayYeepayRefund) updateTableName(table string) *paymentPayYeepayRefund {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.OrderID = field.NewString(table, "order_id")
	p.RefundAmount = field.NewField(table, "refund_amount")
	p.Status = field.NewInt16(table, "status")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.Version = field.NewField(table, "version")
	p.Remark = field.NewString(table, "remark")
	p.RefundReqNo = field.NewString(table, "refund_req_no")

	p.fillFieldMap()

	return p
}

func (p *paymentPayYeepayRefund) WithContext(ctx context.Context) IPaymentPayYeepayRefundDo {
	return p.paymentPayYeepayRefundDo.WithContext(ctx)
}

func (p paymentPayYeepayRefund) TableName() string { return p.paymentPayYeepayRefundDo.TableName() }

func (p paymentPayYeepayRefund) Alias() string { return p.paymentPayYeepayRefundDo.Alias() }

func (p paymentPayYeepayRefund) Columns(cols ...field.Expr) gen.Columns {
	return p.paymentPayYeepayRefundDo.Columns(cols...)
}

func (p *paymentPayYeepayRefund) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *paymentPayYeepayRefund) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 10)
	p.fieldMap["id"] = p.ID
	p.fieldMap["order_id"] = p.OrderID
	p.fieldMap["refund_amount"] = p.RefundAmount
	p.fieldMap["status"] = p.Status
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["version"] = p.Version
	p.fieldMap["remark"] = p.Remark
	p.fieldMap["refund_req_no"] = p.RefundReqNo
}

func (p paymentPayYeepayRefund) clone(db *gorm.DB) paymentPayYeepayRefund {
	p.paymentPayYeepayRefundDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p paymentPayYeepayRefund) replaceDB(db *gorm.DB) paymentPayYeepayRefund {
	p.paymentPayYeepayRefundDo.ReplaceDB(db)
	return p
}

type paymentPayYeepayRefundDo struct{ gen.DO }

type IPaymentPayYeepayRefundDo interface {
	gen.SubQuery
	Debug() IPaymentPayYeepayRefundDo
	WithContext(ctx context.Context) IPaymentPayYeepayRefundDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPaymentPayYeepayRefundDo
	WriteDB() IPaymentPayYeepayRefundDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPaymentPayYeepayRefundDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPaymentPayYeepayRefundDo
	Not(conds ...gen.Condition) IPaymentPayYeepayRefundDo
	Or(conds ...gen.Condition) IPaymentPayYeepayRefundDo
	Select(conds ...field.Expr) IPaymentPayYeepayRefundDo
	Where(conds ...gen.Condition) IPaymentPayYeepayRefundDo
	Order(conds ...field.Expr) IPaymentPayYeepayRefundDo
	Distinct(cols ...field.Expr) IPaymentPayYeepayRefundDo
	Omit(cols ...field.Expr) IPaymentPayYeepayRefundDo
	Join(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayRefundDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayRefundDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayRefundDo
	Group(cols ...field.Expr) IPaymentPayYeepayRefundDo
	Having(conds ...gen.Condition) IPaymentPayYeepayRefundDo
	Limit(limit int) IPaymentPayYeepayRefundDo
	Offset(offset int) IPaymentPayYeepayRefundDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentPayYeepayRefundDo
	Unscoped() IPaymentPayYeepayRefundDo
	Create(values ...*model.PaymentPayYeepayRefund) error
	CreateInBatches(values []*model.PaymentPayYeepayRefund, batchSize int) error
	Save(values ...*model.PaymentPayYeepayRefund) error
	First() (*model.PaymentPayYeepayRefund, error)
	Take() (*model.PaymentPayYeepayRefund, error)
	Last() (*model.PaymentPayYeepayRefund, error)
	Find() ([]*model.PaymentPayYeepayRefund, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentPayYeepayRefund, err error)
	FindInBatches(result *[]*model.PaymentPayYeepayRefund, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PaymentPayYeepayRefund) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPaymentPayYeepayRefundDo
	Assign(attrs ...field.AssignExpr) IPaymentPayYeepayRefundDo
	Joins(fields ...field.RelationField) IPaymentPayYeepayRefundDo
	Preload(fields ...field.RelationField) IPaymentPayYeepayRefundDo
	FirstOrInit() (*model.PaymentPayYeepayRefund, error)
	FirstOrCreate() (*model.PaymentPayYeepayRefund, error)
	FindByPage(offset int, limit int) (result []*model.PaymentPayYeepayRefund, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPaymentPayYeepayRefundDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p paymentPayYeepayRefundDo) Debug() IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Debug())
}

func (p paymentPayYeepayRefundDo) WithContext(ctx context.Context) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p paymentPayYeepayRefundDo) ReadDB() IPaymentPayYeepayRefundDo {
	return p.Clauses(dbresolver.Read)
}

func (p paymentPayYeepayRefundDo) WriteDB() IPaymentPayYeepayRefundDo {
	return p.Clauses(dbresolver.Write)
}

func (p paymentPayYeepayRefundDo) Session(config *gorm.Session) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Session(config))
}

func (p paymentPayYeepayRefundDo) Clauses(conds ...clause.Expression) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p paymentPayYeepayRefundDo) Returning(value interface{}, columns ...string) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p paymentPayYeepayRefundDo) Not(conds ...gen.Condition) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p paymentPayYeepayRefundDo) Or(conds ...gen.Condition) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p paymentPayYeepayRefundDo) Select(conds ...field.Expr) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p paymentPayYeepayRefundDo) Where(conds ...gen.Condition) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p paymentPayYeepayRefundDo) Order(conds ...field.Expr) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p paymentPayYeepayRefundDo) Distinct(cols ...field.Expr) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p paymentPayYeepayRefundDo) Omit(cols ...field.Expr) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p paymentPayYeepayRefundDo) Join(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p paymentPayYeepayRefundDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p paymentPayYeepayRefundDo) RightJoin(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p paymentPayYeepayRefundDo) Group(cols ...field.Expr) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p paymentPayYeepayRefundDo) Having(conds ...gen.Condition) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p paymentPayYeepayRefundDo) Limit(limit int) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p paymentPayYeepayRefundDo) Offset(offset int) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p paymentPayYeepayRefundDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p paymentPayYeepayRefundDo) Unscoped() IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Unscoped())
}

func (p paymentPayYeepayRefundDo) Create(values ...*model.PaymentPayYeepayRefund) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p paymentPayYeepayRefundDo) CreateInBatches(values []*model.PaymentPayYeepayRefund, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p paymentPayYeepayRefundDo) Save(values ...*model.PaymentPayYeepayRefund) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p paymentPayYeepayRefundDo) First() (*model.PaymentPayYeepayRefund, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepayRefund), nil
	}
}

func (p paymentPayYeepayRefundDo) Take() (*model.PaymentPayYeepayRefund, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepayRefund), nil
	}
}

func (p paymentPayYeepayRefundDo) Last() (*model.PaymentPayYeepayRefund, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepayRefund), nil
	}
}

func (p paymentPayYeepayRefundDo) Find() ([]*model.PaymentPayYeepayRefund, error) {
	result, err := p.DO.Find()
	return result.([]*model.PaymentPayYeepayRefund), err
}

func (p paymentPayYeepayRefundDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentPayYeepayRefund, err error) {
	buf := make([]*model.PaymentPayYeepayRefund, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p paymentPayYeepayRefundDo) FindInBatches(result *[]*model.PaymentPayYeepayRefund, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p paymentPayYeepayRefundDo) Attrs(attrs ...field.AssignExpr) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p paymentPayYeepayRefundDo) Assign(attrs ...field.AssignExpr) IPaymentPayYeepayRefundDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p paymentPayYeepayRefundDo) Joins(fields ...field.RelationField) IPaymentPayYeepayRefundDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p paymentPayYeepayRefundDo) Preload(fields ...field.RelationField) IPaymentPayYeepayRefundDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p paymentPayYeepayRefundDo) FirstOrInit() (*model.PaymentPayYeepayRefund, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepayRefund), nil
	}
}

func (p paymentPayYeepayRefundDo) FirstOrCreate() (*model.PaymentPayYeepayRefund, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepayRefund), nil
	}
}

func (p paymentPayYeepayRefundDo) FindByPage(offset int, limit int) (result []*model.PaymentPayYeepayRefund, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p paymentPayYeepayRefundDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p paymentPayYeepayRefundDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p paymentPayYeepayRefundDo) Delete(models ...*model.PaymentPayYeepayRefund) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *paymentPayYeepayRefundDo) withDO(do gen.Dao) *paymentPayYeepayRefundDo {
	p.DO = *do.(*gen.DO)
	return p
}
