// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
)

func newPaymentDivideSecondary(db *gorm.DB, opts ...gen.DOOption) paymentDivideSecondary {
	_paymentDivideSecondary := paymentDivideSecondary{}

	_paymentDivideSecondary.paymentDivideSecondaryDo.UseDB(db, opts...)
	_paymentDivideSecondary.paymentDivideSecondaryDo.UseModel(&model.PaymentDivideSecondary{})

	tableName := _paymentDivideSecondary.paymentDivideSecondaryDo.TableName()
	_paymentDivideSecondary.ALL = field.NewAsterisk(tableName)
	_paymentDivideSecondary.ID = field.NewString(tableName, "id")
	_paymentDivideSecondary.CreatedAt = field.NewTime(tableName, "created_at")
	_paymentDivideSecondary.UpdatedAt = field.NewTime(tableName, "updated_at")
	_paymentDivideSecondary.Version = field.NewField(tableName, "version")
	_paymentDivideSecondary.RecipientDescription = field.NewString(tableName, "recipient_description")
	_paymentDivideSecondary.ProjectID = field.NewString(tableName, "project_id")
	_paymentDivideSecondary.RevenueShare = field.NewField(tableName, "revenue_share")

	_paymentDivideSecondary.fillFieldMap()

	return _paymentDivideSecondary
}

type paymentDivideSecondary struct {
	paymentDivideSecondaryDo paymentDivideSecondaryDo

	ALL                  field.Asterisk
	ID                   field.String // id
	CreatedAt            field.Time   // 创建时间
	UpdatedAt            field.Time   // 更新时间
	Version              field.Field  // 乐观锁
	RecipientDescription field.String // 分账接收方描述
	ProjectID            field.String // 项目id
	RevenueShare         field.Field  // 分成比例如：0.88

	fieldMap map[string]field.Expr
}

func (p paymentDivideSecondary) Table(newTableName string) *paymentDivideSecondary {
	p.paymentDivideSecondaryDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p paymentDivideSecondary) As(alias string) *paymentDivideSecondary {
	p.paymentDivideSecondaryDo.DO = *(p.paymentDivideSecondaryDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *paymentDivideSecondary) updateTableName(table string) *paymentDivideSecondary {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.Version = field.NewField(table, "version")
	p.RecipientDescription = field.NewString(table, "recipient_description")
	p.ProjectID = field.NewString(table, "project_id")
	p.RevenueShare = field.NewField(table, "revenue_share")

	p.fillFieldMap()

	return p
}

func (p *paymentDivideSecondary) WithContext(ctx context.Context) IPaymentDivideSecondaryDo {
	return p.paymentDivideSecondaryDo.WithContext(ctx)
}

func (p paymentDivideSecondary) TableName() string { return p.paymentDivideSecondaryDo.TableName() }

func (p paymentDivideSecondary) Alias() string { return p.paymentDivideSecondaryDo.Alias() }

func (p paymentDivideSecondary) Columns(cols ...field.Expr) gen.Columns {
	return p.paymentDivideSecondaryDo.Columns(cols...)
}

func (p *paymentDivideSecondary) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *paymentDivideSecondary) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 7)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["version"] = p.Version
	p.fieldMap["recipient_description"] = p.RecipientDescription
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["revenue_share"] = p.RevenueShare
}

func (p paymentDivideSecondary) clone(db *gorm.DB) paymentDivideSecondary {
	p.paymentDivideSecondaryDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p paymentDivideSecondary) replaceDB(db *gorm.DB) paymentDivideSecondary {
	p.paymentDivideSecondaryDo.ReplaceDB(db)
	return p
}

type paymentDivideSecondaryDo struct{ gen.DO }

type IPaymentDivideSecondaryDo interface {
	gen.SubQuery
	Debug() IPaymentDivideSecondaryDo
	WithContext(ctx context.Context) IPaymentDivideSecondaryDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPaymentDivideSecondaryDo
	WriteDB() IPaymentDivideSecondaryDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPaymentDivideSecondaryDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPaymentDivideSecondaryDo
	Not(conds ...gen.Condition) IPaymentDivideSecondaryDo
	Or(conds ...gen.Condition) IPaymentDivideSecondaryDo
	Select(conds ...field.Expr) IPaymentDivideSecondaryDo
	Where(conds ...gen.Condition) IPaymentDivideSecondaryDo
	Order(conds ...field.Expr) IPaymentDivideSecondaryDo
	Distinct(cols ...field.Expr) IPaymentDivideSecondaryDo
	Omit(cols ...field.Expr) IPaymentDivideSecondaryDo
	Join(table schema.Tabler, on ...field.Expr) IPaymentDivideSecondaryDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentDivideSecondaryDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPaymentDivideSecondaryDo
	Group(cols ...field.Expr) IPaymentDivideSecondaryDo
	Having(conds ...gen.Condition) IPaymentDivideSecondaryDo
	Limit(limit int) IPaymentDivideSecondaryDo
	Offset(offset int) IPaymentDivideSecondaryDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentDivideSecondaryDo
	Unscoped() IPaymentDivideSecondaryDo
	Create(values ...*model.PaymentDivideSecondary) error
	CreateInBatches(values []*model.PaymentDivideSecondary, batchSize int) error
	Save(values ...*model.PaymentDivideSecondary) error
	First() (*model.PaymentDivideSecondary, error)
	Take() (*model.PaymentDivideSecondary, error)
	Last() (*model.PaymentDivideSecondary, error)
	Find() ([]*model.PaymentDivideSecondary, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentDivideSecondary, err error)
	FindInBatches(result *[]*model.PaymentDivideSecondary, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PaymentDivideSecondary) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPaymentDivideSecondaryDo
	Assign(attrs ...field.AssignExpr) IPaymentDivideSecondaryDo
	Joins(fields ...field.RelationField) IPaymentDivideSecondaryDo
	Preload(fields ...field.RelationField) IPaymentDivideSecondaryDo
	FirstOrInit() (*model.PaymentDivideSecondary, error)
	FirstOrCreate() (*model.PaymentDivideSecondary, error)
	FindByPage(offset int, limit int) (result []*model.PaymentDivideSecondary, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPaymentDivideSecondaryDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p paymentDivideSecondaryDo) Debug() IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Debug())
}

func (p paymentDivideSecondaryDo) WithContext(ctx context.Context) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p paymentDivideSecondaryDo) ReadDB() IPaymentDivideSecondaryDo {
	return p.Clauses(dbresolver.Read)
}

func (p paymentDivideSecondaryDo) WriteDB() IPaymentDivideSecondaryDo {
	return p.Clauses(dbresolver.Write)
}

func (p paymentDivideSecondaryDo) Session(config *gorm.Session) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Session(config))
}

func (p paymentDivideSecondaryDo) Clauses(conds ...clause.Expression) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p paymentDivideSecondaryDo) Returning(value interface{}, columns ...string) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p paymentDivideSecondaryDo) Not(conds ...gen.Condition) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p paymentDivideSecondaryDo) Or(conds ...gen.Condition) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p paymentDivideSecondaryDo) Select(conds ...field.Expr) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p paymentDivideSecondaryDo) Where(conds ...gen.Condition) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p paymentDivideSecondaryDo) Order(conds ...field.Expr) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p paymentDivideSecondaryDo) Distinct(cols ...field.Expr) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p paymentDivideSecondaryDo) Omit(cols ...field.Expr) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p paymentDivideSecondaryDo) Join(table schema.Tabler, on ...field.Expr) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p paymentDivideSecondaryDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p paymentDivideSecondaryDo) RightJoin(table schema.Tabler, on ...field.Expr) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p paymentDivideSecondaryDo) Group(cols ...field.Expr) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p paymentDivideSecondaryDo) Having(conds ...gen.Condition) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p paymentDivideSecondaryDo) Limit(limit int) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p paymentDivideSecondaryDo) Offset(offset int) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p paymentDivideSecondaryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p paymentDivideSecondaryDo) Unscoped() IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Unscoped())
}

func (p paymentDivideSecondaryDo) Create(values ...*model.PaymentDivideSecondary) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p paymentDivideSecondaryDo) CreateInBatches(values []*model.PaymentDivideSecondary, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p paymentDivideSecondaryDo) Save(values ...*model.PaymentDivideSecondary) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p paymentDivideSecondaryDo) First() (*model.PaymentDivideSecondary, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentDivideSecondary), nil
	}
}

func (p paymentDivideSecondaryDo) Take() (*model.PaymentDivideSecondary, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentDivideSecondary), nil
	}
}

func (p paymentDivideSecondaryDo) Last() (*model.PaymentDivideSecondary, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentDivideSecondary), nil
	}
}

func (p paymentDivideSecondaryDo) Find() ([]*model.PaymentDivideSecondary, error) {
	result, err := p.DO.Find()
	return result.([]*model.PaymentDivideSecondary), err
}

func (p paymentDivideSecondaryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentDivideSecondary, err error) {
	buf := make([]*model.PaymentDivideSecondary, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p paymentDivideSecondaryDo) FindInBatches(result *[]*model.PaymentDivideSecondary, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p paymentDivideSecondaryDo) Attrs(attrs ...field.AssignExpr) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p paymentDivideSecondaryDo) Assign(attrs ...field.AssignExpr) IPaymentDivideSecondaryDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p paymentDivideSecondaryDo) Joins(fields ...field.RelationField) IPaymentDivideSecondaryDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p paymentDivideSecondaryDo) Preload(fields ...field.RelationField) IPaymentDivideSecondaryDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p paymentDivideSecondaryDo) FirstOrInit() (*model.PaymentDivideSecondary, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentDivideSecondary), nil
	}
}

func (p paymentDivideSecondaryDo) FirstOrCreate() (*model.PaymentDivideSecondary, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentDivideSecondary), nil
	}
}

func (p paymentDivideSecondaryDo) FindByPage(offset int, limit int) (result []*model.PaymentDivideSecondary, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p paymentDivideSecondaryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p paymentDivideSecondaryDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p paymentDivideSecondaryDo) Delete(models ...*model.PaymentDivideSecondary) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *paymentDivideSecondaryDo) withDO(do gen.Dao) *paymentDivideSecondaryDo {
	p.DO = *do.(*gen.DO)
	return p
}
