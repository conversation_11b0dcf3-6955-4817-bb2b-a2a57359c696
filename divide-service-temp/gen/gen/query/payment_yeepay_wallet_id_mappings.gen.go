// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
)

func newPaymentYeepayWalletIDMapping(db *gorm.DB, opts ...gen.DOOption) paymentYeepayWalletIDMapping {
	_paymentYeepayWalletIDMapping := paymentYeepayWalletIDMapping{}

	_paymentYeepayWalletIDMapping.paymentYeepayWalletIDMappingDo.UseDB(db, opts...)
	_paymentYeepayWalletIDMapping.paymentYeepayWalletIDMappingDo.UseModel(&model.PaymentYeepayWalletIDMapping{})

	tableName := _paymentYeepayWalletIDMapping.paymentYeepayWalletIDMappingDo.TableName()
	_paymentYeepayWalletIDMapping.ALL = field.NewAsterisk(tableName)
	_paymentYeepayWalletIDMapping.ID = field.NewInt32(tableName, "id")
	_paymentYeepayWalletIDMapping.MysqlID = field.NewInt64(tableName, "mysql_id")
	_paymentYeepayWalletIDMapping.PgUUID = field.NewString(tableName, "pg_uuid")
	_paymentYeepayWalletIDMapping.CreatedAt = field.NewTime(tableName, "created_at")

	_paymentYeepayWalletIDMapping.fillFieldMap()

	return _paymentYeepayWalletIDMapping
}

type paymentYeepayWalletIDMapping struct {
	paymentYeepayWalletIDMappingDo paymentYeepayWalletIDMappingDo

	ALL       field.Asterisk
	ID        field.Int32
	MysqlID   field.Int64
	PgUUID    field.String
	CreatedAt field.Time

	fieldMap map[string]field.Expr
}

func (p paymentYeepayWalletIDMapping) Table(newTableName string) *paymentYeepayWalletIDMapping {
	p.paymentYeepayWalletIDMappingDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p paymentYeepayWalletIDMapping) As(alias string) *paymentYeepayWalletIDMapping {
	p.paymentYeepayWalletIDMappingDo.DO = *(p.paymentYeepayWalletIDMappingDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *paymentYeepayWalletIDMapping) updateTableName(table string) *paymentYeepayWalletIDMapping {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt32(table, "id")
	p.MysqlID = field.NewInt64(table, "mysql_id")
	p.PgUUID = field.NewString(table, "pg_uuid")
	p.CreatedAt = field.NewTime(table, "created_at")

	p.fillFieldMap()

	return p
}

func (p *paymentYeepayWalletIDMapping) WithContext(ctx context.Context) IPaymentYeepayWalletIDMappingDo {
	return p.paymentYeepayWalletIDMappingDo.WithContext(ctx)
}

func (p paymentYeepayWalletIDMapping) TableName() string {
	return p.paymentYeepayWalletIDMappingDo.TableName()
}

func (p paymentYeepayWalletIDMapping) Alias() string { return p.paymentYeepayWalletIDMappingDo.Alias() }

func (p paymentYeepayWalletIDMapping) Columns(cols ...field.Expr) gen.Columns {
	return p.paymentYeepayWalletIDMappingDo.Columns(cols...)
}

func (p *paymentYeepayWalletIDMapping) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *paymentYeepayWalletIDMapping) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 4)
	p.fieldMap["id"] = p.ID
	p.fieldMap["mysql_id"] = p.MysqlID
	p.fieldMap["pg_uuid"] = p.PgUUID
	p.fieldMap["created_at"] = p.CreatedAt
}

func (p paymentYeepayWalletIDMapping) clone(db *gorm.DB) paymentYeepayWalletIDMapping {
	p.paymentYeepayWalletIDMappingDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p paymentYeepayWalletIDMapping) replaceDB(db *gorm.DB) paymentYeepayWalletIDMapping {
	p.paymentYeepayWalletIDMappingDo.ReplaceDB(db)
	return p
}

type paymentYeepayWalletIDMappingDo struct{ gen.DO }

type IPaymentYeepayWalletIDMappingDo interface {
	gen.SubQuery
	Debug() IPaymentYeepayWalletIDMappingDo
	WithContext(ctx context.Context) IPaymentYeepayWalletIDMappingDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPaymentYeepayWalletIDMappingDo
	WriteDB() IPaymentYeepayWalletIDMappingDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPaymentYeepayWalletIDMappingDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPaymentYeepayWalletIDMappingDo
	Not(conds ...gen.Condition) IPaymentYeepayWalletIDMappingDo
	Or(conds ...gen.Condition) IPaymentYeepayWalletIDMappingDo
	Select(conds ...field.Expr) IPaymentYeepayWalletIDMappingDo
	Where(conds ...gen.Condition) IPaymentYeepayWalletIDMappingDo
	Order(conds ...field.Expr) IPaymentYeepayWalletIDMappingDo
	Distinct(cols ...field.Expr) IPaymentYeepayWalletIDMappingDo
	Omit(cols ...field.Expr) IPaymentYeepayWalletIDMappingDo
	Join(table schema.Tabler, on ...field.Expr) IPaymentYeepayWalletIDMappingDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentYeepayWalletIDMappingDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPaymentYeepayWalletIDMappingDo
	Group(cols ...field.Expr) IPaymentYeepayWalletIDMappingDo
	Having(conds ...gen.Condition) IPaymentYeepayWalletIDMappingDo
	Limit(limit int) IPaymentYeepayWalletIDMappingDo
	Offset(offset int) IPaymentYeepayWalletIDMappingDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentYeepayWalletIDMappingDo
	Unscoped() IPaymentYeepayWalletIDMappingDo
	Create(values ...*model.PaymentYeepayWalletIDMapping) error
	CreateInBatches(values []*model.PaymentYeepayWalletIDMapping, batchSize int) error
	Save(values ...*model.PaymentYeepayWalletIDMapping) error
	First() (*model.PaymentYeepayWalletIDMapping, error)
	Take() (*model.PaymentYeepayWalletIDMapping, error)
	Last() (*model.PaymentYeepayWalletIDMapping, error)
	Find() ([]*model.PaymentYeepayWalletIDMapping, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentYeepayWalletIDMapping, err error)
	FindInBatches(result *[]*model.PaymentYeepayWalletIDMapping, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PaymentYeepayWalletIDMapping) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPaymentYeepayWalletIDMappingDo
	Assign(attrs ...field.AssignExpr) IPaymentYeepayWalletIDMappingDo
	Joins(fields ...field.RelationField) IPaymentYeepayWalletIDMappingDo
	Preload(fields ...field.RelationField) IPaymentYeepayWalletIDMappingDo
	FirstOrInit() (*model.PaymentYeepayWalletIDMapping, error)
	FirstOrCreate() (*model.PaymentYeepayWalletIDMapping, error)
	FindByPage(offset int, limit int) (result []*model.PaymentYeepayWalletIDMapping, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPaymentYeepayWalletIDMappingDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p paymentYeepayWalletIDMappingDo) Debug() IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Debug())
}

func (p paymentYeepayWalletIDMappingDo) WithContext(ctx context.Context) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p paymentYeepayWalletIDMappingDo) ReadDB() IPaymentYeepayWalletIDMappingDo {
	return p.Clauses(dbresolver.Read)
}

func (p paymentYeepayWalletIDMappingDo) WriteDB() IPaymentYeepayWalletIDMappingDo {
	return p.Clauses(dbresolver.Write)
}

func (p paymentYeepayWalletIDMappingDo) Session(config *gorm.Session) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Session(config))
}

func (p paymentYeepayWalletIDMappingDo) Clauses(conds ...clause.Expression) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p paymentYeepayWalletIDMappingDo) Returning(value interface{}, columns ...string) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p paymentYeepayWalletIDMappingDo) Not(conds ...gen.Condition) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p paymentYeepayWalletIDMappingDo) Or(conds ...gen.Condition) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p paymentYeepayWalletIDMappingDo) Select(conds ...field.Expr) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p paymentYeepayWalletIDMappingDo) Where(conds ...gen.Condition) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p paymentYeepayWalletIDMappingDo) Order(conds ...field.Expr) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p paymentYeepayWalletIDMappingDo) Distinct(cols ...field.Expr) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p paymentYeepayWalletIDMappingDo) Omit(cols ...field.Expr) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p paymentYeepayWalletIDMappingDo) Join(table schema.Tabler, on ...field.Expr) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p paymentYeepayWalletIDMappingDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p paymentYeepayWalletIDMappingDo) RightJoin(table schema.Tabler, on ...field.Expr) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p paymentYeepayWalletIDMappingDo) Group(cols ...field.Expr) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p paymentYeepayWalletIDMappingDo) Having(conds ...gen.Condition) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p paymentYeepayWalletIDMappingDo) Limit(limit int) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p paymentYeepayWalletIDMappingDo) Offset(offset int) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p paymentYeepayWalletIDMappingDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p paymentYeepayWalletIDMappingDo) Unscoped() IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Unscoped())
}

func (p paymentYeepayWalletIDMappingDo) Create(values ...*model.PaymentYeepayWalletIDMapping) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p paymentYeepayWalletIDMappingDo) CreateInBatches(values []*model.PaymentYeepayWalletIDMapping, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p paymentYeepayWalletIDMappingDo) Save(values ...*model.PaymentYeepayWalletIDMapping) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p paymentYeepayWalletIDMappingDo) First() (*model.PaymentYeepayWalletIDMapping, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentYeepayWalletIDMapping), nil
	}
}

func (p paymentYeepayWalletIDMappingDo) Take() (*model.PaymentYeepayWalletIDMapping, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentYeepayWalletIDMapping), nil
	}
}

func (p paymentYeepayWalletIDMappingDo) Last() (*model.PaymentYeepayWalletIDMapping, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentYeepayWalletIDMapping), nil
	}
}

func (p paymentYeepayWalletIDMappingDo) Find() ([]*model.PaymentYeepayWalletIDMapping, error) {
	result, err := p.DO.Find()
	return result.([]*model.PaymentYeepayWalletIDMapping), err
}

func (p paymentYeepayWalletIDMappingDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentYeepayWalletIDMapping, err error) {
	buf := make([]*model.PaymentYeepayWalletIDMapping, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p paymentYeepayWalletIDMappingDo) FindInBatches(result *[]*model.PaymentYeepayWalletIDMapping, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p paymentYeepayWalletIDMappingDo) Attrs(attrs ...field.AssignExpr) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p paymentYeepayWalletIDMappingDo) Assign(attrs ...field.AssignExpr) IPaymentYeepayWalletIDMappingDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p paymentYeepayWalletIDMappingDo) Joins(fields ...field.RelationField) IPaymentYeepayWalletIDMappingDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p paymentYeepayWalletIDMappingDo) Preload(fields ...field.RelationField) IPaymentYeepayWalletIDMappingDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p paymentYeepayWalletIDMappingDo) FirstOrInit() (*model.PaymentYeepayWalletIDMapping, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentYeepayWalletIDMapping), nil
	}
}

func (p paymentYeepayWalletIDMappingDo) FirstOrCreate() (*model.PaymentYeepayWalletIDMapping, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentYeepayWalletIDMapping), nil
	}
}

func (p paymentYeepayWalletIDMappingDo) FindByPage(offset int, limit int) (result []*model.PaymentYeepayWalletIDMapping, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p paymentYeepayWalletIDMappingDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p paymentYeepayWalletIDMappingDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p paymentYeepayWalletIDMappingDo) Delete(models ...*model.PaymentYeepayWalletIDMapping) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *paymentYeepayWalletIDMappingDo) withDO(do gen.Dao) *paymentYeepayWalletIDMappingDo {
	p.DO = *do.(*gen.DO)
	return p
}
