// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                            = new(Query)
	PaymentDivideSecondary       *paymentDivideSecondary
	PaymentOrder                 *paymentOrder
	PaymentOrderPrimary          *paymentOrderPrimary
	PaymentOrderSecondary        *paymentOrderSecondary
	PaymentPayYeepay             *paymentPayYeepay
	PaymentPayYeepayDivide       *paymentPayYeepayDivide
	PaymentPayYeepayRefund       *paymentPayYeepayRefund
	PaymentUserYeepayWallet      *paymentUserYeepayWallet
	PaymentYeepayWalletIDMapping *paymentYeepayWalletIDMapping
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	PaymentDivideSecondary = &Q.PaymentDivideSecondary
	PaymentOrder = &Q.PaymentOrder
	PaymentOrderPrimary = &Q.PaymentOrderPrimary
	PaymentOrderSecondary = &Q.PaymentOrderSecondary
	PaymentPayYeepay = &Q.PaymentPayYeepay
	PaymentPayYeepayDivide = &Q.PaymentPayYeepayDivide
	PaymentPayYeepayRefund = &Q.PaymentPayYeepayRefund
	PaymentUserYeepayWallet = &Q.PaymentUserYeepayWallet
	PaymentYeepayWalletIDMapping = &Q.PaymentYeepayWalletIDMapping
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                           db,
		PaymentDivideSecondary:       newPaymentDivideSecondary(db, opts...),
		PaymentOrder:                 newPaymentOrder(db, opts...),
		PaymentOrderPrimary:          newPaymentOrderPrimary(db, opts...),
		PaymentOrderSecondary:        newPaymentOrderSecondary(db, opts...),
		PaymentPayYeepay:             newPaymentPayYeepay(db, opts...),
		PaymentPayYeepayDivide:       newPaymentPayYeepayDivide(db, opts...),
		PaymentPayYeepayRefund:       newPaymentPayYeepayRefund(db, opts...),
		PaymentUserYeepayWallet:      newPaymentUserYeepayWallet(db, opts...),
		PaymentYeepayWalletIDMapping: newPaymentYeepayWalletIDMapping(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	PaymentDivideSecondary       paymentDivideSecondary
	PaymentOrder                 paymentOrder
	PaymentOrderPrimary          paymentOrderPrimary
	PaymentOrderSecondary        paymentOrderSecondary
	PaymentPayYeepay             paymentPayYeepay
	PaymentPayYeepayDivide       paymentPayYeepayDivide
	PaymentPayYeepayRefund       paymentPayYeepayRefund
	PaymentUserYeepayWallet      paymentUserYeepayWallet
	PaymentYeepayWalletIDMapping paymentYeepayWalletIDMapping
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                           db,
		PaymentDivideSecondary:       q.PaymentDivideSecondary.clone(db),
		PaymentOrder:                 q.PaymentOrder.clone(db),
		PaymentOrderPrimary:          q.PaymentOrderPrimary.clone(db),
		PaymentOrderSecondary:        q.PaymentOrderSecondary.clone(db),
		PaymentPayYeepay:             q.PaymentPayYeepay.clone(db),
		PaymentPayYeepayDivide:       q.PaymentPayYeepayDivide.clone(db),
		PaymentPayYeepayRefund:       q.PaymentPayYeepayRefund.clone(db),
		PaymentUserYeepayWallet:      q.PaymentUserYeepayWallet.clone(db),
		PaymentYeepayWalletIDMapping: q.PaymentYeepayWalletIDMapping.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                           db,
		PaymentDivideSecondary:       q.PaymentDivideSecondary.replaceDB(db),
		PaymentOrder:                 q.PaymentOrder.replaceDB(db),
		PaymentOrderPrimary:          q.PaymentOrderPrimary.replaceDB(db),
		PaymentOrderSecondary:        q.PaymentOrderSecondary.replaceDB(db),
		PaymentPayYeepay:             q.PaymentPayYeepay.replaceDB(db),
		PaymentPayYeepayDivide:       q.PaymentPayYeepayDivide.replaceDB(db),
		PaymentPayYeepayRefund:       q.PaymentPayYeepayRefund.replaceDB(db),
		PaymentUserYeepayWallet:      q.PaymentUserYeepayWallet.replaceDB(db),
		PaymentYeepayWalletIDMapping: q.PaymentYeepayWalletIDMapping.replaceDB(db),
	}
}

type queryCtx struct {
	PaymentDivideSecondary       IPaymentDivideSecondaryDo
	PaymentOrder                 IPaymentOrderDo
	PaymentOrderPrimary          IPaymentOrderPrimaryDo
	PaymentOrderSecondary        IPaymentOrderSecondaryDo
	PaymentPayYeepay             IPaymentPayYeepayDo
	PaymentPayYeepayDivide       IPaymentPayYeepayDivideDo
	PaymentPayYeepayRefund       IPaymentPayYeepayRefundDo
	PaymentUserYeepayWallet      IPaymentUserYeepayWalletDo
	PaymentYeepayWalletIDMapping IPaymentYeepayWalletIDMappingDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		PaymentDivideSecondary:       q.PaymentDivideSecondary.WithContext(ctx),
		PaymentOrder:                 q.PaymentOrder.WithContext(ctx),
		PaymentOrderPrimary:          q.PaymentOrderPrimary.WithContext(ctx),
		PaymentOrderSecondary:        q.PaymentOrderSecondary.WithContext(ctx),
		PaymentPayYeepay:             q.PaymentPayYeepay.WithContext(ctx),
		PaymentPayYeepayDivide:       q.PaymentPayYeepayDivide.WithContext(ctx),
		PaymentPayYeepayRefund:       q.PaymentPayYeepayRefund.WithContext(ctx),
		PaymentUserYeepayWallet:      q.PaymentUserYeepayWallet.WithContext(ctx),
		PaymentYeepayWalletIDMapping: q.PaymentYeepayWalletIDMapping.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
