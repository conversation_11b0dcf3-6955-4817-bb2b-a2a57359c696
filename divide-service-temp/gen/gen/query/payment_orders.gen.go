// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
)

func newPaymentOrder(db *gorm.DB, opts ...gen.DOOption) paymentOrder {
	_paymentOrder := paymentOrder{}

	_paymentOrder.paymentOrderDo.UseDB(db, opts...)
	_paymentOrder.paymentOrderDo.UseModel(&model.PaymentOrder{})

	tableName := _paymentOrder.paymentOrderDo.TableName()
	_paymentOrder.ALL = field.NewAsterisk(tableName)
	_paymentOrder.ID = field.NewString(tableName, "id")
	_paymentOrder.Status = field.NewInt16(tableName, "status")
	_paymentOrder.OrderNo = field.NewString(tableName, "orderNo")
	_paymentOrder.UserID = field.NewString(tableName, "user_id")
	_paymentOrder.Type = field.NewInt16(tableName, "type")
	_paymentOrder.Title = field.NewString(tableName, "title")
	_paymentOrder.CreatedAt = field.NewTime(tableName, "created_at")
	_paymentOrder.UpdatedAt = field.NewTime(tableName, "updated_at")
	_paymentOrder.DeletedAt = field.NewField(tableName, "deleted_at")
	_paymentOrder.Version = field.NewField(tableName, "version")
	_paymentOrder.PayType = field.NewInt16(tableName, "pay_type")
	_paymentOrder.PayTime = field.NewTime(tableName, "pay_time")
	_paymentOrder.ExpiredTime = field.NewTime(tableName, "expired_time")
	_paymentOrder.ImgURL = field.NewString(tableName, "img_url")
	_paymentOrder.Price = field.NewField(tableName, "price")
	_paymentOrder.PayID = field.NewString(tableName, "pay_id")

	_paymentOrder.fillFieldMap()

	return _paymentOrder
}

type paymentOrder struct {
	paymentOrderDo paymentOrderDo

	ALL         field.Asterisk
	ID          field.String // id
	Status      field.Int16  // 订单状态
	OrderNo     field.String // 订单号
	UserID      field.String // 用户id
	Type        field.Int16  // 订单类型
	Title       field.String // 订单标题
	CreatedAt   field.Time   // 创建时间
	UpdatedAt   field.Time   // 更新时间
	DeletedAt   field.Field  // 软删除
	Version     field.Field  // 乐观锁
	PayType     field.Int16  // 支付方式类型
	PayTime     field.Time   // 支付时间
	ExpiredTime field.Time   // 订单过期时间
	ImgURL      field.String // 图片
	Price       field.Field  // 价格
	PayID       field.String // 对应支付方式表id

	fieldMap map[string]field.Expr
}

func (p paymentOrder) Table(newTableName string) *paymentOrder {
	p.paymentOrderDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p paymentOrder) As(alias string) *paymentOrder {
	p.paymentOrderDo.DO = *(p.paymentOrderDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *paymentOrder) updateTableName(table string) *paymentOrder {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.Status = field.NewInt16(table, "status")
	p.OrderNo = field.NewString(table, "orderNo")
	p.UserID = field.NewString(table, "user_id")
	p.Type = field.NewInt16(table, "type")
	p.Title = field.NewString(table, "title")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.Version = field.NewField(table, "version")
	p.PayType = field.NewInt16(table, "pay_type")
	p.PayTime = field.NewTime(table, "pay_time")
	p.ExpiredTime = field.NewTime(table, "expired_time")
	p.ImgURL = field.NewString(table, "img_url")
	p.Price = field.NewField(table, "price")
	p.PayID = field.NewString(table, "pay_id")

	p.fillFieldMap()

	return p
}

func (p *paymentOrder) WithContext(ctx context.Context) IPaymentOrderDo {
	return p.paymentOrderDo.WithContext(ctx)
}

func (p paymentOrder) TableName() string { return p.paymentOrderDo.TableName() }

func (p paymentOrder) Alias() string { return p.paymentOrderDo.Alias() }

func (p paymentOrder) Columns(cols ...field.Expr) gen.Columns {
	return p.paymentOrderDo.Columns(cols...)
}

func (p *paymentOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *paymentOrder) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 16)
	p.fieldMap["id"] = p.ID
	p.fieldMap["status"] = p.Status
	p.fieldMap["orderNo"] = p.OrderNo
	p.fieldMap["user_id"] = p.UserID
	p.fieldMap["type"] = p.Type
	p.fieldMap["title"] = p.Title
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["version"] = p.Version
	p.fieldMap["pay_type"] = p.PayType
	p.fieldMap["pay_time"] = p.PayTime
	p.fieldMap["expired_time"] = p.ExpiredTime
	p.fieldMap["img_url"] = p.ImgURL
	p.fieldMap["price"] = p.Price
	p.fieldMap["pay_id"] = p.PayID
}

func (p paymentOrder) clone(db *gorm.DB) paymentOrder {
	p.paymentOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p paymentOrder) replaceDB(db *gorm.DB) paymentOrder {
	p.paymentOrderDo.ReplaceDB(db)
	return p
}

type paymentOrderDo struct{ gen.DO }

type IPaymentOrderDo interface {
	gen.SubQuery
	Debug() IPaymentOrderDo
	WithContext(ctx context.Context) IPaymentOrderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPaymentOrderDo
	WriteDB() IPaymentOrderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPaymentOrderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPaymentOrderDo
	Not(conds ...gen.Condition) IPaymentOrderDo
	Or(conds ...gen.Condition) IPaymentOrderDo
	Select(conds ...field.Expr) IPaymentOrderDo
	Where(conds ...gen.Condition) IPaymentOrderDo
	Order(conds ...field.Expr) IPaymentOrderDo
	Distinct(cols ...field.Expr) IPaymentOrderDo
	Omit(cols ...field.Expr) IPaymentOrderDo
	Join(table schema.Tabler, on ...field.Expr) IPaymentOrderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentOrderDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPaymentOrderDo
	Group(cols ...field.Expr) IPaymentOrderDo
	Having(conds ...gen.Condition) IPaymentOrderDo
	Limit(limit int) IPaymentOrderDo
	Offset(offset int) IPaymentOrderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentOrderDo
	Unscoped() IPaymentOrderDo
	Create(values ...*model.PaymentOrder) error
	CreateInBatches(values []*model.PaymentOrder, batchSize int) error
	Save(values ...*model.PaymentOrder) error
	First() (*model.PaymentOrder, error)
	Take() (*model.PaymentOrder, error)
	Last() (*model.PaymentOrder, error)
	Find() ([]*model.PaymentOrder, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentOrder, err error)
	FindInBatches(result *[]*model.PaymentOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PaymentOrder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPaymentOrderDo
	Assign(attrs ...field.AssignExpr) IPaymentOrderDo
	Joins(fields ...field.RelationField) IPaymentOrderDo
	Preload(fields ...field.RelationField) IPaymentOrderDo
	FirstOrInit() (*model.PaymentOrder, error)
	FirstOrCreate() (*model.PaymentOrder, error)
	FindByPage(offset int, limit int) (result []*model.PaymentOrder, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPaymentOrderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p paymentOrderDo) Debug() IPaymentOrderDo {
	return p.withDO(p.DO.Debug())
}

func (p paymentOrderDo) WithContext(ctx context.Context) IPaymentOrderDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p paymentOrderDo) ReadDB() IPaymentOrderDo {
	return p.Clauses(dbresolver.Read)
}

func (p paymentOrderDo) WriteDB() IPaymentOrderDo {
	return p.Clauses(dbresolver.Write)
}

func (p paymentOrderDo) Session(config *gorm.Session) IPaymentOrderDo {
	return p.withDO(p.DO.Session(config))
}

func (p paymentOrderDo) Clauses(conds ...clause.Expression) IPaymentOrderDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p paymentOrderDo) Returning(value interface{}, columns ...string) IPaymentOrderDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p paymentOrderDo) Not(conds ...gen.Condition) IPaymentOrderDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p paymentOrderDo) Or(conds ...gen.Condition) IPaymentOrderDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p paymentOrderDo) Select(conds ...field.Expr) IPaymentOrderDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p paymentOrderDo) Where(conds ...gen.Condition) IPaymentOrderDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p paymentOrderDo) Order(conds ...field.Expr) IPaymentOrderDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p paymentOrderDo) Distinct(cols ...field.Expr) IPaymentOrderDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p paymentOrderDo) Omit(cols ...field.Expr) IPaymentOrderDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p paymentOrderDo) Join(table schema.Tabler, on ...field.Expr) IPaymentOrderDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p paymentOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentOrderDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p paymentOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) IPaymentOrderDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p paymentOrderDo) Group(cols ...field.Expr) IPaymentOrderDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p paymentOrderDo) Having(conds ...gen.Condition) IPaymentOrderDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p paymentOrderDo) Limit(limit int) IPaymentOrderDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p paymentOrderDo) Offset(offset int) IPaymentOrderDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p paymentOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentOrderDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p paymentOrderDo) Unscoped() IPaymentOrderDo {
	return p.withDO(p.DO.Unscoped())
}

func (p paymentOrderDo) Create(values ...*model.PaymentOrder) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p paymentOrderDo) CreateInBatches(values []*model.PaymentOrder, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p paymentOrderDo) Save(values ...*model.PaymentOrder) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p paymentOrderDo) First() (*model.PaymentOrder, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrder), nil
	}
}

func (p paymentOrderDo) Take() (*model.PaymentOrder, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrder), nil
	}
}

func (p paymentOrderDo) Last() (*model.PaymentOrder, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrder), nil
	}
}

func (p paymentOrderDo) Find() ([]*model.PaymentOrder, error) {
	result, err := p.DO.Find()
	return result.([]*model.PaymentOrder), err
}

func (p paymentOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentOrder, err error) {
	buf := make([]*model.PaymentOrder, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p paymentOrderDo) FindInBatches(result *[]*model.PaymentOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p paymentOrderDo) Attrs(attrs ...field.AssignExpr) IPaymentOrderDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p paymentOrderDo) Assign(attrs ...field.AssignExpr) IPaymentOrderDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p paymentOrderDo) Joins(fields ...field.RelationField) IPaymentOrderDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p paymentOrderDo) Preload(fields ...field.RelationField) IPaymentOrderDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p paymentOrderDo) FirstOrInit() (*model.PaymentOrder, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrder), nil
	}
}

func (p paymentOrderDo) FirstOrCreate() (*model.PaymentOrder, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrder), nil
	}
}

func (p paymentOrderDo) FindByPage(offset int, limit int) (result []*model.PaymentOrder, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p paymentOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p paymentOrderDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p paymentOrderDo) Delete(models ...*model.PaymentOrder) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *paymentOrderDo) withDO(do gen.Dao) *paymentOrderDo {
	p.DO = *do.(*gen.DO)
	return p
}
