// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
)

func newPaymentOrderPrimary(db *gorm.DB, opts ...gen.DOOption) paymentOrderPrimary {
	_paymentOrderPrimary := paymentOrderPrimary{}

	_paymentOrderPrimary.paymentOrderPrimaryDo.UseDB(db, opts...)
	_paymentOrderPrimary.paymentOrderPrimaryDo.UseModel(&model.PaymentOrderPrimary{})

	tableName := _paymentOrderPrimary.paymentOrderPrimaryDo.TableName()
	_paymentOrderPrimary.ALL = field.NewAsterisk(tableName)
	_paymentOrderPrimary.ID = field.NewString(tableName, "id")
	_paymentOrderPrimary.OrderID = field.NewString(tableName, "order_id")
	_paymentOrderPrimary.UserID = field.NewString(tableName, "user_id")
	_paymentOrderPrimary.ProjectID = field.NewString(tableName, "project_id")
	_paymentOrderPrimary.CreatedAt = field.NewTime(tableName, "created_at")
	_paymentOrderPrimary.UpdatedAt = field.NewTime(tableName, "updated_at")
	_paymentOrderPrimary.DeletedAt = field.NewField(tableName, "deleted_at")
	_paymentOrderPrimary.Version = field.NewField(tableName, "version")
	_paymentOrderPrimary.NftID = field.NewString(tableName, "nft_id")
	_paymentOrderPrimary.Price = field.NewField(tableName, "price")

	_paymentOrderPrimary.fillFieldMap()

	return _paymentOrderPrimary
}

type paymentOrderPrimary struct {
	paymentOrderPrimaryDo paymentOrderPrimaryDo

	ALL       field.Asterisk
	ID        field.String // id
	OrderID   field.String // 订单id
	UserID    field.String // 用户id
	ProjectID field.String // 项目id
	CreatedAt field.Time   // 创建时间
	UpdatedAt field.Time   // 更新时间
	DeletedAt field.Field  // 软删除
	Version   field.Field  // 乐观锁
	NftID     field.String // 购买nft id
	Price     field.Field  // 价格

	fieldMap map[string]field.Expr
}

func (p paymentOrderPrimary) Table(newTableName string) *paymentOrderPrimary {
	p.paymentOrderPrimaryDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p paymentOrderPrimary) As(alias string) *paymentOrderPrimary {
	p.paymentOrderPrimaryDo.DO = *(p.paymentOrderPrimaryDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *paymentOrderPrimary) updateTableName(table string) *paymentOrderPrimary {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.OrderID = field.NewString(table, "order_id")
	p.UserID = field.NewString(table, "user_id")
	p.ProjectID = field.NewString(table, "project_id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.Version = field.NewField(table, "version")
	p.NftID = field.NewString(table, "nft_id")
	p.Price = field.NewField(table, "price")

	p.fillFieldMap()

	return p
}

func (p *paymentOrderPrimary) WithContext(ctx context.Context) IPaymentOrderPrimaryDo {
	return p.paymentOrderPrimaryDo.WithContext(ctx)
}

func (p paymentOrderPrimary) TableName() string { return p.paymentOrderPrimaryDo.TableName() }

func (p paymentOrderPrimary) Alias() string { return p.paymentOrderPrimaryDo.Alias() }

func (p paymentOrderPrimary) Columns(cols ...field.Expr) gen.Columns {
	return p.paymentOrderPrimaryDo.Columns(cols...)
}

func (p *paymentOrderPrimary) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *paymentOrderPrimary) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 10)
	p.fieldMap["id"] = p.ID
	p.fieldMap["order_id"] = p.OrderID
	p.fieldMap["user_id"] = p.UserID
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["version"] = p.Version
	p.fieldMap["nft_id"] = p.NftID
	p.fieldMap["price"] = p.Price
}

func (p paymentOrderPrimary) clone(db *gorm.DB) paymentOrderPrimary {
	p.paymentOrderPrimaryDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p paymentOrderPrimary) replaceDB(db *gorm.DB) paymentOrderPrimary {
	p.paymentOrderPrimaryDo.ReplaceDB(db)
	return p
}

type paymentOrderPrimaryDo struct{ gen.DO }

type IPaymentOrderPrimaryDo interface {
	gen.SubQuery
	Debug() IPaymentOrderPrimaryDo
	WithContext(ctx context.Context) IPaymentOrderPrimaryDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPaymentOrderPrimaryDo
	WriteDB() IPaymentOrderPrimaryDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPaymentOrderPrimaryDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPaymentOrderPrimaryDo
	Not(conds ...gen.Condition) IPaymentOrderPrimaryDo
	Or(conds ...gen.Condition) IPaymentOrderPrimaryDo
	Select(conds ...field.Expr) IPaymentOrderPrimaryDo
	Where(conds ...gen.Condition) IPaymentOrderPrimaryDo
	Order(conds ...field.Expr) IPaymentOrderPrimaryDo
	Distinct(cols ...field.Expr) IPaymentOrderPrimaryDo
	Omit(cols ...field.Expr) IPaymentOrderPrimaryDo
	Join(table schema.Tabler, on ...field.Expr) IPaymentOrderPrimaryDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentOrderPrimaryDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPaymentOrderPrimaryDo
	Group(cols ...field.Expr) IPaymentOrderPrimaryDo
	Having(conds ...gen.Condition) IPaymentOrderPrimaryDo
	Limit(limit int) IPaymentOrderPrimaryDo
	Offset(offset int) IPaymentOrderPrimaryDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentOrderPrimaryDo
	Unscoped() IPaymentOrderPrimaryDo
	Create(values ...*model.PaymentOrderPrimary) error
	CreateInBatches(values []*model.PaymentOrderPrimary, batchSize int) error
	Save(values ...*model.PaymentOrderPrimary) error
	First() (*model.PaymentOrderPrimary, error)
	Take() (*model.PaymentOrderPrimary, error)
	Last() (*model.PaymentOrderPrimary, error)
	Find() ([]*model.PaymentOrderPrimary, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentOrderPrimary, err error)
	FindInBatches(result *[]*model.PaymentOrderPrimary, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PaymentOrderPrimary) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPaymentOrderPrimaryDo
	Assign(attrs ...field.AssignExpr) IPaymentOrderPrimaryDo
	Joins(fields ...field.RelationField) IPaymentOrderPrimaryDo
	Preload(fields ...field.RelationField) IPaymentOrderPrimaryDo
	FirstOrInit() (*model.PaymentOrderPrimary, error)
	FirstOrCreate() (*model.PaymentOrderPrimary, error)
	FindByPage(offset int, limit int) (result []*model.PaymentOrderPrimary, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPaymentOrderPrimaryDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p paymentOrderPrimaryDo) Debug() IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Debug())
}

func (p paymentOrderPrimaryDo) WithContext(ctx context.Context) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p paymentOrderPrimaryDo) ReadDB() IPaymentOrderPrimaryDo {
	return p.Clauses(dbresolver.Read)
}

func (p paymentOrderPrimaryDo) WriteDB() IPaymentOrderPrimaryDo {
	return p.Clauses(dbresolver.Write)
}

func (p paymentOrderPrimaryDo) Session(config *gorm.Session) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Session(config))
}

func (p paymentOrderPrimaryDo) Clauses(conds ...clause.Expression) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p paymentOrderPrimaryDo) Returning(value interface{}, columns ...string) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p paymentOrderPrimaryDo) Not(conds ...gen.Condition) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p paymentOrderPrimaryDo) Or(conds ...gen.Condition) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p paymentOrderPrimaryDo) Select(conds ...field.Expr) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p paymentOrderPrimaryDo) Where(conds ...gen.Condition) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p paymentOrderPrimaryDo) Order(conds ...field.Expr) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p paymentOrderPrimaryDo) Distinct(cols ...field.Expr) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p paymentOrderPrimaryDo) Omit(cols ...field.Expr) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p paymentOrderPrimaryDo) Join(table schema.Tabler, on ...field.Expr) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p paymentOrderPrimaryDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p paymentOrderPrimaryDo) RightJoin(table schema.Tabler, on ...field.Expr) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p paymentOrderPrimaryDo) Group(cols ...field.Expr) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p paymentOrderPrimaryDo) Having(conds ...gen.Condition) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p paymentOrderPrimaryDo) Limit(limit int) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p paymentOrderPrimaryDo) Offset(offset int) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p paymentOrderPrimaryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p paymentOrderPrimaryDo) Unscoped() IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Unscoped())
}

func (p paymentOrderPrimaryDo) Create(values ...*model.PaymentOrderPrimary) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p paymentOrderPrimaryDo) CreateInBatches(values []*model.PaymentOrderPrimary, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p paymentOrderPrimaryDo) Save(values ...*model.PaymentOrderPrimary) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p paymentOrderPrimaryDo) First() (*model.PaymentOrderPrimary, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrderPrimary), nil
	}
}

func (p paymentOrderPrimaryDo) Take() (*model.PaymentOrderPrimary, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrderPrimary), nil
	}
}

func (p paymentOrderPrimaryDo) Last() (*model.PaymentOrderPrimary, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrderPrimary), nil
	}
}

func (p paymentOrderPrimaryDo) Find() ([]*model.PaymentOrderPrimary, error) {
	result, err := p.DO.Find()
	return result.([]*model.PaymentOrderPrimary), err
}

func (p paymentOrderPrimaryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentOrderPrimary, err error) {
	buf := make([]*model.PaymentOrderPrimary, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p paymentOrderPrimaryDo) FindInBatches(result *[]*model.PaymentOrderPrimary, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p paymentOrderPrimaryDo) Attrs(attrs ...field.AssignExpr) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p paymentOrderPrimaryDo) Assign(attrs ...field.AssignExpr) IPaymentOrderPrimaryDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p paymentOrderPrimaryDo) Joins(fields ...field.RelationField) IPaymentOrderPrimaryDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p paymentOrderPrimaryDo) Preload(fields ...field.RelationField) IPaymentOrderPrimaryDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p paymentOrderPrimaryDo) FirstOrInit() (*model.PaymentOrderPrimary, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrderPrimary), nil
	}
}

func (p paymentOrderPrimaryDo) FirstOrCreate() (*model.PaymentOrderPrimary, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrderPrimary), nil
	}
}

func (p paymentOrderPrimaryDo) FindByPage(offset int, limit int) (result []*model.PaymentOrderPrimary, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p paymentOrderPrimaryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p paymentOrderPrimaryDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p paymentOrderPrimaryDo) Delete(models ...*model.PaymentOrderPrimary) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *paymentOrderPrimaryDo) withDO(do gen.Dao) *paymentOrderPrimaryDo {
	p.DO = *do.(*gen.DO)
	return p
}
