// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
)

func newPaymentPayYeepay(db *gorm.DB, opts ...gen.DOOption) paymentPayYeepay {
	_paymentPayYeepay := paymentPayYeepay{}

	_paymentPayYeepay.paymentPayYeepayDo.UseDB(db, opts...)
	_paymentPayYeepay.paymentPayYeepayDo.UseModel(&model.PaymentPayYeepay{})

	tableName := _paymentPayYeepay.paymentPayYeepayDo.TableName()
	_paymentPayYeepay.ALL = field.NewAsterisk(tableName)
	_paymentPayYeepay.ID = field.NewString(tableName, "id")
	_paymentPayYeepay.UserID = field.NewString(tableName, "user_id")
	_paymentPayYeepay.OrderID = field.NewString(tableName, "order_id")
	_paymentPayYeepay.YeepayOrderNo = field.NewString(tableName, "yeepay_order_no")
	_paymentPayYeepay.PayAmount = field.NewField(tableName, "pay_amount")
	_paymentPayYeepay.MerchantFee = field.NewField(tableName, "merchant_fee")
	_paymentPayYeepay.UnSplitAmount = field.NewField(tableName, "un_split_amount")
	_paymentPayYeepay.CsAt = field.NewTime(tableName, "cs_at")
	_paymentPayYeepay.PayAt = field.NewTime(tableName, "pay_at")
	_paymentPayYeepay.PayURL = field.NewString(tableName, "pay_url")
	_paymentPayYeepay.PayWay = field.NewInt16(tableName, "pay_way")
	_paymentPayYeepay.DivideType = field.NewInt16(tableName, "divide_type")
	_paymentPayYeepay.CreatedAt = field.NewTime(tableName, "created_at")
	_paymentPayYeepay.UpdatedAt = field.NewTime(tableName, "updated_at")
	_paymentPayYeepay.DeletedAt = field.NewField(tableName, "deleted_at")
	_paymentPayYeepay.Version = field.NewField(tableName, "version")
	_paymentPayYeepay.DivideStatus = field.NewInt16(tableName, "divide_status")

	_paymentPayYeepay.fillFieldMap()

	return _paymentPayYeepay
}

type paymentPayYeepay struct {
	paymentPayYeepayDo paymentPayYeepayDo

	ALL           field.Asterisk
	ID            field.String // id
	UserID        field.String // 平台用户id
	OrderID       field.String // 订单id
	YeepayOrderNo field.String // 第三方订单号
	PayAmount     field.Field  // 交易金额
	MerchantFee   field.Field  // 商户手续费
	UnSplitAmount field.Field  // 剩余可分账金额
	CsAt          field.Time   // 清算时间
	PayAt         field.Time   // 支付时间
	PayURL        field.String // 支付链接
	PayWay        field.Int16  // 支付方式
	DivideType    field.Int16  // 分账类型
	CreatedAt     field.Time   // 创建时间
	UpdatedAt     field.Time   // 更新时间
	DeletedAt     field.Field  // 软删除
	Version       field.Field  // 乐观锁
	DivideStatus  field.Int16  // 分账状态

	fieldMap map[string]field.Expr
}

func (p paymentPayYeepay) Table(newTableName string) *paymentPayYeepay {
	p.paymentPayYeepayDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p paymentPayYeepay) As(alias string) *paymentPayYeepay {
	p.paymentPayYeepayDo.DO = *(p.paymentPayYeepayDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *paymentPayYeepay) updateTableName(table string) *paymentPayYeepay {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.UserID = field.NewString(table, "user_id")
	p.OrderID = field.NewString(table, "order_id")
	p.YeepayOrderNo = field.NewString(table, "yeepay_order_no")
	p.PayAmount = field.NewField(table, "pay_amount")
	p.MerchantFee = field.NewField(table, "merchant_fee")
	p.UnSplitAmount = field.NewField(table, "un_split_amount")
	p.CsAt = field.NewTime(table, "cs_at")
	p.PayAt = field.NewTime(table, "pay_at")
	p.PayURL = field.NewString(table, "pay_url")
	p.PayWay = field.NewInt16(table, "pay_way")
	p.DivideType = field.NewInt16(table, "divide_type")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.Version = field.NewField(table, "version")
	p.DivideStatus = field.NewInt16(table, "divide_status")

	p.fillFieldMap()

	return p
}

func (p *paymentPayYeepay) WithContext(ctx context.Context) IPaymentPayYeepayDo {
	return p.paymentPayYeepayDo.WithContext(ctx)
}

func (p paymentPayYeepay) TableName() string { return p.paymentPayYeepayDo.TableName() }

func (p paymentPayYeepay) Alias() string { return p.paymentPayYeepayDo.Alias() }

func (p paymentPayYeepay) Columns(cols ...field.Expr) gen.Columns {
	return p.paymentPayYeepayDo.Columns(cols...)
}

func (p *paymentPayYeepay) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *paymentPayYeepay) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 17)
	p.fieldMap["id"] = p.ID
	p.fieldMap["user_id"] = p.UserID
	p.fieldMap["order_id"] = p.OrderID
	p.fieldMap["yeepay_order_no"] = p.YeepayOrderNo
	p.fieldMap["pay_amount"] = p.PayAmount
	p.fieldMap["merchant_fee"] = p.MerchantFee
	p.fieldMap["un_split_amount"] = p.UnSplitAmount
	p.fieldMap["cs_at"] = p.CsAt
	p.fieldMap["pay_at"] = p.PayAt
	p.fieldMap["pay_url"] = p.PayURL
	p.fieldMap["pay_way"] = p.PayWay
	p.fieldMap["divide_type"] = p.DivideType
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["version"] = p.Version
	p.fieldMap["divide_status"] = p.DivideStatus
}

func (p paymentPayYeepay) clone(db *gorm.DB) paymentPayYeepay {
	p.paymentPayYeepayDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p paymentPayYeepay) replaceDB(db *gorm.DB) paymentPayYeepay {
	p.paymentPayYeepayDo.ReplaceDB(db)
	return p
}

type paymentPayYeepayDo struct{ gen.DO }

type IPaymentPayYeepayDo interface {
	gen.SubQuery
	Debug() IPaymentPayYeepayDo
	WithContext(ctx context.Context) IPaymentPayYeepayDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPaymentPayYeepayDo
	WriteDB() IPaymentPayYeepayDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPaymentPayYeepayDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPaymentPayYeepayDo
	Not(conds ...gen.Condition) IPaymentPayYeepayDo
	Or(conds ...gen.Condition) IPaymentPayYeepayDo
	Select(conds ...field.Expr) IPaymentPayYeepayDo
	Where(conds ...gen.Condition) IPaymentPayYeepayDo
	Order(conds ...field.Expr) IPaymentPayYeepayDo
	Distinct(cols ...field.Expr) IPaymentPayYeepayDo
	Omit(cols ...field.Expr) IPaymentPayYeepayDo
	Join(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayDo
	Group(cols ...field.Expr) IPaymentPayYeepayDo
	Having(conds ...gen.Condition) IPaymentPayYeepayDo
	Limit(limit int) IPaymentPayYeepayDo
	Offset(offset int) IPaymentPayYeepayDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentPayYeepayDo
	Unscoped() IPaymentPayYeepayDo
	Create(values ...*model.PaymentPayYeepay) error
	CreateInBatches(values []*model.PaymentPayYeepay, batchSize int) error
	Save(values ...*model.PaymentPayYeepay) error
	First() (*model.PaymentPayYeepay, error)
	Take() (*model.PaymentPayYeepay, error)
	Last() (*model.PaymentPayYeepay, error)
	Find() ([]*model.PaymentPayYeepay, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentPayYeepay, err error)
	FindInBatches(result *[]*model.PaymentPayYeepay, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PaymentPayYeepay) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPaymentPayYeepayDo
	Assign(attrs ...field.AssignExpr) IPaymentPayYeepayDo
	Joins(fields ...field.RelationField) IPaymentPayYeepayDo
	Preload(fields ...field.RelationField) IPaymentPayYeepayDo
	FirstOrInit() (*model.PaymentPayYeepay, error)
	FirstOrCreate() (*model.PaymentPayYeepay, error)
	FindByPage(offset int, limit int) (result []*model.PaymentPayYeepay, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPaymentPayYeepayDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p paymentPayYeepayDo) Debug() IPaymentPayYeepayDo {
	return p.withDO(p.DO.Debug())
}

func (p paymentPayYeepayDo) WithContext(ctx context.Context) IPaymentPayYeepayDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p paymentPayYeepayDo) ReadDB() IPaymentPayYeepayDo {
	return p.Clauses(dbresolver.Read)
}

func (p paymentPayYeepayDo) WriteDB() IPaymentPayYeepayDo {
	return p.Clauses(dbresolver.Write)
}

func (p paymentPayYeepayDo) Session(config *gorm.Session) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Session(config))
}

func (p paymentPayYeepayDo) Clauses(conds ...clause.Expression) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p paymentPayYeepayDo) Returning(value interface{}, columns ...string) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p paymentPayYeepayDo) Not(conds ...gen.Condition) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p paymentPayYeepayDo) Or(conds ...gen.Condition) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p paymentPayYeepayDo) Select(conds ...field.Expr) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p paymentPayYeepayDo) Where(conds ...gen.Condition) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p paymentPayYeepayDo) Order(conds ...field.Expr) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p paymentPayYeepayDo) Distinct(cols ...field.Expr) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p paymentPayYeepayDo) Omit(cols ...field.Expr) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p paymentPayYeepayDo) Join(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p paymentPayYeepayDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p paymentPayYeepayDo) RightJoin(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p paymentPayYeepayDo) Group(cols ...field.Expr) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p paymentPayYeepayDo) Having(conds ...gen.Condition) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p paymentPayYeepayDo) Limit(limit int) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p paymentPayYeepayDo) Offset(offset int) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p paymentPayYeepayDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p paymentPayYeepayDo) Unscoped() IPaymentPayYeepayDo {
	return p.withDO(p.DO.Unscoped())
}

func (p paymentPayYeepayDo) Create(values ...*model.PaymentPayYeepay) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p paymentPayYeepayDo) CreateInBatches(values []*model.PaymentPayYeepay, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p paymentPayYeepayDo) Save(values ...*model.PaymentPayYeepay) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p paymentPayYeepayDo) First() (*model.PaymentPayYeepay, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepay), nil
	}
}

func (p paymentPayYeepayDo) Take() (*model.PaymentPayYeepay, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepay), nil
	}
}

func (p paymentPayYeepayDo) Last() (*model.PaymentPayYeepay, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepay), nil
	}
}

func (p paymentPayYeepayDo) Find() ([]*model.PaymentPayYeepay, error) {
	result, err := p.DO.Find()
	return result.([]*model.PaymentPayYeepay), err
}

func (p paymentPayYeepayDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentPayYeepay, err error) {
	buf := make([]*model.PaymentPayYeepay, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p paymentPayYeepayDo) FindInBatches(result *[]*model.PaymentPayYeepay, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p paymentPayYeepayDo) Attrs(attrs ...field.AssignExpr) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p paymentPayYeepayDo) Assign(attrs ...field.AssignExpr) IPaymentPayYeepayDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p paymentPayYeepayDo) Joins(fields ...field.RelationField) IPaymentPayYeepayDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p paymentPayYeepayDo) Preload(fields ...field.RelationField) IPaymentPayYeepayDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p paymentPayYeepayDo) FirstOrInit() (*model.PaymentPayYeepay, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepay), nil
	}
}

func (p paymentPayYeepayDo) FirstOrCreate() (*model.PaymentPayYeepay, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepay), nil
	}
}

func (p paymentPayYeepayDo) FindByPage(offset int, limit int) (result []*model.PaymentPayYeepay, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p paymentPayYeepayDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p paymentPayYeepayDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p paymentPayYeepayDo) Delete(models ...*model.PaymentPayYeepay) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *paymentPayYeepayDo) withDO(do gen.Dao) *paymentPayYeepayDo {
	p.DO = *do.(*gen.DO)
	return p
}
