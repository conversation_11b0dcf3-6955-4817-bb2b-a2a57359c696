// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
)

func newPaymentOrderSecondary(db *gorm.DB, opts ...gen.DOOption) paymentOrderSecondary {
	_paymentOrderSecondary := paymentOrderSecondary{}

	_paymentOrderSecondary.paymentOrderSecondaryDo.UseDB(db, opts...)
	_paymentOrderSecondary.paymentOrderSecondaryDo.UseModel(&model.PaymentOrderSecondary{})

	tableName := _paymentOrderSecondary.paymentOrderSecondaryDo.TableName()
	_paymentOrderSecondary.ALL = field.NewAsterisk(tableName)
	_paymentOrderSecondary.ID = field.NewString(tableName, "id")
	_paymentOrderSecondary.OrderID = field.NewString(tableName, "order_id")
	_paymentOrderSecondary.UserID = field.NewString(tableName, "user_id")
	_paymentOrderSecondary.ProjectID = field.NewString(tableName, "project_id")
	_paymentOrderSecondary.CreatedAt = field.NewTime(tableName, "created_at")
	_paymentOrderSecondary.UpdatedAt = field.NewTime(tableName, "updated_at")
	_paymentOrderSecondary.DeletedAt = field.NewField(tableName, "deleted_at")
	_paymentOrderSecondary.Version = field.NewField(tableName, "version")
	_paymentOrderSecondary.NftID = field.NewString(tableName, "nft_id")
	_paymentOrderSecondary.Price = field.NewField(tableName, "price")
	_paymentOrderSecondary.ListingID = field.NewString(tableName, "listing_id")
	_paymentOrderSecondary.SellerID = field.NewString(tableName, "seller_id")

	_paymentOrderSecondary.fillFieldMap()

	return _paymentOrderSecondary
}

type paymentOrderSecondary struct {
	paymentOrderSecondaryDo paymentOrderSecondaryDo

	ALL       field.Asterisk
	ID        field.String // id
	OrderID   field.String // 订单id
	UserID    field.String // 用户id
	ProjectID field.String // 项目id
	CreatedAt field.Time   // 创建时间
	UpdatedAt field.Time   // 更新时间
	DeletedAt field.Field  // 软删除
	Version   field.Field  // 乐观锁
	NftID     field.String // 购买nft id
	Price     field.Field  // 价格
	ListingID field.String // 市场在售商品id
	SellerID  field.String // 售卖者id

	fieldMap map[string]field.Expr
}

func (p paymentOrderSecondary) Table(newTableName string) *paymentOrderSecondary {
	p.paymentOrderSecondaryDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p paymentOrderSecondary) As(alias string) *paymentOrderSecondary {
	p.paymentOrderSecondaryDo.DO = *(p.paymentOrderSecondaryDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *paymentOrderSecondary) updateTableName(table string) *paymentOrderSecondary {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.OrderID = field.NewString(table, "order_id")
	p.UserID = field.NewString(table, "user_id")
	p.ProjectID = field.NewString(table, "project_id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.Version = field.NewField(table, "version")
	p.NftID = field.NewString(table, "nft_id")
	p.Price = field.NewField(table, "price")
	p.ListingID = field.NewString(table, "listing_id")
	p.SellerID = field.NewString(table, "seller_id")

	p.fillFieldMap()

	return p
}

func (p *paymentOrderSecondary) WithContext(ctx context.Context) IPaymentOrderSecondaryDo {
	return p.paymentOrderSecondaryDo.WithContext(ctx)
}

func (p paymentOrderSecondary) TableName() string { return p.paymentOrderSecondaryDo.TableName() }

func (p paymentOrderSecondary) Alias() string { return p.paymentOrderSecondaryDo.Alias() }

func (p paymentOrderSecondary) Columns(cols ...field.Expr) gen.Columns {
	return p.paymentOrderSecondaryDo.Columns(cols...)
}

func (p *paymentOrderSecondary) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *paymentOrderSecondary) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 12)
	p.fieldMap["id"] = p.ID
	p.fieldMap["order_id"] = p.OrderID
	p.fieldMap["user_id"] = p.UserID
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["version"] = p.Version
	p.fieldMap["nft_id"] = p.NftID
	p.fieldMap["price"] = p.Price
	p.fieldMap["listing_id"] = p.ListingID
	p.fieldMap["seller_id"] = p.SellerID
}

func (p paymentOrderSecondary) clone(db *gorm.DB) paymentOrderSecondary {
	p.paymentOrderSecondaryDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p paymentOrderSecondary) replaceDB(db *gorm.DB) paymentOrderSecondary {
	p.paymentOrderSecondaryDo.ReplaceDB(db)
	return p
}

type paymentOrderSecondaryDo struct{ gen.DO }

type IPaymentOrderSecondaryDo interface {
	gen.SubQuery
	Debug() IPaymentOrderSecondaryDo
	WithContext(ctx context.Context) IPaymentOrderSecondaryDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPaymentOrderSecondaryDo
	WriteDB() IPaymentOrderSecondaryDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPaymentOrderSecondaryDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPaymentOrderSecondaryDo
	Not(conds ...gen.Condition) IPaymentOrderSecondaryDo
	Or(conds ...gen.Condition) IPaymentOrderSecondaryDo
	Select(conds ...field.Expr) IPaymentOrderSecondaryDo
	Where(conds ...gen.Condition) IPaymentOrderSecondaryDo
	Order(conds ...field.Expr) IPaymentOrderSecondaryDo
	Distinct(cols ...field.Expr) IPaymentOrderSecondaryDo
	Omit(cols ...field.Expr) IPaymentOrderSecondaryDo
	Join(table schema.Tabler, on ...field.Expr) IPaymentOrderSecondaryDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentOrderSecondaryDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPaymentOrderSecondaryDo
	Group(cols ...field.Expr) IPaymentOrderSecondaryDo
	Having(conds ...gen.Condition) IPaymentOrderSecondaryDo
	Limit(limit int) IPaymentOrderSecondaryDo
	Offset(offset int) IPaymentOrderSecondaryDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentOrderSecondaryDo
	Unscoped() IPaymentOrderSecondaryDo
	Create(values ...*model.PaymentOrderSecondary) error
	CreateInBatches(values []*model.PaymentOrderSecondary, batchSize int) error
	Save(values ...*model.PaymentOrderSecondary) error
	First() (*model.PaymentOrderSecondary, error)
	Take() (*model.PaymentOrderSecondary, error)
	Last() (*model.PaymentOrderSecondary, error)
	Find() ([]*model.PaymentOrderSecondary, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentOrderSecondary, err error)
	FindInBatches(result *[]*model.PaymentOrderSecondary, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PaymentOrderSecondary) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPaymentOrderSecondaryDo
	Assign(attrs ...field.AssignExpr) IPaymentOrderSecondaryDo
	Joins(fields ...field.RelationField) IPaymentOrderSecondaryDo
	Preload(fields ...field.RelationField) IPaymentOrderSecondaryDo
	FirstOrInit() (*model.PaymentOrderSecondary, error)
	FirstOrCreate() (*model.PaymentOrderSecondary, error)
	FindByPage(offset int, limit int) (result []*model.PaymentOrderSecondary, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPaymentOrderSecondaryDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p paymentOrderSecondaryDo) Debug() IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Debug())
}

func (p paymentOrderSecondaryDo) WithContext(ctx context.Context) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p paymentOrderSecondaryDo) ReadDB() IPaymentOrderSecondaryDo {
	return p.Clauses(dbresolver.Read)
}

func (p paymentOrderSecondaryDo) WriteDB() IPaymentOrderSecondaryDo {
	return p.Clauses(dbresolver.Write)
}

func (p paymentOrderSecondaryDo) Session(config *gorm.Session) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Session(config))
}

func (p paymentOrderSecondaryDo) Clauses(conds ...clause.Expression) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p paymentOrderSecondaryDo) Returning(value interface{}, columns ...string) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p paymentOrderSecondaryDo) Not(conds ...gen.Condition) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p paymentOrderSecondaryDo) Or(conds ...gen.Condition) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p paymentOrderSecondaryDo) Select(conds ...field.Expr) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p paymentOrderSecondaryDo) Where(conds ...gen.Condition) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p paymentOrderSecondaryDo) Order(conds ...field.Expr) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p paymentOrderSecondaryDo) Distinct(cols ...field.Expr) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p paymentOrderSecondaryDo) Omit(cols ...field.Expr) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p paymentOrderSecondaryDo) Join(table schema.Tabler, on ...field.Expr) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p paymentOrderSecondaryDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p paymentOrderSecondaryDo) RightJoin(table schema.Tabler, on ...field.Expr) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p paymentOrderSecondaryDo) Group(cols ...field.Expr) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p paymentOrderSecondaryDo) Having(conds ...gen.Condition) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p paymentOrderSecondaryDo) Limit(limit int) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p paymentOrderSecondaryDo) Offset(offset int) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p paymentOrderSecondaryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p paymentOrderSecondaryDo) Unscoped() IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Unscoped())
}

func (p paymentOrderSecondaryDo) Create(values ...*model.PaymentOrderSecondary) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p paymentOrderSecondaryDo) CreateInBatches(values []*model.PaymentOrderSecondary, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p paymentOrderSecondaryDo) Save(values ...*model.PaymentOrderSecondary) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p paymentOrderSecondaryDo) First() (*model.PaymentOrderSecondary, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrderSecondary), nil
	}
}

func (p paymentOrderSecondaryDo) Take() (*model.PaymentOrderSecondary, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrderSecondary), nil
	}
}

func (p paymentOrderSecondaryDo) Last() (*model.PaymentOrderSecondary, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrderSecondary), nil
	}
}

func (p paymentOrderSecondaryDo) Find() ([]*model.PaymentOrderSecondary, error) {
	result, err := p.DO.Find()
	return result.([]*model.PaymentOrderSecondary), err
}

func (p paymentOrderSecondaryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentOrderSecondary, err error) {
	buf := make([]*model.PaymentOrderSecondary, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p paymentOrderSecondaryDo) FindInBatches(result *[]*model.PaymentOrderSecondary, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p paymentOrderSecondaryDo) Attrs(attrs ...field.AssignExpr) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p paymentOrderSecondaryDo) Assign(attrs ...field.AssignExpr) IPaymentOrderSecondaryDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p paymentOrderSecondaryDo) Joins(fields ...field.RelationField) IPaymentOrderSecondaryDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p paymentOrderSecondaryDo) Preload(fields ...field.RelationField) IPaymentOrderSecondaryDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p paymentOrderSecondaryDo) FirstOrInit() (*model.PaymentOrderSecondary, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrderSecondary), nil
	}
}

func (p paymentOrderSecondaryDo) FirstOrCreate() (*model.PaymentOrderSecondary, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentOrderSecondary), nil
	}
}

func (p paymentOrderSecondaryDo) FindByPage(offset int, limit int) (result []*model.PaymentOrderSecondary, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p paymentOrderSecondaryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p paymentOrderSecondaryDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p paymentOrderSecondaryDo) Delete(models ...*model.PaymentOrderSecondary) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *paymentOrderSecondaryDo) withDO(do gen.Dao) *paymentOrderSecondaryDo {
	p.DO = *do.(*gen.DO)
	return p
}
