// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/divide-service/gen/gen/model"
)

func newPaymentPayYeepayDivide(db *gorm.DB, opts ...gen.DOOption) paymentPayYeepayDivide {
	_paymentPayYeepayDivide := paymentPayYeepayDivide{}

	_paymentPayYeepayDivide.paymentPayYeepayDivideDo.UseDB(db, opts...)
	_paymentPayYeepayDivide.paymentPayYeepayDivideDo.UseModel(&model.PaymentPayYeepayDivide{})

	tableName := _paymentPayYeepayDivide.paymentPayYeepayDivideDo.TableName()
	_paymentPayYeepayDivide.ALL = field.NewAsterisk(tableName)
	_paymentPayYeepayDivide.ID = field.NewString(tableName, "id")
	_paymentPayYeepayDivide.MemberID = field.NewString(tableName, "member_id")
	_paymentPayYeepayDivide.MemberType = field.NewInt16(tableName, "member_type")
	_paymentPayYeepayDivide.RequestNo = field.NewString(tableName, "request_no")
	_paymentPayYeepayDivide.OrderID = field.NewString(tableName, "order_id")
	_paymentPayYeepayDivide.Amount = field.NewField(tableName, "amount")
	_paymentPayYeepayDivide.CreatedAt = field.NewTime(tableName, "created_at")
	_paymentPayYeepayDivide.UpdatedAt = field.NewTime(tableName, "updated_at")
	_paymentPayYeepayDivide.DeletedAt = field.NewField(tableName, "deleted_at")
	_paymentPayYeepayDivide.Version = field.NewField(tableName, "version")
	_paymentPayYeepayDivide.OrderSecondaryExtraID = field.NewString(tableName, "order_secondary_extra_id")

	_paymentPayYeepayDivide.fillFieldMap()

	return _paymentPayYeepayDivide
}

type paymentPayYeepayDivide struct {
	paymentPayYeepayDivideDo paymentPayYeepayDivideDo

	ALL                   field.Asterisk
	ID                    field.String // id
	MemberID              field.String // 分账易宝用户 Member对象 的 id；若是商户本身时，传入0
	MemberType            field.Int16  // 商户类型[1:项目方, 2:卖家用户]
	RequestNo             field.String // 分账流水号,幂等
	OrderID               field.String // 订单id
	Amount                field.Field  // 分账金额
	CreatedAt             field.Time   // 创建时间
	UpdatedAt             field.Time   // 更新时间
	DeletedAt             field.Field  // 软删除
	Version               field.Field  // 乐观锁
	OrderSecondaryExtraID field.String // 订单二级额外信息表id

	fieldMap map[string]field.Expr
}

func (p paymentPayYeepayDivide) Table(newTableName string) *paymentPayYeepayDivide {
	p.paymentPayYeepayDivideDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p paymentPayYeepayDivide) As(alias string) *paymentPayYeepayDivide {
	p.paymentPayYeepayDivideDo.DO = *(p.paymentPayYeepayDivideDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *paymentPayYeepayDivide) updateTableName(table string) *paymentPayYeepayDivide {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.MemberID = field.NewString(table, "member_id")
	p.MemberType = field.NewInt16(table, "member_type")
	p.RequestNo = field.NewString(table, "request_no")
	p.OrderID = field.NewString(table, "order_id")
	p.Amount = field.NewField(table, "amount")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.Version = field.NewField(table, "version")
	p.OrderSecondaryExtraID = field.NewString(table, "order_secondary_extra_id")

	p.fillFieldMap()

	return p
}

func (p *paymentPayYeepayDivide) WithContext(ctx context.Context) IPaymentPayYeepayDivideDo {
	return p.paymentPayYeepayDivideDo.WithContext(ctx)
}

func (p paymentPayYeepayDivide) TableName() string { return p.paymentPayYeepayDivideDo.TableName() }

func (p paymentPayYeepayDivide) Alias() string { return p.paymentPayYeepayDivideDo.Alias() }

func (p paymentPayYeepayDivide) Columns(cols ...field.Expr) gen.Columns {
	return p.paymentPayYeepayDivideDo.Columns(cols...)
}

func (p *paymentPayYeepayDivide) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *paymentPayYeepayDivide) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 11)
	p.fieldMap["id"] = p.ID
	p.fieldMap["member_id"] = p.MemberID
	p.fieldMap["member_type"] = p.MemberType
	p.fieldMap["request_no"] = p.RequestNo
	p.fieldMap["order_id"] = p.OrderID
	p.fieldMap["amount"] = p.Amount
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["version"] = p.Version
	p.fieldMap["order_secondary_extra_id"] = p.OrderSecondaryExtraID
}

func (p paymentPayYeepayDivide) clone(db *gorm.DB) paymentPayYeepayDivide {
	p.paymentPayYeepayDivideDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p paymentPayYeepayDivide) replaceDB(db *gorm.DB) paymentPayYeepayDivide {
	p.paymentPayYeepayDivideDo.ReplaceDB(db)
	return p
}

type paymentPayYeepayDivideDo struct{ gen.DO }

type IPaymentPayYeepayDivideDo interface {
	gen.SubQuery
	Debug() IPaymentPayYeepayDivideDo
	WithContext(ctx context.Context) IPaymentPayYeepayDivideDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPaymentPayYeepayDivideDo
	WriteDB() IPaymentPayYeepayDivideDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPaymentPayYeepayDivideDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPaymentPayYeepayDivideDo
	Not(conds ...gen.Condition) IPaymentPayYeepayDivideDo
	Or(conds ...gen.Condition) IPaymentPayYeepayDivideDo
	Select(conds ...field.Expr) IPaymentPayYeepayDivideDo
	Where(conds ...gen.Condition) IPaymentPayYeepayDivideDo
	Order(conds ...field.Expr) IPaymentPayYeepayDivideDo
	Distinct(cols ...field.Expr) IPaymentPayYeepayDivideDo
	Omit(cols ...field.Expr) IPaymentPayYeepayDivideDo
	Join(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayDivideDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayDivideDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayDivideDo
	Group(cols ...field.Expr) IPaymentPayYeepayDivideDo
	Having(conds ...gen.Condition) IPaymentPayYeepayDivideDo
	Limit(limit int) IPaymentPayYeepayDivideDo
	Offset(offset int) IPaymentPayYeepayDivideDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentPayYeepayDivideDo
	Unscoped() IPaymentPayYeepayDivideDo
	Create(values ...*model.PaymentPayYeepayDivide) error
	CreateInBatches(values []*model.PaymentPayYeepayDivide, batchSize int) error
	Save(values ...*model.PaymentPayYeepayDivide) error
	First() (*model.PaymentPayYeepayDivide, error)
	Take() (*model.PaymentPayYeepayDivide, error)
	Last() (*model.PaymentPayYeepayDivide, error)
	Find() ([]*model.PaymentPayYeepayDivide, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentPayYeepayDivide, err error)
	FindInBatches(result *[]*model.PaymentPayYeepayDivide, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PaymentPayYeepayDivide) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPaymentPayYeepayDivideDo
	Assign(attrs ...field.AssignExpr) IPaymentPayYeepayDivideDo
	Joins(fields ...field.RelationField) IPaymentPayYeepayDivideDo
	Preload(fields ...field.RelationField) IPaymentPayYeepayDivideDo
	FirstOrInit() (*model.PaymentPayYeepayDivide, error)
	FirstOrCreate() (*model.PaymentPayYeepayDivide, error)
	FindByPage(offset int, limit int) (result []*model.PaymentPayYeepayDivide, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPaymentPayYeepayDivideDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p paymentPayYeepayDivideDo) Debug() IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Debug())
}

func (p paymentPayYeepayDivideDo) WithContext(ctx context.Context) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p paymentPayYeepayDivideDo) ReadDB() IPaymentPayYeepayDivideDo {
	return p.Clauses(dbresolver.Read)
}

func (p paymentPayYeepayDivideDo) WriteDB() IPaymentPayYeepayDivideDo {
	return p.Clauses(dbresolver.Write)
}

func (p paymentPayYeepayDivideDo) Session(config *gorm.Session) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Session(config))
}

func (p paymentPayYeepayDivideDo) Clauses(conds ...clause.Expression) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p paymentPayYeepayDivideDo) Returning(value interface{}, columns ...string) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p paymentPayYeepayDivideDo) Not(conds ...gen.Condition) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p paymentPayYeepayDivideDo) Or(conds ...gen.Condition) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p paymentPayYeepayDivideDo) Select(conds ...field.Expr) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p paymentPayYeepayDivideDo) Where(conds ...gen.Condition) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p paymentPayYeepayDivideDo) Order(conds ...field.Expr) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p paymentPayYeepayDivideDo) Distinct(cols ...field.Expr) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p paymentPayYeepayDivideDo) Omit(cols ...field.Expr) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p paymentPayYeepayDivideDo) Join(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p paymentPayYeepayDivideDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p paymentPayYeepayDivideDo) RightJoin(table schema.Tabler, on ...field.Expr) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p paymentPayYeepayDivideDo) Group(cols ...field.Expr) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p paymentPayYeepayDivideDo) Having(conds ...gen.Condition) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p paymentPayYeepayDivideDo) Limit(limit int) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p paymentPayYeepayDivideDo) Offset(offset int) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p paymentPayYeepayDivideDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p paymentPayYeepayDivideDo) Unscoped() IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Unscoped())
}

func (p paymentPayYeepayDivideDo) Create(values ...*model.PaymentPayYeepayDivide) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p paymentPayYeepayDivideDo) CreateInBatches(values []*model.PaymentPayYeepayDivide, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p paymentPayYeepayDivideDo) Save(values ...*model.PaymentPayYeepayDivide) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p paymentPayYeepayDivideDo) First() (*model.PaymentPayYeepayDivide, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepayDivide), nil
	}
}

func (p paymentPayYeepayDivideDo) Take() (*model.PaymentPayYeepayDivide, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepayDivide), nil
	}
}

func (p paymentPayYeepayDivideDo) Last() (*model.PaymentPayYeepayDivide, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepayDivide), nil
	}
}

func (p paymentPayYeepayDivideDo) Find() ([]*model.PaymentPayYeepayDivide, error) {
	result, err := p.DO.Find()
	return result.([]*model.PaymentPayYeepayDivide), err
}

func (p paymentPayYeepayDivideDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PaymentPayYeepayDivide, err error) {
	buf := make([]*model.PaymentPayYeepayDivide, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p paymentPayYeepayDivideDo) FindInBatches(result *[]*model.PaymentPayYeepayDivide, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p paymentPayYeepayDivideDo) Attrs(attrs ...field.AssignExpr) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p paymentPayYeepayDivideDo) Assign(attrs ...field.AssignExpr) IPaymentPayYeepayDivideDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p paymentPayYeepayDivideDo) Joins(fields ...field.RelationField) IPaymentPayYeepayDivideDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p paymentPayYeepayDivideDo) Preload(fields ...field.RelationField) IPaymentPayYeepayDivideDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p paymentPayYeepayDivideDo) FirstOrInit() (*model.PaymentPayYeepayDivide, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepayDivide), nil
	}
}

func (p paymentPayYeepayDivideDo) FirstOrCreate() (*model.PaymentPayYeepayDivide, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PaymentPayYeepayDivide), nil
	}
}

func (p paymentPayYeepayDivideDo) FindByPage(offset int, limit int) (result []*model.PaymentPayYeepayDivide, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p paymentPayYeepayDivideDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p paymentPayYeepayDivideDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p paymentPayYeepayDivideDo) Delete(models ...*model.PaymentPayYeepayDivide) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *paymentPayYeepayDivideDo) withDO(do gen.Dao) *paymentPayYeepayDivideDo {
	p.DO = *do.(*gen.DO)
	return p
}
