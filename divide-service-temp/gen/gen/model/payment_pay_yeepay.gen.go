// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/plugin/optimisticlock"
)

const TableNamePaymentPayYeepay = "payment_pay_yeepay"

// PaymentPayYeepay mapped from table <payment_pay_yeepay>
type PaymentPayYeepay struct {
	ID            string                 `gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid();comment:id" json:"id"`                                                       // id
	UserID        string                 `gorm:"column:user_id;type:uuid;not null;comment:平台用户id" json:"user_id"`                                                                     // 平台用户id
	OrderID       string                 `gorm:"column:order_id;type:uuid;not null;comment:订单id" json:"order_id"`                                                                     // 订单id
	YeepayOrderNo string                 `gorm:"column:yeepay_order_no;type:text;not null;comment:第三方订单号" json:"yeepay_order_no"`                                                     // 第三方订单号
	PayAmount     decimal.Decimal        `gorm:"column:pay_amount;type:numeric(12,2);not null;comment:交易金额" json:"pay_amount"`                                                        // 交易金额
	MerchantFee   *decimal.Decimal       `gorm:"column:merchant_fee;type:numeric(12,2);comment:商户手续费" json:"merchant_fee"`                                                            // 商户手续费
	UnSplitAmount *decimal.Decimal       `gorm:"column:un_split_amount;type:numeric(12,2);default:0.00;comment:剩余可分账金额" json:"un_split_amount"`                                       // 剩余可分账金额
	CsAt          *time.Time             `gorm:"column:cs_at;type:timestamp with time zone;comment:清算时间" json:"cs_at"`                                                                // 清算时间
	PayAt         *time.Time             `gorm:"column:pay_at;type:timestamp with time zone;comment:支付时间" json:"pay_at"`                                                              // 支付时间
	PayURL        string                 `gorm:"column:pay_url;type:text;not null;comment:支付链接" json:"pay_url"`                                                                       // 支付链接
	PayWay        int16                  `gorm:"column:pay_way;type:smallint;not null;comment:支付方式" json:"pay_way"`                                                                   // 支付方式
	DivideType    int16                  `gorm:"column:divide_type;type:smallint;not null;comment:分账类型" json:"divide_type"`                                                           // 分账类型
	CreatedAt     time.Time              `gorm:"column:created_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoCreateTime;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt     time.Time              `gorm:"column:updated_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoUpdateTime;comment:更新时间" json:"updated_at"` // 更新时间
	DeletedAt     gorm.DeletedAt         `gorm:"column:deleted_at;type:timestamp(6) with time zone;comment:软删除" json:"deleted_at"`                                                    // 软删除
	Version       optimisticlock.Version `gorm:"column:version;type:bigint;not null;comment:乐观锁" json:"version"`                                                                      // 乐观锁
	DivideStatus  int16                  `gorm:"column:divide_status;type:smallint;not null;comment:分账状态" json:"divide_status"`                                                       // 分账状态
}

// TableName PaymentPayYeepay's table name
func (*PaymentPayYeepay) TableName() string {
	return TableNamePaymentPayYeepay
}
