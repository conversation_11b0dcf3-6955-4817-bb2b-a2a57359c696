// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/plugin/optimisticlock"
)

const TableNamePaymentOrder = "payment_orders"

// PaymentOrder mapped from table <payment_orders>
type PaymentOrder struct {
	ID          string                 `gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid();comment:id" json:"id"`                                                       // id
	Status      int16                  `gorm:"column:status;type:smallint;not null;comment:订单状态" json:"status"`                                                                     // 订单状态
	OrderNo     string                 `gorm:"column:orderNo;type:text;not null;comment:订单号" json:"orderNo"`                                                                        // 订单号
	UserID      string                 `gorm:"column:user_id;type:uuid;not null;comment:用户id" json:"user_id"`                                                                       // 用户id
	Type        int16                  `gorm:"column:type;type:smallint;not null;comment:订单类型" json:"type"`                                                                         // 订单类型
	Title       string                 `gorm:"column:title;type:text;not null;comment:订单标题" json:"title"`                                                                           // 订单标题
	CreatedAt   time.Time              `gorm:"column:created_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoCreateTime;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt   time.Time              `gorm:"column:updated_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoUpdateTime;comment:更新时间" json:"updated_at"` // 更新时间
	DeletedAt   gorm.DeletedAt         `gorm:"column:deleted_at;type:timestamp(6) with time zone;comment:软删除" json:"deleted_at"`                                                    // 软删除
	Version     optimisticlock.Version `gorm:"column:version;type:bigint;not null;comment:乐观锁" json:"version"`                                                                      // 乐观锁
	PayType     int16                  `gorm:"column:pay_type;type:smallint;not null;comment:支付方式类型" json:"pay_type"`                                                               // 支付方式类型
	PayTime     *time.Time             `gorm:"column:pay_time;type:timestamp(6) with time zone;comment:支付时间" json:"pay_time"`                                                       // 支付时间
	ExpiredTime time.Time              `gorm:"column:expired_time;type:timestamp(6) with time zone;not null;comment:订单过期时间" json:"expired_time"`                                    // 订单过期时间
	ImgURL      string                 `gorm:"column:img_url;type:text;not null;comment:图片" json:"img_url"`                                                                         // 图片
	Price       decimal.Decimal        `gorm:"column:price;type:numeric(12,2);not null;comment:价格" json:"price"`                                                                    // 价格
	PayID       *string                `gorm:"column:pay_id;type:uuid;comment:对应支付方式表id" json:"pay_id"`                                                                             // 对应支付方式表id
}

// TableName PaymentOrder's table name
func (*PaymentOrder) TableName() string {
	return TableNamePaymentOrder
}
