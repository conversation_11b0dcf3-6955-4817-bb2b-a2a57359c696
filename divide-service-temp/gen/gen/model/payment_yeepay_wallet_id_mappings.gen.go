// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNamePaymentYeepayWalletIDMapping = "payment_yeepay_wallet_id_mappings"

// PaymentYeepayWalletIDMapping mapped from table <payment_yeepay_wallet_id_mappings>
type PaymentYeepayWalletIDMapping struct {
	ID        int32      `gorm:"column:id;type:integer;primaryKey" json:"id"`
	MysqlID   *int64     `gorm:"column:mysql_id;type:bigint" json:"mysql_id"`
	PgUUID    *string    `gorm:"column:pg_uuid;type:uuid" json:"pg_uuid"`
	CreatedAt *time.Time `gorm:"column:created_at;type:timestamp with time zone;autoCreateTime" json:"created_at"`
}

// TableName PaymentYeepayWalletIDMapping's table name
func (*PaymentYeepayWalletIDMapping) TableName() string {
	return TableNamePaymentYeepayWalletIDMapping
}
