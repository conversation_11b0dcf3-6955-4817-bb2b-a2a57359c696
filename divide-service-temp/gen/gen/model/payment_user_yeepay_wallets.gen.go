// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNamePaymentUserYeepayWallet = "payment_user_yeepay_wallets"

// PaymentUserYeepayWallet mapped from table <payment_user_yeepay_wallets>
type PaymentUserYeepayWallet struct {
	ID             string    `gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid();comment:主键" json:"id"`                                         // 主键
	UserID         string    `gorm:"column:user_id;type:text;not null;default:NULL;comment:用户id" json:"user_id"`                                            // 用户id
	MemberID       *string   `gorm:"column:member_id;type:text;default:NULL;comment:钱包账户id" json:"member_id"`                                               // 钱包账户id
	Name           string    `gorm:"column:name;type:text;not null;default:NULL;comment:姓名" json:"name"`                                                    // 姓名
	IDCard         string    `gorm:"column:id_card;type:text;not null;default:NULL;comment:身份证号" json:"id_card"`                                            // 身份证号
	CreatedTime    time.Time `gorm:"column:created_time;type:timestamp with time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_time"` // 创建时间
	UpdatedTime    time.Time `gorm:"column:updated_time;type:timestamp with time zone;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_time"` // 更新时间
	ExternalUserID string    `gorm:"column:external_user_id;type:text;not null;comment:元镜用户id" json:"external_user_id"`                                     // 元镜用户id
	BusinessNo     *string   `gorm:"column:business_no;type:text;comment:易宝唯一订单号" json:"business_no"`                                                       // 易宝唯一订单号
}

// TableName PaymentUserYeepayWallet's table name
func (*PaymentUserYeepayWallet) TableName() string {
	return TableNamePaymentUserYeepayWallet
}
