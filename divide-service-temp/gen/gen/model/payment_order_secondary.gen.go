// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/plugin/optimisticlock"
)

const TableNamePaymentOrderSecondary = "payment_order_secondary"

// PaymentOrderSecondary mapped from table <payment_order_secondary>
type PaymentOrderSecondary struct {
	ID        string                 `gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid();comment:id" json:"id"`                                                       // id
	OrderID   string                 `gorm:"column:order_id;type:uuid;not null;comment:订单id" json:"order_id"`                                                                     // 订单id
	UserID    string                 `gorm:"column:user_id;type:uuid;not null;comment:用户id" json:"user_id"`                                                                       // 用户id
	ProjectID string                 `gorm:"column:project_id;type:uuid;not null;comment:项目id" json:"project_id"`                                                                 // 项目id
	CreatedAt time.Time              `gorm:"column:created_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoCreateTime;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt time.Time              `gorm:"column:updated_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoUpdateTime;comment:更新时间" json:"updated_at"` // 更新时间
	DeletedAt gorm.DeletedAt         `gorm:"column:deleted_at;type:timestamp(6) with time zone;comment:软删除" json:"deleted_at"`                                                    // 软删除
	Version   optimisticlock.Version `gorm:"column:version;type:bigint;not null;comment:乐观锁" json:"version"`                                                                      // 乐观锁
	NftID     string                 `gorm:"column:nft_id;type:uuid;not null;comment:购买nft id" json:"nft_id"`                                                                     // 购买nft id
	Price     decimal.Decimal        `gorm:"column:price;type:numeric(12,2);not null;comment:价格" json:"price"`                                                                    // 价格
	ListingID string                 `gorm:"column:listing_id;type:uuid;not null;comment:市场在售商品id" json:"listing_id"`                                                             // 市场在售商品id
	SellerID  string                 `gorm:"column:seller_id;type:uuid;not null;comment:售卖者id" json:"seller_id"`                                                                  // 售卖者id
}

// TableName PaymentOrderSecondary's table name
func (*PaymentOrderSecondary) TableName() string {
	return TableNamePaymentOrderSecondary
}
