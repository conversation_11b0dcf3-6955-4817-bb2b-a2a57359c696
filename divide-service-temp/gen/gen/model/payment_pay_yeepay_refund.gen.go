// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/plugin/optimisticlock"
)

const TableNamePaymentPayYeepayRefund = "payment_pay_yeepay_refund"

// PaymentPayYeepayRefund mapped from table <payment_pay_yeepay_refund>
type PaymentPayYeepayRefund struct {
	ID           string                 `gorm:"column:id;type:uuid;not null;default:gen_random_uuid();comment:id" json:"id"`                                                         // id
	OrderID      string                 `gorm:"column:order_id;type:uuid;primaryKey;comment:订单id" json:"order_id"`                                                                   // 订单id
	RefundAmount decimal.Decimal        `gorm:"column:refund_amount;type:numeric(10,2);not null;comment:退款金额" json:"refund_amount"`                                                  // 退款金额
	Status       int16                  `gorm:"column:status;type:smallint;not null;comment:状态" json:"status"`                                                                       // 状态
	CreatedAt    time.Time              `gorm:"column:created_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoCreateTime;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt    time.Time              `gorm:"column:updated_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoUpdateTime;comment:更新时间" json:"updated_at"` // 更新时间
	DeletedAt    gorm.DeletedAt         `gorm:"column:deleted_at;type:timestamp(6) with time zone;comment:软删除" json:"deleted_at"`                                                    // 软删除
	Version      optimisticlock.Version `gorm:"column:version;type:bigint;not null;comment:乐观锁" json:"version"`                                                                      // 乐观锁
	Remark       string                 `gorm:"column:remark;type:text;not null;comment:备注" json:"remark"`                                                                           // 备注
	RefundReqNo  string                 `gorm:"column:refund_req_no;type:text;not null;comment:退款流水号" json:"refund_req_no"`                                                          // 退款流水号
}

// TableName PaymentPayYeepayRefund's table name
func (*PaymentPayYeepayRefund) TableName() string {
	return TableNamePaymentPayYeepayRefund
}
