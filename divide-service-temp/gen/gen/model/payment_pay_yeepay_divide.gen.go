// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/plugin/optimisticlock"
)

const TableNamePaymentPayYeepayDivide = "payment_pay_yeepay_divide"

// PaymentPayYeepayDivide mapped from table <payment_pay_yeepay_divide>
type PaymentPayYeepayDivide struct {
	ID                    string                 `gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid();comment:id" json:"id"`                                                                       // id
	MemberID              string                 `gorm:"column:member_id;type:text;not null;default:NULL;comment:分账易宝用户 Member对象 的 id；若是商户本身时，传入0" json:"member_id"`                                          // 分账易宝用户 Member对象 的 id；若是商户本身时，传入0
	MemberType            int16                  `gorm:"column:member_type;type:smallint;not null;comment:商户类型[1:项目方, 2:卖家用户]" json:"member_type"`                                                            // 商户类型[1:项目方, 2:卖家用户]
	RequestNo             string                 `gorm:"column:request_no;type:text;not null;default:NULL;comment:分账流水号,幂等" json:"request_no"`                                                                // 分账流水号,幂等
	OrderID               string                 `gorm:"column:order_id;type:uuid;not null;comment:订单id" json:"order_id"`                                                                                     // 订单id
	Amount                decimal.Decimal        `gorm:"column:amount;type:numeric(12,2);not null;default:NULL;comment:分账金额" json:"amount"`                                                                   // 分账金额
	CreatedAt             time.Time              `gorm:"column:created_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoCreateTime;comment:创建时间" json:"created_at"`                 // 创建时间
	UpdatedAt             time.Time              `gorm:"column:updated_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoUpdateTime;comment:更新时间" json:"updated_at"`                 // 更新时间
	DeletedAt             gorm.DeletedAt         `gorm:"column:deleted_at;type:timestamp(6) with time zone;comment:软删除" json:"deleted_at"`                                                                    // 软删除
	Version               optimisticlock.Version `gorm:"column:version;type:bigint;not null;comment:乐观锁" json:"version"`                                                                                      // 乐观锁
	OrderSecondaryExtraID string                 `gorm:"column:order_secondary_extra_id;type:uuid;not null;default:00000000-0000-0000-0000-000000000000;comment:订单二级额外信息表id" json:"order_secondary_extra_id"` // 订单二级额外信息表id
}

// TableName PaymentPayYeepayDivide's table name
func (*PaymentPayYeepayDivide) TableName() string {
	return TableNamePaymentPayYeepayDivide
}
