// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/plugin/optimisticlock"
)

const TableNamePaymentDivideSecondary = "payment_divide_secondary"

// PaymentDivideSecondary mapped from table <payment_divide_secondary>
type PaymentDivideSecondary struct {
	ID                   string                 `gorm:"column:id;type:uuid;not null;default:gen_random_uuid();comment:id" json:"id"`                                                         // id
	CreatedAt            time.Time              `gorm:"column:created_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoCreateTime;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt            time.Time              `gorm:"column:updated_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoUpdateTime;comment:更新时间" json:"updated_at"` // 更新时间
	Version              optimisticlock.Version `gorm:"column:version;type:bigint;not null;comment:乐观锁" json:"version"`                                                                      // 乐观锁
	RecipientDescription string                 `gorm:"column:recipient_description;type:text;not null;comment:分账接收方描述" json:"recipient_description"`                                        // 分账接收方描述
	ProjectID            string                 `gorm:"column:project_id;type:uuid;primaryKey;comment:项目id" json:"project_id"`                                                               // 项目id
	RevenueShare         decimal.Decimal        `gorm:"column:revenue_share;type:numeric(2,2);not null;comment:分成比例如：0.88" json:"revenue_share"`                                             // 分成比例如：0.88
}

// TableName PaymentDivideSecondary's table name
func (*PaymentDivideSecondary) TableName() string {
	return TableNamePaymentDivideSecondary
}
