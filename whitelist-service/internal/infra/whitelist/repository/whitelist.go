package repository

import (
	"context"
	"errors"

	"cnb.cool/cymirror/ces-services/whitelist-service/gen/gen/model"
	"cnb.cool/cymirror/ces-services/whitelist-service/internal/domain/whitelist"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"go.uber.org/zap"

	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/whitelist-service/gen/gen/query"
	"cnb.cool/cymirror/ces-services/whitelist-service/internal/domain/whitelist/entity"
)

var _ whitelist.Repo = (*WhitelistRepository)(nil)

type WhitelistRepository struct {
	db *db.DB[*query.Query]
}

func NewWhitelistRepository(db *db.DB[*query.Query]) *WhitelistRepository {
	return &WhitelistRepository{
		db: db,
	}
}

func (r *WhitelistRepository) CreateWhitelist(ctx context.Context, w *entity.Whitelist) (*entity.Whitelist, error) {
	d := r.db.Get(ctx)
	md := entity.ToWhitelistModel(w)
	if err := d.WithContext(ctx).Whitelist.Create(md); err != nil {
		zap.L().Error("failed to create whitelist", zap.Error(err))
		return nil, err
	}
	return entity.NewWhitelistFromModel(md), nil
}

func (r *WhitelistRepository) UpdateWhitelist(ctx context.Context, w *entity.Whitelist) (*entity.Whitelist, error) {
	d := r.db.Get(ctx)
	md := entity.ToWhitelistModel(w)
	if _, err := d.WithContext(ctx).Whitelist.
		Where(d.Whitelist.ID.Eq(w.ID)).
		Updates(md); err != nil {
		zap.L().Error("failed to update whitelist", zap.Error(err))
		return nil, err
	}
	return w, nil
}

func (r *WhitelistRepository) BatchCreateUserRules(ctx context.Context, rules []*entity.UserRule) error {
	d := r.db.Get(ctx)
	var models []*model.WhitelistUserRule
	for _, rule := range rules {
		models = append(models, entity.ToUserRuleModel(rule))
	}
	if err := d.WithContext(ctx).WhitelistUserRule.CreateInBatches(models, 1000); err != nil {
		zap.L().Error("failed to batch create user rules", zap.Error(err))
		return err
	}
	return nil
}

func (r *WhitelistRepository) UpdateUserRules(ctx context.Context, rule *entity.UserRule) error {
	d := r.db.Get(ctx)
	md := entity.ToUserRuleModel(rule)
	if _, err := d.WithContext(ctx).WhitelistUserRule.
		Where(d.WhitelistUserRule.ID.Eq(rule.ID)).
		Updates(md); err != nil {
		zap.L().Error("failed to update user rules", zap.Error(err))
		return err
	}
	return nil
}

func (r *WhitelistRepository) BatchUpdateUserRules(ctx context.Context, rules []*entity.UserRule) error {
	if len(rules) == 0 {
		return nil
	}

	d := r.db.Get(ctx)

	// 将领域模型转换为数据库模型
	var models []*model.WhitelistUserRule
	for _, rule := range rules {
		models = append(models, entity.ToUserRuleModel(rule))
	}

	// 使用 Save 进行批量更新（基于主键的 upsert 操作）
	if err := d.WithContext(ctx).WhitelistUserRule.Save(models...); err != nil {
		zap.L().Error("failed to batch update user rules", zap.Error(err))
		return err
	}

	return nil
}

func (r *WhitelistRepository) GetWhitelistByID(ctx context.Context, id string) (*entity.Whitelist, error) {
	d := r.db.Get(ctx)
	result, err := d.Whitelist.WithContext(ctx).
		Where(d.Whitelist.ID.Eq(id)).
		First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		zap.L().Error("failed to get whitelist by id", zap.Error(err))
		return nil, err
	}
	return entity.NewWhitelistFromModel(result), nil
}

func (r *WhitelistRepository) ListWhitelists(ctx context.Context, page int, pageSize int) ([]*entity.Whitelist, int64, error) {
	d := r.db.Get(ctx)
	offset := (page - 1) * pageSize
	limit := pageSize

	data, total, err := d.Whitelist.WithContext(ctx).
		Order(d.Whitelist.CreatedAt.Desc()).
		FindByPage(offset, limit)
	if err != nil {
		zap.L().Error("failed to list whitelists", zap.Error(err))
		return nil, 0, err
	}

	var result []*entity.Whitelist
	for _, item := range data {
		result = append(result, entity.NewWhitelistFromModel(item))
	}
	return result, total, nil
}

func (r *WhitelistRepository) GetWhitelistByRuleTypeAndRuleTypeID(ctx context.Context, ruleType int32, ruleTypeID string) (*entity.Whitelist, error) {
	d := r.db.Get(ctx)
	result, err := d.Whitelist.WithContext(ctx).
		Where(d.Whitelist.RuleType.Eq(int16(ruleType)), d.Whitelist.RuleTypeID.Eq(ruleTypeID)).
		Order(d.Whitelist.CreatedAt.Desc()). // 一组 ruletype 和 ruleTypeID 可能有多个白名单，取最新的一个
		First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		zap.L().Error("failed to get whitelist by rule type and rule type id", zap.Error(err))
		return nil, err
	}
	return entity.NewWhitelistFromModel(result), nil
}

func (r *WhitelistRepository) GetUserRuleByUserIDAndWhitelistID(ctx context.Context, userID string, whitelistID string) (*entity.UserRule, error) {
	d := r.db.Get(ctx)
	result, err := d.WhitelistUserRule.WithContext(ctx).
		Where(d.WhitelistUserRule.UserID.Eq(userID), d.WhitelistUserRule.WhitelistID.Eq(whitelistID)).
		First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		zap.L().Error("failed to get user rule by user id and whitelist id", zap.Error(err))
		return nil, err
	}
	return entity.NewUserRuleFromModel(result), nil
}

func (r *WhitelistRepository) GetUserRulesByWhitelistID(ctx context.Context, whitelistID string) ([]*entity.UserRule, error) {
	d := r.db.Get(ctx)
	rules, err := d.WhitelistUserRule.WithContext(ctx).
		Where(d.WhitelistUserRule.WhitelistID.Eq(whitelistID)).
		Find()
	if err != nil {
		zap.L().Error("failed to get user rules by whitelist id", zap.Error(err))
		return nil, err
	}
	var result []*entity.UserRule
	for _, rule := range rules {
		result = append(result, entity.NewUserRuleFromModel(rule))
	}
	return result, nil
}

// SearchWhitelists 搜索白名单（支持关键字、类型、时间区间、数量区间）
func (r *WhitelistRepository) SearchWhitelists(ctx context.Context, opts entity.WhitelistRPCSearchOptions) ([]*entity.Whitelist, int64, error) {
	d := r.db.Get(ctx)
	tx := d.Whitelist.WithContext(ctx)

	// 关键词搜索：支持白名单ID、白名单名称、描述、关联业务ID
	if opts.Keyword != "" {
		if _, err := uuid.Parse(opts.Keyword); err == nil {
			// 如果关键词是UUID格式，搜索白名单ID和关联业务ID
			tx = tx.Where(d.Whitelist.ID.Eq(opts.Keyword)).Or(d.Whitelist.RuleTypeID.Eq(opts.Keyword))
		} else {
			// 如果不是UUID，模糊搜索白名单名称和描述
			tx = tx.Where(d.Whitelist.Name.Like("%" + opts.Keyword + "%")).Or(d.Whitelist.Description.Like("%" + opts.Keyword + "%"))
		}
	}

	// 类型筛选
	if opts.Type != nil {
		tx = tx.Where(d.Whitelist.RuleType.Eq(int16(*opts.Type)))
	}

	// 分页
	offset := (opts.Page - 1) * opts.PageSize
	limit := opts.PageSize

	// 查询，默认按创建时间倒序
	data, total, err := tx.
		Order(d.Whitelist.CreatedAt.Desc()).
		FindByPage(offset, limit)
	if err != nil {
		zap.L().Error("failed to search whitelists for RPC", zap.Error(err))
		return nil, 0, err
	}

	var result []*entity.Whitelist
	for _, item := range data {
		result = append(result, entity.NewWhitelistFromModel(item))
	}
	return result, total, nil
}

// SearchUserRules 管理后台RPC专用的用户规则搜索
func (r *WhitelistRepository) SearchUserRules(ctx context.Context, opts entity.UserRuleSearchOptions) ([]*entity.UserRule, int64, error) {
	d := r.db.Get(ctx)
	tx := d.WhitelistUserRule.WithContext(ctx)

	// 必须指定白名单ID
	tx = tx.Where(d.WhitelistUserRule.WhitelistID.Eq(opts.WhitelistID))

	// 关键词搜索：支持用户ID、手机号
	if opts.Keyword != "" {
		if _, err := uuid.Parse(opts.Keyword); err == nil {
			// 如果关键词是UUID格式，精确匹配用户ID
			tx = tx.Where(d.WhitelistUserRule.UserID.Eq(opts.Keyword))
		} else {
			// 如果不是UUID，模糊搜索手机号
			tx = tx.Where(d.WhitelistUserRule.PhoneNumber.Like("%" + opts.Keyword + "%"))
		}
	}

	// 分页
	offset := (opts.Page - 1) * opts.PageSize
	limit := opts.PageSize

	// 查询，默认按创建时间倒序
	data, total, err := tx.
		Order(d.WhitelistUserRule.CreatedAt.Desc()).
		FindByPage(offset, limit)
	if err != nil {
		zap.L().Error("failed to search user rules for RPC", zap.Error(err))
		return nil, 0, err
	}

	var result []*entity.UserRule
	for _, item := range data {
		result = append(result, entity.NewUserRuleFromModel(item))
	}
	return result, total, nil
}
