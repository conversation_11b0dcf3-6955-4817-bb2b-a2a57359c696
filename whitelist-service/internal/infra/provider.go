package infra

import (
	"cnb.cool/cymirror/ces-services/whitelist-service/internal/domain/whitelist"
	"cnb.cool/cymirror/ces-services/whitelist-service/internal/infra/whitelist/repository"
	"github.com/google/wire"
)

// InfraProviderSet Infrastructure providers
var InfraProviderSet = wire.NewSet(
	repository.NewWhitelistRepository,
	wire.Bind(new(whitelist.Repo), new(*repository.WhitelistRepository)),
)
