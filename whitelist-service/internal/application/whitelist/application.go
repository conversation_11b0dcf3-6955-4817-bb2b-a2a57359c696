package whitelist

import (
	"bytes"
	"context"
	"fmt"
	"strconv"
	"time"

	"cnb.cool/cymirror/ces-services/common/auth"
	"cnb.cool/cymirror/ces-services/common/excel"
	fusionpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/fusion"
	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
	profilepb "cnb.cool/cymirror/ces-services/user-service/gen/proto/ces/user/profile"
	whitelistpb "cnb.cool/cymirror/ces-services/whitelist-service/gen/proto/ces/whitelist/whitelist"
	"cnb.cool/cymirror/ces-services/whitelist-service/internal/domain/whitelist"
	"cnb.cool/cymirror/ces-services/whitelist-service/internal/domain/whitelist/entity"
	"github.com/google/uuid"
)

type Application struct {
	whitelistRepo whitelist.Repo
	profileClient profilepb.ProfileServiceClient
	orderClient   orderpb.OrderServiceClient
	fusionClient  fusionpb.FusionServiceClient
}

func NewApplication(whitelistRepo whitelist.Repo, profileClient profilepb.ProfileServiceClient, orderClient orderpb.OrderServiceClient, fusionClient fusionpb.FusionServiceClient) *Application {
	return &Application{
		whitelistRepo: whitelistRepo,
		profileClient: profileClient,
		orderClient:   orderClient,
		fusionClient:  fusionClient,
	}
}

func (a *Application) CreateWhitelist(ctx context.Context, req *whitelistpb.CreateWhitelistReq) (*whitelistpb.Whitelist, error) {
	userId := auth.UserIDFromContext(ctx)
	if userId == "" {
		return nil, whitelist.ErrUnauthorized
	}
	// 临时写死
	if req.Token != "N7$gPz2q#f1LwX!eD9vB3@KtYm%a8RuJ" {
		return nil, whitelist.ErrInvalidToken
	}
	w := &entity.Whitelist{
		RuleType:   req.Type,
		RuleTypeID: req.TypeID,
		Name:       req.Name,
		StartTime:  time.UnixMilli(req.StartTime),
		Num:        req.Num,
	}
	if req.Description != nil {
		w.Description = *req.Description
	}
	if req.EndTime != nil {
		w.EndTime = time.UnixMilli(*req.EndTime)
	}
	dbWhitelist, err := a.whitelistRepo.CreateWhitelist(ctx, w)
	if err != nil {
		return nil, err
	}

	return entity.ToProto(dbWhitelist), nil
}

func (a *Application) UpdateWhitelist(ctx context.Context, req *whitelistpb.UpdateWhitelistReq) (*whitelistpb.Whitelist, error) {
	userId := auth.UserIDFromContext(ctx)
	if userId == "" {
		return nil, whitelist.ErrUnauthorized
	}
	// 临时写死
	if req.Token != "N7$gPz2q#f1LwX!eD9vB3@KtYm%a8RuJ" {
		return nil, whitelist.ErrInvalidToken
	}
	w, err := a.whitelistRepo.GetWhitelistByID(ctx, req.Id)
	if err != nil {
		return nil, whitelist.ErrNotFound
	}
	if w.StartTime.Before(time.Now()) {
		return nil, whitelist.ErrWhitelistStarted
	}
	w.RuleTypeID = req.TypeID
	w.RuleType = req.Type
	w.Name = req.Name
	w.Num = req.Num
	w.StartTime = time.UnixMilli(req.StartTime)
	if req.Description != nil {
		w.Description = *req.Description
	}
	if req.EndTime != nil {
		w.EndTime = time.UnixMilli(*req.EndTime)
	}
	dbWhitelist, err := a.whitelistRepo.UpdateWhitelist(ctx, w)
	if err != nil {
		return nil, err
	}

	return entity.ToProto(dbWhitelist), nil
}

func (a *Application) ImportWhitelistUsers(ctx context.Context, req *whitelistpb.ImportWhitelistUsersReq) (*whitelistpb.ImportWhitelistUsersResp, error) {
	// 临时写死
	if req.Token != "N7$gPz2q#f1LwX!eD9vB3@KtYm%a8RuJ" {
		return nil, whitelist.ErrInvalidToken
	}
	reader := bytes.NewReader(req.FileContent)
	records, err := excel.ParseFileToRecords(req.FileName, reader)
	if err != nil {
		return nil, err
	}

	// 收集所有手机号
	phoneSet := map[string]struct{}{}
	for _, r := range records {
		if phone := r["phone"]; phone != "" {
			phoneSet[phone] = struct{}{}
		}
	}
	var phones []string
	for phone := range phoneSet {
		phones = append(phones, phone)
	}

	// 调用 RPC 批量获取 user_id
	users, err := a.profileClient.GetUserByPhoneNumbersRPC(ctx, &profilepb.GetUserByPhoneNumbersReq{PhoneNumber: phones})
	if err != nil {
		return nil, fmt.Errorf("get user_id by phone failed: %w", err)
	}
	// userMap: map[string]string, key 是手机号，value 是 userID
	userMap := make(map[string]string)
	for _, user := range users.Users {
		if user != nil && user.PhoneNumber != "" && user.ID != "" {
			userMap[user.PhoneNumber] = user.ID
		}
	}

	// 获取已存在的用户规则
	existingRules, err := a.whitelistRepo.GetUserRulesByWhitelistID(ctx, req.WhitelistID)
	if err != nil {
		return nil, fmt.Errorf("get existing rules failed: %w", err)
	}
	existingRuleMap := make(map[string]*entity.UserRule)
	for _, rule := range existingRules {
		existingRuleMap[rule.UserID] = rule
	}

	// 转换为 UserRule，并合并相同用户的 num
	newRuleMap := make(map[string]*entity.UserRule)
	resp := &whitelistpb.ImportWhitelistUsersResp{
		FailedRecords:    make([]*whitelistpb.ImportWhitelistUsersResp_FailedRecord, 0),
		DuplicateRecords: make([]*whitelistpb.ImportWhitelistUsersResp_DuplicateRecord, 0),
	}

	for idx, r := range records {
		phone := r["phone"]
		// 如果手机号为空，则跳过
		if phone == "" {
			resp.FailedRecords = append(resp.FailedRecords, &whitelistpb.ImportWhitelistUsersResp_FailedRecord{
				RowNumber: int32(idx + 2),
				Reason:    "手机号为空",
			})
			continue
		}
		userID := userMap[phone]
		if userID == "" {
			resp.FailedRecords = append(resp.FailedRecords, &whitelistpb.ImportWhitelistUsersResp_FailedRecord{
				RowNumber:   int32(idx + 2),
				PhoneNumber: phone,
				Reason:      "用户不存在",
			})
			continue
		}
		// 获取 num 值
		var numValue int32
		if rawNum, ok := r["num"]; ok && rawNum != "" {
			n, err := strconv.Atoi(rawNum)
			if err != nil {
				resp.FailedRecords = append(resp.FailedRecords, &whitelistpb.ImportWhitelistUsersResp_FailedRecord{
					RowNumber:   int32(idx + 2),
					PhoneNumber: phone,
					Reason:      fmt.Sprintf("无效的数量值: %v", err),
				})
				continue
			}
			numValue = int32(n)
		}

		if existingRule, exists := existingRuleMap[userID]; exists {
			// 用户已存在于白名单中
			oldNum := int32(0)
			if existingRule.Num != nil {
				oldNum = *existingRule.Num
			}
			newNum := oldNum + numValue
			resp.ExistingUsers = append(resp.ExistingUsers, &whitelistpb.ImportWhitelistUsersResp_ExistingUserInfo{
				PhoneNumber: phone,
				UserId:      userID,
				OldNum:      oldNum,
				NewNum:      newNum,
			})
			resp.TotalUpdated++
			existingRule.Num = &newNum
			existingRuleMap[userID] = existingRule
		} else if newRule, exists := newRuleMap[userID]; exists {
			// 新导入数据中有重复用户
			oldNum := int32(0)
			if newRule.Num != nil {
				oldNum = *newRule.Num
			}
			newNum := oldNum + numValue
			resp.DuplicateRecords = append(resp.DuplicateRecords, &whitelistpb.ImportWhitelistUsersResp_DuplicateRecord{
				PhoneNumber: phone,
				UserId:      userID,
				OldNum:      oldNum,
				NewNum:      newNum,
			})
			newRule.Num = &newNum
		} else {
			// 全新的用户
			rule := &entity.UserRule{
				UserID:      userID,
				WhitelistID: req.WhitelistID,
				Num:         &numValue,
				PhoneNumber: phone,
				CreatedAt:   time.Now(),
			}
			newRuleMap[userID] = rule
			resp.TotalImported++
		}
	}

	// 更新现有规则
	var rulesToUpdate []*entity.UserRule
	for _, rule := range existingRuleMap {
		rulesToUpdate = append(rulesToUpdate, rule)
	}
	if len(rulesToUpdate) > 0 {
		for _, rule := range rulesToUpdate {
			if err := a.whitelistRepo.UpdateUserRules(ctx, rule); err != nil {
				return nil, fmt.Errorf("update user rules failed: %w", err)
			}
		}
	}

	// 创建新规则
	var newRules []*entity.UserRule
	for _, rule := range newRuleMap {
		newRules = append(newRules, rule)
	}
	if len(newRules) > 0 {
		if err := a.whitelistRepo.BatchCreateUserRules(ctx, newRules); err != nil {
			return nil, fmt.Errorf("create new rules failed: %w", err)
		}
	}

	return resp, nil
}

func (a *Application) GetWhitelist(ctx context.Context, req *whitelistpb.GetWhitelistReq) (*whitelistpb.Whitelist, error) {
	userId := auth.UserIDFromContext(ctx)
	if userId == "" {
		return nil, whitelist.ErrUnauthorized
	}
	w, err := a.whitelistRepo.GetWhitelistByID(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return entity.ToProto(w), nil
}

func (a *Application) ListWhitelists(ctx context.Context, req *whitelistpb.ListWhitelistsReq) (*whitelistpb.ListWhitelistsResp, error) {
	userId := auth.UserIDFromContext(ctx)
	if userId == "" {
		return nil, whitelist.ErrUnauthorized
	}
	whitelists, total, err := a.whitelistRepo.ListWhitelists(ctx, int(req.Page), int(req.PageSize))
	if err != nil {
		return nil, err
	}
	var respWhitelists []*whitelistpb.Whitelist
	for _, w := range whitelists {
		respWhitelists = append(respWhitelists, entity.ToProto(w))
	}
	return &whitelistpb.ListWhitelistsResp{Whitelists: respWhitelists, Total: total}, nil
}

func (a *Application) CheckWhitelistQualificationRPC(ctx context.Context, req *whitelistpb.CheckWhitelistQualificationRPCReq) (*whitelistpb.CheckWhitelistQualificationRPCResp, error) {
	// 先尝试获取全量用户白名单
	wl, err := a.whitelistRepo.GetWhitelistByRuleTypeAndRuleTypeID(ctx, 2, req.RuleTypeID)
	if err != nil {
		return nil, err
	}

	// 如果存在全量用户白名单且在有效期内
	if wl != nil && (wl.EndTime.IsZero() || !wl.EndTime.Before(time.Now())) && !wl.StartTime.After(time.Now()) {
		// RPC 获取当前用户的购买数量
		orderCount, err := a.orderClient.GetUserCompletedOrderCountRPC(ctx, &orderpb.GetUserCompletedOrderCountRPCReq{
			UserID:    req.UserID,
			ProjectID: req.RuleTypeID,
		})
		if err != nil {
			return nil, err
		}
		if wl.Num == nil || *wl.Num == 0 {
			// 如果白名单没有数量限制，直接返回 true
			return &whitelistpb.CheckWhitelistQualificationRPCResp{
				HasQualification: true,
				UsageCount:       orderCount.Count,
				TotalCount:       0,
			}, nil
		}
		// 如果用户购买数量没超过白名单数量限制，则返回 true
		if orderCount.Count+int64(req.Count) <= int64(*wl.Num) {
			return &whitelistpb.CheckWhitelistQualificationRPCResp{
				HasQualification: true,
				UsageCount:       orderCount.Count,
				TotalCount:       int64(*wl.Num),
			}, nil
		}
		// 如果全量白名单数量超限，继续尝试优先购白名单，不直接返回失败
	}

	// 如果全量用户白名单不存在或不在有效期或数量超限，则尝试获取优先购白名单
	wl, err = a.whitelistRepo.GetWhitelistByRuleTypeAndRuleTypeID(ctx, 1, req.RuleTypeID)
	if err != nil {
		return nil, err
	}

	// 如果优先购白名单不存在
	if wl == nil {
		return nil, whitelist.ErrNotFound
	}

	// 检查白名单是否在有效期
	if (!wl.EndTime.IsZero() && wl.EndTime.Before(time.Now())) || wl.StartTime.After(time.Now()) {
		return nil, whitelist.ErrWhitelistNotActive
	}

	// 检查用户是否在白名单中
	userRule, err := a.whitelistRepo.GetUserRuleByUserIDAndWhitelistID(ctx, req.UserID, wl.ID)
	if err != nil {
		return nil, err
	}

	if userRule == nil {
		return nil, whitelist.ErrUserNotInWhitelist
	}
	// RPC 获取当前用户的购买数量
	orderCount, err := a.orderClient.GetUserCompletedOrderCountRPC(ctx, &orderpb.GetUserCompletedOrderCountRPCReq{
		UserID:    req.UserID,
		ProjectID: req.RuleTypeID,
	})
	if err != nil {
		return nil, err
	}
	// 如果白名单没有数量限制，直接返回 true
	if userRule.Num == nil || *userRule.Num == 0 {
		return &whitelistpb.CheckWhitelistQualificationRPCResp{
			HasQualification: true,
			UsageCount:       orderCount.Count,
			TotalCount:       0,
		}, nil
	}

	// 如果用户购买数量超过白名单数量限制，则返回 false
	// orderCount.Count + req.Count 超过白名单数量限制
	if orderCount.Count+int64(req.Count) > int64(*userRule.Num) {
		return &whitelistpb.CheckWhitelistQualificationRPCResp{
			HasQualification: false,
			UsageCount:       orderCount.Count,
			TotalCount:       int64(*userRule.Num),
		}, nil
	}

	return &whitelistpb.CheckWhitelistQualificationRPCResp{
		HasQualification: true,
		UsageCount:       orderCount.Count,
		TotalCount:       int64(*userRule.Num),
	}, nil
}

func (a *Application) CheckWhitelistQualificationForFuctionRPC(ctx context.Context, req *whitelistpb.CheckWhitelistQualificationForFuctionRPCReq) (*whitelistpb.CheckWhitelistQualificationForFuctionRPCResp, error) {
	// 先尝试获取全量用户白名单
	wl, err := a.whitelistRepo.GetWhitelistByRuleTypeAndRuleTypeID(ctx, 4, req.RuleTypeID)
	if err != nil {
		return nil, err
	}

	// 如果存在全量用户白名单且在有效期内
	if wl != nil && (wl.EndTime.IsZero() || !wl.EndTime.Before(time.Now())) && !wl.StartTime.After(time.Now()) {
		// RPC 获取当前用户的合成次数
		fusionCount, err := a.fusionClient.GetUserFusionCountRPC(ctx, &fusionpb.GetUserFusionCountRPCReq{
			UserId:  req.UserID,
			EventId: req.RuleTypeID,
		})
		if err != nil {
			return nil, err
		}
		if wl.Num == nil || *wl.Num == 0 {
			// 如果白名单没有数量限制，直接返回 true
			return &whitelistpb.CheckWhitelistQualificationForFuctionRPCResp{
				HasQualification: true,
				UsageCount:       fusionCount.Count,
				TotalCount:       0,
			}, nil
		}
		// 如果用户合成数量没超过白名单数量限制，则返回 true
		if fusionCount.Count+int64(req.Count) <= int64(*wl.Num) {
			return &whitelistpb.CheckWhitelistQualificationForFuctionRPCResp{
				HasQualification: true,
				UsageCount:       fusionCount.Count,
				TotalCount:       int64(*wl.Num),
			}, nil
		}
		// 如果全量白名单数量超限，继续尝试优先购白名单，不直接返回失败
	}

	// 如果全量用户白名单不存在或不在有效期或数量超限，则尝试获取优先购白名单
	wl, err = a.whitelistRepo.GetWhitelistByRuleTypeAndRuleTypeID(ctx, 3, req.RuleTypeID)
	if err != nil {
		return nil, err
	}

	// 如果优先购白名单不存在
	if wl == nil {
		return nil, whitelist.ErrNotFound
	}

	// 检查白名单是否在有效期
	if (!wl.EndTime.IsZero() && wl.EndTime.Before(time.Now())) || wl.StartTime.After(time.Now()) {
		return nil, whitelist.ErrWhitelistNotActive
	}

	// 检查用户是否在白名单中
	userRule, err := a.whitelistRepo.GetUserRuleByUserIDAndWhitelistID(ctx, req.UserID, wl.ID)
	if err != nil {
		return nil, err
	}

	if userRule == nil {
		return nil, whitelist.ErrUserNotInWhitelist
	}
	// RPC 获取当前用户的合成次数
	fusionCount, err := a.fusionClient.GetUserFusionCountRPC(ctx, &fusionpb.GetUserFusionCountRPCReq{
		UserId:  req.UserID,
		EventId: req.RuleTypeID,
	})
	if err != nil {
		return nil, err
	}
	// 如果白名单没有数量限制，直接返回 true
	if userRule.Num == nil || *userRule.Num == 0 {
		return &whitelistpb.CheckWhitelistQualificationForFuctionRPCResp{
			HasQualification: true,
			UsageCount:       fusionCount.Count,
			TotalCount:       0,
		}, nil
	}

	// 如果用户合成数量超过白名单数量限制，则返回 false
	// fusionCount.Count + req.Count 超过白名单数量限制
	if fusionCount.Count+int64(req.Count) > int64(*userRule.Num) {
		return &whitelistpb.CheckWhitelistQualificationForFuctionRPCResp{
			HasQualification: false,
			UsageCount:       fusionCount.Count,
			TotalCount:       int64(*userRule.Num),
		}, nil
	}

	return &whitelistpb.CheckWhitelistQualificationForFuctionRPCResp{
		HasQualification: true,
		UsageCount:       fusionCount.Count,
		TotalCount:       int64(*userRule.Num),
	}, nil
}

func (a *Application) CreateWhitelistRPC(ctx context.Context, req *whitelistpb.CreateWhitelistRPCReq) (*whitelistpb.Whitelist, error) {
	w := &entity.Whitelist{
		RuleType:   req.Type,
		RuleTypeID: req.TypeID,
		Name:       req.Name,
		StartTime:  time.UnixMilli(req.StartTime),
		Num:        req.Num,
	}
	if req.Description != nil {
		w.Description = *req.Description
	}
	if req.EndTime != nil {
		w.EndTime = time.UnixMilli(*req.EndTime)
	}
	dbWhitelist, err := a.whitelistRepo.CreateWhitelist(ctx, w)
	if err != nil {
		return nil, err
	}

	return entity.ToProto(dbWhitelist), nil
}

func (a *Application) BatchConfigureUserWhitelistRPC(ctx context.Context, req *whitelistpb.BatchConfigureUserWhitelistRPCReq) (*whitelistpb.BatchConfigureUserWhitelistRPCResp, error) {
	// 1. 预处理传入的用户ID配置，去重并聚合数量
	userIDToNumMap := make(map[string]int32)
	for _, userWhitelist := range req.UserWhitelists {
		// 如果同一个用户ID有多个配置，累加数量
		if existingNum, exists := userIDToNumMap[userWhitelist.UserID]; exists {
			userIDToNumMap[userWhitelist.UserID] = existingNum + userWhitelist.Num
		} else {
			userIDToNumMap[userWhitelist.UserID] = userWhitelist.Num
		}
	}

	// 2. 批量获取用户信息
	var userIDs []string
	for userID := range userIDToNumMap {
		userIDs = append(userIDs, userID)
	}

	// 调用用户服务获取用户信息
	userResp, err := a.profileClient.GetUserByIDsRPC(ctx, &profilepb.GetUserByIDsRPCReq{
		UserIDs: userIDs,
	})
	if err != nil {
		return nil, fmt.Errorf("get user info by IDs failed: %w", err)
	}

	// 创建用户ID到手机号的映射
	userIDToPhoneMap := make(map[string]string)
	for _, user := range userResp.Users {
		if user != nil && user.ID != "" {
			userIDToPhoneMap[user.ID] = user.PhoneNumber
		}
	}

	// 准备返回信息
	resp := &whitelistpb.BatchConfigureUserWhitelistRPCResp{
		ExistingUsers: make([]*whitelistpb.BatchConfigureUserWhitelistRPCResp_ExistingUserInfo, 0),
		InvalidUsers:  make([]*whitelistpb.BatchConfigureUserWhitelistRPCResp_InvalidUserInfo, 0),
	}

	// 记录无效用户
	for userID := range userIDToNumMap {
		if _, exists := userIDToPhoneMap[userID]; !exists {
			resp.InvalidUsers = append(resp.InvalidUsers, &whitelistpb.BatchConfigureUserWhitelistRPCResp_InvalidUserInfo{
				UserId: userID,
				Reason: "用户不存在",
			})
		}
	}

	// 如果没有找到任何有效的用户，直接返回
	if len(userIDToPhoneMap) == 0 {
		return resp, nil
	}

	// 3. 获取已存在的用户规则
	existingRules, err := a.whitelistRepo.GetUserRulesByWhitelistID(ctx, req.WhitelistID)
	if err != nil {
		return nil, fmt.Errorf("get existing rules failed: %w", err)
	}
	existingRuleMap := make(map[string]*entity.UserRule)
	for _, rule := range existingRules {
		existingRuleMap[rule.UserID] = rule
	}

	// 4. 处理传入的用户白名单配置
	newRuleMap := make(map[string]*entity.UserRule)

	for userID, numValue := range userIDToNumMap {
		phoneNumber, exists := userIDToPhoneMap[userID]
		if !exists {
			// 用户ID对应的用户不存在，跳过（已在上面记录）
			continue
		}

		if existingRule, exists := existingRuleMap[userID]; exists {
			// 用户已存在于白名单中，累加数量
			oldNum := int32(0)
			if existingRule.Num != nil {
				oldNum = *existingRule.Num
			}
			newNum := oldNum + numValue
			existingRule.Num = &newNum
			existingRuleMap[userID] = existingRule

			// 记录更新信息
			resp.ExistingUsers = append(resp.ExistingUsers, &whitelistpb.BatchConfigureUserWhitelistRPCResp_ExistingUserInfo{
				UserId:      userID,
				PhoneNumber: phoneNumber,
				OldNum:      oldNum,
				NewNum:      newNum,
			})
		} else if newRule, exists := newRuleMap[userID]; exists {
			// 新配置数据中有重复用户，累加数量
			oldNum := int32(0)
			if newRule.Num != nil {
				oldNum = *newRule.Num
			}
			newNum := oldNum + numValue
			newRule.Num = &newNum
		} else {
			// 全新的用户规则
			rule := &entity.UserRule{
				ID:          uuid.New().String(),
				UserID:      userID,
				WhitelistID: req.WhitelistID,
				Num:         &numValue,
				PhoneNumber: phoneNumber, // 使用从用户服务获取的手机号
				CreatedAt:   time.Now(),
			}
			newRuleMap[userID] = rule
		}
	}

	// 5. 批量更新现有规则
	requestUserIDSet := make(map[string]struct{})
	for userID := range userIDToPhoneMap {
		requestUserIDSet[userID] = struct{}{}
	}

	var rulesToUpdate []*entity.UserRule
	for _, rule := range existingRuleMap {
		// 只收集那些在请求中存在的 user rule
		if _, exists := requestUserIDSet[rule.UserID]; exists {
			rulesToUpdate = append(rulesToUpdate, rule)
		}
	}

	// 使用批量更新
	if len(rulesToUpdate) > 0 {
		if err := a.whitelistRepo.BatchUpdateUserRules(ctx, rulesToUpdate); err != nil {
			return nil, fmt.Errorf("batch update user rules failed: %w", err)
		}
		resp.TotalUpdated = int32(len(rulesToUpdate))
	}

	// 6. 创建新规则
	var newRules []*entity.UserRule
	for _, rule := range newRuleMap {
		newRules = append(newRules, rule)
	}
	if len(newRules) > 0 {
		if err := a.whitelistRepo.BatchCreateUserRules(ctx, newRules); err != nil {
			return nil, fmt.Errorf("create new rules failed: %w", err)
		}
		resp.TotalCreated = int32(len(newRules))
	}

	return resp, nil
}

func (a *Application) BatchConfigureUserWhitelisttByPhoneNumberRPC(ctx context.Context, req *whitelistpb.BatchConfigureUserWhitelisttByPhoneNumberRPCReq) (*whitelistpb.BatchConfigureUserWhitelisttByPhoneNumberRPCResp, error) {
	// 1. 预处理传入的手机号配置，去重并聚合数量
	phoneToNumMap := make(map[string]int32)
	for _, userWhitelist := range req.UserWhitelists {
		// 如果同一个手机号有多个配置，累加数量
		if existingNum, exists := phoneToNumMap[userWhitelist.PhoneNumber]; exists {
			phoneToNumMap[userWhitelist.PhoneNumber] = existingNum + userWhitelist.Num
		} else {
			phoneToNumMap[userWhitelist.PhoneNumber] = userWhitelist.Num
		}
	}

	// 2. 批量获取用户信息
	var phoneNumbers []string
	for phoneNumber := range phoneToNumMap {
		phoneNumbers = append(phoneNumbers, phoneNumber)
	}

	// 调用用户服务获取用户信息
	userResp, err := a.profileClient.GetUserByPhoneNumbersRPC(ctx, &profilepb.GetUserByPhoneNumbersReq{
		PhoneNumber: phoneNumbers,
	})
	if err != nil {
		return nil, fmt.Errorf("get user info by phone numbers failed: %w", err)
	}

	// 创建手机号到用户ID的映射
	phoneToUserIDMap := make(map[string]string)
	for _, user := range userResp.Users {
		if user != nil && user.ID != "" {
			phoneToUserIDMap[user.PhoneNumber] = user.ID
		}
	}

	// 准备返回信息
	resp := &whitelistpb.BatchConfigureUserWhitelisttByPhoneNumberRPCResp{
		ExistingUsers: make([]*whitelistpb.BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo, 0),
		InvalidPhones: make([]*whitelistpb.BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo, 0),
	}

	// 记录无效手机号
	for phoneNumber := range phoneToNumMap {
		if _, exists := phoneToUserIDMap[phoneNumber]; !exists {
			resp.InvalidPhones = append(resp.InvalidPhones, &whitelistpb.BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo{
				PhoneNumber: phoneNumber,
				Reason:      "用户不存在",
			})
		}
	}

	// 如果没有找到任何有效的用户，直接返回
	if len(phoneToUserIDMap) == 0 {
		return resp, nil
	}

	// 3. 获取已存在的用户规则
	existingRules, err := a.whitelistRepo.GetUserRulesByWhitelistID(ctx, req.WhitelistID)
	if err != nil {
		return nil, fmt.Errorf("get existing rules failed: %w", err)
	}
	existingRuleMap := make(map[string]*entity.UserRule)
	for _, rule := range existingRules {
		existingRuleMap[rule.UserID] = rule
	}

	// 4. 处理传入的用户白名单配置
	newRuleMap := make(map[string]*entity.UserRule)

	for phoneNumber, numValue := range phoneToNumMap {
		userID, exists := phoneToUserIDMap[phoneNumber]
		if !exists {
			// 手机号对应的用户不存在，跳过（已在上面记录）
			continue
		}

		if existingRule, exists := existingRuleMap[userID]; exists {
			// 用户已存在于白名单中，累加数量
			oldNum := int32(0)
			if existingRule.Num != nil {
				oldNum = *existingRule.Num
			}
			newNum := oldNum + numValue
			existingRule.Num = &newNum
			existingRuleMap[userID] = existingRule

			// 记录更新信息
			resp.ExistingUsers = append(resp.ExistingUsers, &whitelistpb.BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo{
				PhoneNumber: phoneNumber,
				UserId:      userID,
				OldNum:      oldNum,
				NewNum:      newNum,
			})
		} else if newRule, exists := newRuleMap[userID]; exists {
			// 新配置数据中有重复用户（不同手机号对应相同用户ID），累加数量
			oldNum := int32(0)
			if newRule.Num != nil {
				oldNum = *newRule.Num
			}
			newNum := oldNum + numValue
			newRule.Num = &newNum
		} else {
			// 全新的用户规则
			rule := &entity.UserRule{
				ID:          uuid.New().String(),
				UserID:      userID,
				WhitelistID: req.WhitelistID,
				Num:         &numValue,
				PhoneNumber: phoneNumber, // 直接使用传入的手机号
				CreatedAt:   time.Now(),
			}
			newRuleMap[userID] = rule
		}
	}

	// 5. 批量更新现有规则
	requestUserIDSet := make(map[string]struct{})
	for _, userID := range phoneToUserIDMap {
		requestUserIDSet[userID] = struct{}{}
	}

	var rulesToUpdate []*entity.UserRule
	for _, rule := range existingRuleMap {
		// 只收集那些在请求中存在的 user rule
		if _, exists := requestUserIDSet[rule.UserID]; exists {
			rulesToUpdate = append(rulesToUpdate, rule)
		}
	}

	// 使用批量更新
	if len(rulesToUpdate) > 0 {
		if err := a.whitelistRepo.BatchUpdateUserRules(ctx, rulesToUpdate); err != nil {
			return nil, fmt.Errorf("batch update user rules failed: %w", err)
		}
		resp.TotalUpdated = int32(len(rulesToUpdate))
	}

	// 6. 创建新规则
	var newRules []*entity.UserRule
	for _, rule := range newRuleMap {
		newRules = append(newRules, rule)
	}
	if len(newRules) > 0 {
		if err := a.whitelistRepo.BatchCreateUserRules(ctx, newRules); err != nil {
			return nil, fmt.Errorf("create new rules failed: %w", err)
		}
		resp.TotalCreated = int32(len(newRules))
	}

	return resp, nil
}

func (a *Application) GetWhitelistsRPC(ctx context.Context, req *whitelistpb.GetWhitelistsRPCReq) (*whitelistpb.GetWhitelistsRPCResp, error) {
	// 构建搜索选项
	opts := entity.WhitelistRPCSearchOptions{
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
	}

	// 关键词搜索
	if req.Keyword != nil {
		opts.Keyword = *req.Keyword
	}

	// 类型筛选
	if req.Type != nil {
		opts.Type = req.Type
	}

	// 调用repo层搜索 - 默认按创建时间倒序
	whitelists, total, err := a.whitelistRepo.SearchWhitelists(ctx, opts)
	if err != nil {
		return nil, err
	}

	// 转换为protobuf格式
	var respWhitelists []*whitelistpb.Whitelist
	for _, w := range whitelists {
		respWhitelists = append(respWhitelists, entity.ToProto(w))
	}

	return &whitelistpb.GetWhitelistsRPCResp{
		Whitelists: respWhitelists,
		Total:      total,
	}, nil
}

func (a *Application) GetWhitelistUserRuleListRPC(ctx context.Context, req *whitelistpb.GetWhitelistUserRuleListRPCReq) (*whitelistpb.GetWhitelistUserRuleListRPCResp, error) {
	// 构建搜索选项
	opts := entity.UserRuleSearchOptions{
		WhitelistID: req.WhitelistId,
		Page:        int(req.Page),
		PageSize:    int(req.PageSize),
	}

	// 关键词搜索
	if req.Keyword != nil {
		opts.Keyword = *req.Keyword
	}

	// 调用repo层搜索 - 默认按创建时间倒序
	userRules, total, err := a.whitelistRepo.SearchUserRules(ctx, opts)
	if err != nil {
		return nil, err
	}

	// 转换为protobuf格式
	var respUserRules []*whitelistpb.GetWhitelistUserRuleListRPCResp_WhitelistUserRule
	for _, rule := range userRules {
		num := int32(0)
		if rule.Num != nil {
			num = *rule.Num
		}
		respUserRules = append(respUserRules, &whitelistpb.GetWhitelistUserRuleListRPCResp_WhitelistUserRule{
			UserId:      rule.UserID,
			PhoneNumber: rule.PhoneNumber,
			Num:         num,
			CreatedAt:   rule.CreatedAt.UnixMilli(),
		})
	}

	return &whitelistpb.GetWhitelistUserRuleListRPCResp{
		UserRules: respUserRules,
		Total:     total,
	}, nil
}
