package whitelist

import (
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var (
	ErrUnauthorized       = status.Error(codes.Unauthenticated, "unauthorized")
	ErrNotFound           = status.Error(codes.NotFound, "not found")
	ErrAlreadyExist       = status.Error(codes.AlreadyExists, "already exists")
	ErrWhitelistStarted   = status.Error(codes.FailedPrecondition, "whitelist has already taken effect,cannot be modified")
	ErrWhitelistNotActive = status.Error(codes.FailedPrecondition, "whitelist is not active")
	ErrUserNotInWhitelist = status.Error(codes.FailedPrecondition, "user is not in whitelist")
	ErrInvalidToken       = status.Error(codes.PermissionDenied, "invalid token")
)
