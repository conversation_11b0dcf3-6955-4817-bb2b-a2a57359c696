package whitelist

import (
	"context"

	"cnb.cool/cymirror/ces-services/whitelist-service/internal/domain/whitelist/entity"
)

type Repo interface {
	// CreateWhitelist 创建白名单
	CreateWhitelist(ctx context.Context, w *entity.Whitelist) (*entity.Whitelist, error)

	// UpdateWhitelist 更新白名单
	UpdateWhitelist(ctx context.Context, w *entity.Whitelist) (*entity.Whitelist, error)

	// BatchCreateUserRules 批量创建用户规则
	BatchCreateUserRules(ctx context.Context, rules []*entity.UserRule) error
	// UpdateUserRules 更新用户规则
	UpdateUserRules(ctx context.Context, rule *entity.UserRule) error
	// BatchUpdateUserRules 批量更新用户规则
	BatchUpdateUserRules(ctx context.Context, rules []*entity.UserRule) error

	// GetWhitelistByID 根据 ID 获取白名单详情
	GetWhitelistByID(ctx context.Context, id string) (*entity.Whitelist, error)

	// ListWhitelists 获取白名单分页列表（按时间倒序）
	ListWhitelists(ctx context.Context, page int, pageSize int) ([]*entity.Whitelist, int64, error)

	// GetWhitelistByRuleTypeAndRuleTypeID 根据规则类型和规则类型ID获取白名单
	GetWhitelistByRuleTypeAndRuleTypeID(ctx context.Context, ruleType int32, ruleTypeID string) (*entity.Whitelist, error)

	// GetUserRuleByUserIDAndWhitelistID 根据用户ID和白名单ID获取用户资格
	GetUserRuleByUserIDAndWhitelistID(ctx context.Context, userID string, whitelistID string) (*entity.UserRule, error)

	// GetUserRulesByWhitelistID 根据白名单ID获取用户规则
	GetUserRulesByWhitelistID(ctx context.Context, whitelistID string) ([]*entity.UserRule, error)

	// SearchWhitelists 搜索白名单
	SearchWhitelists(ctx context.Context, opts entity.WhitelistRPCSearchOptions) ([]*entity.Whitelist, int64, error)
	// SearchUserRules 搜索用户规则
	SearchUserRules(ctx context.Context, opts entity.UserRuleSearchOptions) ([]*entity.UserRule, int64, error)

	// 获取用户规则的使用次数 (废弃)
	// GetUserRuleUsageCount(ctx context.Context, userRuleID string) (int64, error)

	// 增加用户规则的使用次数 (废弃)
	// IncrementUserRuleUsageCount(ctx context.Context, userRuleID string) error

	// 创建用户白名单使用记录 (废弃)
	// CreateUserWhitelistUsageRecord(ctx context.Context, userID string, userRuleID string) error
}
