package entity

import (
	"time"

	"cnb.cool/cymirror/ces-services/whitelist-service/gen/gen/model"
)

// UserRule 用户规则（用户在某白名单下的限制）
type UserRule struct {
	ID          string    // 记录 ID
	UserID      string    // 用户 ID
	WhitelistID string    // 白名单 ID
	UsedCount   int64     // 使用次数
	Num         *int32    // 个性化数量限制，nil 表示不限制
	PhoneNumber string    // 手机号码
	CreatedAt   time.Time // 创建时间
}

// NewUserRuleFromModel 将数据库模型转换为领域模型
func NewUserRuleFromModel(m *model.WhitelistUserRule) *UserRule {
	if m == nil {
		return nil
	}
	return &UserRule{
		ID:          m.ID,
		UserID:      m.UserID,
		WhitelistID: m.WhitelistID,
		UsedCount:   int64(m.UsedCount),
		Num:         m.Num,
		PhoneNumber: m.PhoneNumber,
		CreatedAt:   m.CreatedAt,
	}
}

// ToUserRuleModel 将领域模型转换为数据库模型
func ToUserRuleModel(e *UserRule) *model.WhitelistUserRule {
	if e == nil {
		return nil
	}
	return &model.WhitelistUserRule{
		ID:          e.ID,
		UserID:      e.UserID,
		WhitelistID: e.WhitelistID,
		Num:         e.Num,
		PhoneNumber: e.PhoneNumber,
		CreatedAt:   e.CreatedAt,
	}
}
