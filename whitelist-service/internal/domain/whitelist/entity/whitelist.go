package entity

import (
	"time"

	"cnb.cool/cymirror/ces-services/whitelist-service/gen/gen/model"
	whitelistpb "cnb.cool/cymirror/ces-services/whitelist-service/gen/proto/ces/whitelist/whitelist"
)

// Whitelist 白名单定义
type Whitelist struct {
	ID          string    // 白名单 ID
	RuleType    int32     // 规则类型：1.一级优先购、2.一级全量、3.合成优先、4.合成全量
	RuleTypeID  string    // 关联规则对象 ID（如活动/发售计划等）
	Name        string    // 白名单名称
	Description string    // 描述（可选）
	StartTime   time.Time // 生效开始时间（必填）
	EndTime     time.Time // 生效结束时间（可选）
	Num         *int32    // 数量限制（可选）,仅用于全量用户白名单
	CreatedAt   time.Time // 创建时间
}

// NewWhitelistFromModel 将数据库模型转换为领域模型
func NewWhitelistFromModel(m *model.Whitelist) *Whitelist {
	if m == nil {
		return nil
	}
	w := &Whitelist{
		ID:         m.ID,
		RuleType:   int32(m.RuleType),
		RuleTypeID: m.RuleTypeID,
		Name:       m.Name,
		Num:        m.Num,
		StartTime:  m.StartTime,
		CreatedAt:  m.CreatedAt,
	}
	if m.Description != nil {
		w.Description = *m.Description
	}
	if m.EndTime != nil {
		w.EndTime = *m.EndTime
	}

	return w
}

// ToWhitelistModel 将领域模型转换为数据库模型
func ToWhitelistModel(e *Whitelist) *model.Whitelist {
	if e == nil {
		return nil
	}
	return &model.Whitelist{
		ID:          e.ID,
		RuleType:    int16(e.RuleType),
		RuleTypeID:  e.RuleTypeID,
		Name:        e.Name,
		Description: &e.Description,
		StartTime:   e.StartTime,
		EndTime:     &e.EndTime,
		CreatedAt:   e.CreatedAt,
		Num:         e.Num,
	}
}

func ToProto(w *Whitelist) *whitelistpb.Whitelist {
	if w == nil {
		return nil
	}
	wl := &whitelistpb.Whitelist{
		Id:          w.ID,
		TypeId:      w.RuleTypeID,
		Type:        w.RuleType,
		Name:        w.Name,
		Description: w.Description,
		StartTime:   w.StartTime.UnixMilli(),
		CreatedAt:   w.CreatedAt.UnixMilli(),
	}
	if !w.EndTime.IsZero() {
		wl.EndTime = w.EndTime.UnixMilli()
	}
	if w.Num != nil {
		wl.Num = *w.Num
	}
	return wl
}
