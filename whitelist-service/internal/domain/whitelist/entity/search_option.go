package entity

import "time"

// SearchOptions 白名单搜索选项
type SearchOptions struct {
	Keyword  string // 可匹配 ID、名称、用户 ID 等
	Type     *int32 // 规则类型筛选（可选）
	TimeMin  *time.Time
	TimeMax  *time.Time
	Page     int
	PageSize int
}

// WhitelistRPCSearchOptions 白名单RPC搜索选项（管理后台用）- 简化版
type WhitelistRPCSearchOptions struct {
	Keyword  string // 关键词搜索：白名单名称、描述、关联业务ID
	Type     *int32 // 类型筛选：1.一级优先购 2.一级全量 3.合成优先 4.合成全量
	Page     int    // 页码
	PageSize int    // 页大小
}

// UserRuleSearchOptions 用户规则搜索选项（管理后台用）- 简化版
type UserRuleSearchOptions struct {
	WhitelistID string // 白名单ID
	Keyword     string // 关键词搜索：用户ID、手机号
	Page        int    // 页码
	PageSize    int    // 页大小
}
