package whitelist

import (
	"context"

	whitelistpb "cnb.cool/cymirror/ces-services/whitelist-service/gen/proto/ces/whitelist/whitelist"
	"cnb.cool/cymirror/ces-services/whitelist-service/internal/application/whitelist"
)

var _ whitelistpb.WhitelistServiceServer = (*ServiceServer)(nil)

type ServiceServer struct {
	whitelistApplication *whitelist.Application
}

func NewServiceServer(whitelistApp *whitelist.Application) *ServiceServer {
	return &ServiceServer{
		whitelistApplication: whitelistApp,
	}
}

func (s ServiceServer) CreateWhitelist(ctx context.Context, req *whitelistpb.CreateWhitelistReq) (*whitelistpb.Whitelist, error) {
	return s.whitelistApplication.CreateWhitelist(ctx, req)
}

func (s ServiceServer) UpdateWhitelist(ctx context.Context, req *whitelistpb.UpdateWhitelistReq) (*whitelistpb.Whitelist, error) {
	return s.whitelistApplication.UpdateWhitelist(ctx, req)
}

func (s ServiceServer) ImportWhitelistUsers(ctx context.Context, req *whitelistpb.ImportWhitelistUsersReq) (*whitelistpb.ImportWhitelistUsersResp, error) {
	return s.whitelistApplication.ImportWhitelistUsers(ctx, req)
}

func (s ServiceServer) GetWhitelist(ctx context.Context, req *whitelistpb.GetWhitelistReq) (*whitelistpb.Whitelist, error) {
	return s.whitelistApplication.GetWhitelist(ctx, req)
}

func (s ServiceServer) ListWhitelists(ctx context.Context, req *whitelistpb.ListWhitelistsReq) (*whitelistpb.ListWhitelistsResp, error) {
	return s.whitelistApplication.ListWhitelists(ctx, req)
}

func (s ServiceServer) CheckWhitelistQualificationRPC(ctx context.Context, req *whitelistpb.CheckWhitelistQualificationRPCReq) (*whitelistpb.CheckWhitelistQualificationRPCResp, error) {
	return s.whitelistApplication.CheckWhitelistQualificationRPC(ctx, req)
}

func (s ServiceServer) CheckWhitelistQualificationForFuctionRPC(ctx context.Context, req *whitelistpb.CheckWhitelistQualificationForFuctionRPCReq) (*whitelistpb.CheckWhitelistQualificationForFuctionRPCResp, error) {
	return s.whitelistApplication.CheckWhitelistQualificationForFuctionRPC(ctx, req)
}

func (s ServiceServer) CreateWhitelistRPC(ctx context.Context, req *whitelistpb.CreateWhitelistRPCReq) (*whitelistpb.Whitelist, error) {
	return s.whitelistApplication.CreateWhitelistRPC(ctx, req)
}

func (s ServiceServer) BatchConfigureUserWhitelistRPC(ctx context.Context, req *whitelistpb.BatchConfigureUserWhitelistRPCReq) (*whitelistpb.BatchConfigureUserWhitelistRPCResp, error) {
	return s.whitelistApplication.BatchConfigureUserWhitelistRPC(ctx, req)
}

func (s ServiceServer) BatchConfigureUserWhitelisttByPhoneNumberRPC(ctx context.Context, req *whitelistpb.BatchConfigureUserWhitelisttByPhoneNumberRPCReq) (*whitelistpb.BatchConfigureUserWhitelisttByPhoneNumberRPCResp, error) {
	return s.whitelistApplication.BatchConfigureUserWhitelisttByPhoneNumberRPC(ctx, req)
}

func (s ServiceServer) GetWhitelistsRPC(ctx context.Context, req *whitelistpb.GetWhitelistsRPCReq) (*whitelistpb.GetWhitelistsRPCResp, error) {
	return s.whitelistApplication.GetWhitelistsRPC(ctx, req)
}

func (s ServiceServer) GetWhitelistUserRuleListRPC(ctx context.Context, req *whitelistpb.GetWhitelistUserRuleListRPCReq) (*whitelistpb.GetWhitelistUserRuleListRPCResp, error) {
	return s.whitelistApplication.GetWhitelistUserRuleListRPC(ctx, req)
}
