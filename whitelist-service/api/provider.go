package api

import (
	"cnb.cool/cymirror/ces-services/whitelist-service/api/whitelist"
	whitelistpb "cnb.cool/cymirror/ces-services/whitelist-service/gen/proto/ces/whitelist/whitelist"
	"github.com/google/wire"
)

// HandlerProviderSet Handler providers
var HandlerProviderSet = wire.NewSet(
	whitelist.NewServiceServer,
	wire.Bind(new(whitelistpb.WhitelistServiceServer), new(*whitelist.ServiceServer)),
)
