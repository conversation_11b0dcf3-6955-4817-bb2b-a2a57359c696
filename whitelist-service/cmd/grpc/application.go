package main

import (
	"cnb.cool/cymirror/ces-services/common/server"
	"cnb.cool/cymirror/ces-services/whitelist-service/cmd/grpc/internal/config"
	whitelistpb "cnb.cool/cymirror/ces-services/whitelist-service/gen/proto/ces/whitelist/whitelist"
	"context"
	"google.golang.org/grpc"
)

type Application struct {
	Server *server.Server
}

func NewApplication(
	cfg *config.Config,
	s *server.Server,
	whitelistServer whitelistpb.WhitelistServiceServer,
) *Application {

	s.RegisterServer(func(s *grpc.Server) {
		whitelistpb.RegisterWhitelistServiceServer(s, whitelistServer)
	})

	return &Application{
		Server: s,
	}
}

func (s *Application) Run(ctx context.Context) error {
	return s.Server.Run(ctx)
}
