// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"cnb.cool/cymirror/ces-services/common/client"
	"cnb.cool/cymirror/ces-services/common/server"
	whitelist2 "cnb.cool/cymirror/ces-services/whitelist-service/api/whitelist"
	"cnb.cool/cymirror/ces-services/whitelist-service/cmd/grpc/internal"
	client2 "cnb.cool/cymirror/ces-services/whitelist-service/cmd/grpc/internal/client"
	"cnb.cool/cymirror/ces-services/whitelist-service/cmd/grpc/internal/config"
	"cnb.cool/cymirror/ces-services/whitelist-service/internal/application/whitelist"
	"cnb.cool/cymirror/ces-services/whitelist-service/internal/infra/whitelist/repository"
	"context"
)

// Injectors from wire.go:

func InitializeApplication(ctx context.Context, configPath2 string) (*Application, func()) {
	configConfig := config.MustLoad(configPath2)
	grpcServerConfig := config.GetGrpcServerConfig(configConfig)
	serverServer, cleanup := server.InitializeServer(ctx, grpcServerConfig)
	databaseConfig := config.GetDBConfig(configConfig)
	db := internal.NewDB(databaseConfig)
	whitelistRepository := repository.NewWhitelistRepository(db)
	clientConfig := client.NewConfigFromGRPCConfig(grpcServerConfig)
	profileServiceClient, cleanup2 := client2.NewUserClient(clientConfig)
	orderServiceClient, cleanup3 := client2.NewOrderClient(clientConfig)
	fusionServiceClient, cleanup4 := client2.NewFusionClient(clientConfig)
	application := whitelist.NewApplication(whitelistRepository, profileServiceClient, orderServiceClient, fusionServiceClient)
	serviceServer := whitelist2.NewServiceServer(application)
	mainApplication := NewApplication(configConfig, serverServer, serviceServer)
	return mainApplication, func() {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}
}
