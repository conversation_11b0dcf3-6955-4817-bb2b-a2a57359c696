//go:build wireinject

package main

import (
	"cnb.cool/cymirror/ces-services/common/server"
	"cnb.cool/cymirror/ces-services/whitelist-service/api"
	"cnb.cool/cymirror/ces-services/whitelist-service/cmd/grpc/internal"
	"cnb.cool/cymirror/ces-services/whitelist-service/cmd/grpc/internal/client"
	"cnb.cool/cymirror/ces-services/whitelist-service/cmd/grpc/internal/config"
	"cnb.cool/cymirror/ces-services/whitelist-service/internal/application"
	"cnb.cool/cymirror/ces-services/whitelist-service/internal/infra"
	"context"
	"github.com/google/wire"
)

func InitializeApplication(ctx context.Context, configPath string) (*Application, func()) {
	panic(wire.Build(
		config.MustLoad,
		config.GetGrpcServerConfig,

		config.ConfigProviderSet,
		client.GrpcClientProviderSet,
		application.AppProviderSet,
		api.HandlerProviderSet,
		infra.InfraProviderSet,
		internal.InternalProviderSet,

		server.InitializeServer,

		NewApplication,
	))
}
