package client

import (
	"cnb.cool/cymirror/ces-services/common/client"
	fusionpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/fusion"
	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
	profilepb "cnb.cool/cymirror/ces-services/user-service/gen/proto/ces/user/profile"
)

func NewUserClient(registryCfg *client.Config) (profilepb.ProfileServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "user-service", profilepb.NewProfileServiceClient)
}

func NewOrderClient(registryCfg *client.Config) (orderpb.OrderServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "payment-service", orderpb.NewOrderServiceClient)
}

func NewFusionClient(registryCfg *client.Config) (fusionpb.FusionServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "nft-service", fusionpb.NewFusionServiceClient)
}
