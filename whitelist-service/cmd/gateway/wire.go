//go:build wireinject

package main

import (
	"cnb.cool/cymirror/ces-services/common/gateway"
	"cnb.cool/cymirror/ces-services/whitelist-service/cmd/gateway/internal/client"
	"cnb.cool/cymirror/ces-services/whitelist-service/cmd/gateway/internal/config"
	"context"

	"github.com/google/wire"
)

func InitializeApplication(ctx context.Context, configPath string) (*Application, func()) {
	panic(wire.Build(
		config.MustLoad,
		config.GetGatewayConfig,

		config.ConfigProviderSet,
		client.ClientProviderSet,

		gateway.InitializeGateway,

		NewApplication,
	))
}
