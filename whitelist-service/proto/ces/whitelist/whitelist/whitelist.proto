syntax = "proto3";

package ces.whitelist.whitelist;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";

option go_package = "/whitelistpb";

service WhitelistService {
  // 配置（创建）白名单
  rpc CreateWhitelist(CreateWhitelistReq) returns (Whitelist) {
    option (google.api.http) = {
      post: "/v1/whitelist/configure"
      body: "*"
    };
  }

  // 更新白名单信息(仅在发售开始前可操作)
  rpc UpdateWhitelist(UpdateWhitelistReq) returns (Whitelist) {
    option (google.api.http) = {
      put: "/v1/whitelist/{id}"
      body: "*"
    };
  }

  // 批量导入白名单用户（通过excel文件上传）
  rpc ImportWhitelistUsers(ImportWhitelistUsersReq) returns (ImportWhitelistUsersResp) {
    option (google.api.http) = {
      post: "/v1/whitelist/{whitelistID}/import"
      body: "*"
    };
  }

  // 获取白名单信息
  rpc GetWhitelist(GetWhitelistReq) returns (Whitelist) {
    option (google.api.http) = {get: "/v1/whitelist/{id}"};
  }

  // 获取白名单列表（按时间从近到远）
  rpc ListWhitelists(ListWhitelistsReq) returns (ListWhitelistsResp) {
    option (google.api.http) = {get: "/v1/whitelist/list"};
  }

  // 一级市场白名单资格校验 RPC
  rpc CheckWhitelistQualificationRPC(CheckWhitelistQualificationRPCReq) returns (CheckWhitelistQualificationRPCResp);

  // 合成白名单资格校验 RPC
  rpc CheckWhitelistQualificationForFuctionRPC(CheckWhitelistQualificationForFuctionRPCReq) returns (CheckWhitelistQualificationForFuctionRPCResp);

  // 创建白名单RPC
  rpc CreateWhitelistRPC(CreateWhitelistRPCReq) returns (Whitelist);

  // 批量配置用户白名单RPC
  rpc BatchConfigureUserWhitelistRPC(BatchConfigureUserWhitelistRPCReq) returns (BatchConfigureUserWhitelistRPCResp);

  // 根据手机号配置用户白名单RPC
  rpc BatchConfigureUserWhitelisttByPhoneNumberRPC(BatchConfigureUserWhitelisttByPhoneNumberRPCReq) returns (BatchConfigureUserWhitelisttByPhoneNumberRPCResp);

  // 获取白名单列表RPC
  rpc GetWhitelistsRPC(GetWhitelistsRPCReq) returns (GetWhitelistsRPCResp);

  // 获取白名单用户规则列表RPC
  rpc GetWhitelistUserRuleListRPC(GetWhitelistUserRuleListRPCReq) returns (GetWhitelistUserRuleListRPCResp);
}

message CreateWhitelistReq {
  // 关联业务 ID（如发售/活动等）
  string typeID = 1 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.uuid = true
  ];
  // 类型：1.一级优先购 2.一级全量 3.合成优先 4.合成全量
  int32 type = 2 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).cel = {
      message: "invalid rule type"
      expression: "this >= 0"
    }
  ];
  // 白名单名称
  string name = 3 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.min_len = 1, // 字符串至少包含1个字符
    (buf.validate.field).string.max_len = 255 // 字符串最多255个字符
  ];
  optional string description = 4 [(google.api.field_behavior) = OPTIONAL]; // 描述
  // 生效开始时间（时间戳）
  int64 startTime = 5 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).int64.gt = 0 // startTime 必须大于 0
  ];

  // 生效结束时间（时间戳,可选）
  optional int64 endTime = 6 [(google.api.field_behavior) = OPTIONAL];

  // 数量限制, 0 表示无限制,仅针对全量用户白名单
  optional int32 num = 7 [(google.api.field_behavior) = OPTIONAL];
  // token
  string token = 8 [(google.api.field_behavior) = REQUIRED];
}

message UpdateWhitelistReq {
  string id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.uuid = true
  ];

  string typeID = 2 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.uuid = true
  ];
  int32 type = 3 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).cel = {
      message: "invalid rule type"
      expression: "this >= 0"
    }
  ];
  string name = 4 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.min_len = 1, // 字符串至少包含1个字符
    (buf.validate.field).string.max_len = 255 // 字符串最多255个字符
  ];
  optional string description = 5 [(google.api.field_behavior) = OPTIONAL];
  // 生效开始时间（时间戳）
  int64 startTime = 6 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).int64.gt = 0 // startTime 必须大于 0
  ];

  // 生效结束时间（时间戳,可选）
  optional int64 endTime = 7 [(google.api.field_behavior) = OPTIONAL];

  // 数量限制, 0 表示无限制,仅针对全量用户白名单
  optional int32 num = 8 [(google.api.field_behavior) = OPTIONAL];
  // token
  string token = 9 [(google.api.field_behavior) = REQUIRED];
}

message ImportWhitelistUsersReq {
  // 白名单 ID
  string whitelistID = 1 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.uuid = true
  ];
  bytes fileContent = 2 [(google.api.field_behavior) = REQUIRED]; // Excel 文件内容（二进制）
  string fileName = 3 [(google.api.field_behavior) = REQUIRED]; // 原始文件名，用于判断是解析csv还是xlsx
  // token
  string token = 8 [(google.api.field_behavior) = REQUIRED];
}

message ImportWhitelistUsersResp {
  int32 total_imported = 1; // 成功导入的总数
  int32 total_updated = 2; // 更新现有记录的数量
  repeated ExistingUserInfo existing_users = 3; // 已存在的用户信息
  repeated FailedRecord failed_records = 4; // 导入失败的记录
  repeated DuplicateRecord duplicate_records = 5; // 导入文件中重复的记录

  message ExistingUserInfo {
    string phone_number = 1;
    string user_id = 2;
    int32 old_num = 3;
    int32 new_num = 4;
  }

  message FailedRecord {
    int32 row_number = 1; // Excel 行号（从2开始，因为第1行是表头）
    string phone_number = 2; // 手机号
    string reason = 3; // 失败原因
  }

  message DuplicateRecord {
    string phone_number = 1;
    string user_id = 2;
    int32 old_num = 3;
    int32 new_num = 4;
  }
}

// 获取白名单请求
message GetWhitelistReq {
  // 白名单 ID
  string id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.uuid = true
  ];
}

// 白名单对象
message Whitelist {
  string id = 1; // 白名单 ID
  string type_id = 2; // 活动/项目 ID
  int32 type = 3; // 类型（1.一级优先购, 2.一级全量, 3.合成优先, 4.合成全量）
  string name = 4; // 标题
  string description = 5; // 白名单描述
  int64 start_time = 6; // 生效的开始时间
  int64 end_time = 7; // 生效的结束时间
  int64 created_at = 8; // 创建时间
  int32 num = 9; // 数量限制(0或NULL表示无限制，仅对全量用户白名单有效)
}

message GetWhitelistResp {
  Whitelist whitelist = 1; // 白名单信息
}

// 获取白名单列表请求（分页）
message ListWhitelistsReq {
  int32 page = 1 [(google.api.field_behavior) = REQUIRED]; // 当前页码
  int32 pageSize = 2 [(google.api.field_behavior) = REQUIRED]; // 每页数量
}

message ListWhitelistsResp {
  repeated Whitelist whitelists = 1; // 白名单列表
  int64 total = 2; // 总记录数
}

message CheckWhitelistQualificationRPCReq {
  string ruleTypeID = 1 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.uuid = true
  ];
  string userID = 2 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.uuid = true
  ];
  // 购买数量
  int64 count = 3 [(google.api.field_behavior) = REQUIRED];
}

message CheckWhitelistQualificationRPCResp {
  bool hasQualification = 1; // 是否具有白名单资格
  int64 usageCount = 2; // 已使用次数
  int64 totalCount = 3; // 总次数 0 表示无限制
}

message CheckWhitelistQualificationForFuctionRPCReq {
  string ruleTypeID = 1 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.uuid = true
  ];
  string userID = 2 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.uuid = true
  ];
  // 合成数量
  int64 count = 3 [(google.api.field_behavior) = REQUIRED];
}

message CheckWhitelistQualificationForFuctionRPCResp {
  bool hasQualification = 1; // 是否具有白名单资格
  int64 usageCount = 2; // 已使用次数
  int64 totalCount = 3; // 总次数 0 表示无限制
}

message CreateWhitelistRPCReq {
  // 关联业务 ID（如发售/活动等）
  string typeID = 1 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.uuid = true
  ];
  // 类型：1.一级优先购 2.一级全量 3.合成优先 4.合成全量
  int32 type = 2 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).cel = {
      message: "invalid rule type"
      expression: "this >= 0"
    }
  ];
  // 白名单名称
  string name = 3 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.min_len = 1, // 字符串至少包含1个字符
    (buf.validate.field).string.max_len = 255 // 字符串最多255个字符
  ];
  optional string description = 4 [(google.api.field_behavior) = OPTIONAL]; // 描述
  // 生效开始时间（时间戳）
  int64 startTime = 5 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).int64.gt = 0 // startTime 必须大于 0
  ];

  // 生效结束时间（时间戳,可选）
  optional int64 endTime = 6 [(google.api.field_behavior) = OPTIONAL];

  // 数量限制, 0 表示无限制,仅针对全量用户白名单
  optional int32 num = 7 [(google.api.field_behavior) = OPTIONAL];
}

message BatchConfigureUserWhitelistRPCReq {
  // 白名单 ID
  string whitelistID = 1 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.uuid = true
  ];
  message UserWhitelist {
    // 用户 ID
    string userID = 1 [
      (google.api.field_behavior) = REQUIRED,
      (buf.validate.field).string.uuid = true
    ];
    // 数量限制, 0 表示无限制
    int32 num = 2 [(google.api.field_behavior) = REQUIRED];
  }
  repeated UserWhitelist userWhitelists = 2 [(google.api.field_behavior) = REQUIRED];
}

message BatchConfigureUserWhitelistRPCResp {
  int32 total_created = 1; // 成功创建的总数
  int32 total_updated = 2; // 更新现有记录的数量
  repeated ExistingUserInfo existing_users = 3; // 已存在的用户信息
  repeated InvalidUserInfo invalid_users = 4; // 无效的用户信息（用户不存在）

  message ExistingUserInfo {
    string user_id = 1;
    string phone_number = 2;
    int32 old_num = 3;
    int32 new_num = 4;
  }

  message InvalidUserInfo {
    string user_id = 1;
    string reason = 2; // 失败原因
  }
}

message BatchConfigureUserWhitelisttByPhoneNumberRPCReq {
  // 白名单 ID
  string whitelistID = 1 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.uuid = true
  ];
  message UserWhitelist {
    // 手机号
    string phoneNumber = 1 [
      (google.api.field_behavior) = REQUIRED,
      (buf.validate.field).string.min_len = 1, // 字符串至少包含1个字符
      (buf.validate.field).string.max_len = 255 // 字符串最多255个字符
    ];
    // 数量限制, 0 表示无限制
    int32 num = 2 [(google.api.field_behavior) = REQUIRED];
  }
  repeated UserWhitelist userWhitelists = 2 [(google.api.field_behavior) = REQUIRED];
}

message BatchConfigureUserWhitelisttByPhoneNumberRPCResp {
  int32 total_created = 1; // 成功创建的总数
  int32 total_updated = 2; // 更新现有记录的数量
  repeated ExistingUserInfo existing_users = 3; // 已存在的用户信息
  repeated InvalidPhoneInfo invalid_phones = 4; // 无效的手机号信息（用户不存在）

  message ExistingUserInfo {
    string phone_number = 1;
    string user_id = 2;
    int32 old_num = 3;
    int32 new_num = 4;
  }

  message InvalidPhoneInfo {
    string phone_number = 1;
    string reason = 2; // 失败原因
  }
}

message GetWhitelistsRPCReq {
  // 当前页码
  int32 page = 1 [(google.api.field_behavior) = REQUIRED];
  // 每页数量
  int32 page_size = 2 [(google.api.field_behavior) = REQUIRED];

  // 搜索条件
  optional string keyword = 3 [(google.api.field_behavior) = OPTIONAL]; // 关键词搜索：白名单名称、描述、关联业务ID
  optional int32 type = 4 [(google.api.field_behavior) = OPTIONAL]; // 类型筛选：1.一级优先购 2.一级全量 3.合成优先 4.合成全量
}

message GetWhitelistsRPCResp {
  repeated Whitelist whitelists = 1; // 白名单列表
  int64 total = 2; // 总记录数
}

message GetWhitelistUserRuleListRPCReq {
  // 白名单 ID
  string whitelist_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.uuid = true
  ];
  // 当前页码
  int32 page = 2 [(google.api.field_behavior) = REQUIRED];
  // 每页数量
  int32 page_size = 3 [(google.api.field_behavior) = REQUIRED];

  // 搜索条件
  optional string keyword = 4 [(google.api.field_behavior) = OPTIONAL]; // 关键词搜索：用户ID、手机号
}

message GetWhitelistUserRuleListRPCResp {
  repeated WhitelistUserRule user_rules = 1;
  int64 total = 2; // 总记录数

  message WhitelistUserRule {
    string user_id = 1;
    string phone_number = 2;
    int32 num = 3;
    int64 created_at = 4; // 创建时间
  }
}
