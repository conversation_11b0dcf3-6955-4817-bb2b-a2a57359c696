// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: ces/whitelist/whitelist/whitelist.proto

package whitelistpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateWhitelistReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 关联业务 ID（如发售/活动等）
	TypeID string `protobuf:"bytes,1,opt,name=typeID,proto3" json:"typeID,omitempty"`
	// 类型：1.一级优先购 2.一级全量 3.合成优先 4.合成全量
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	// 白名单名称
	Name        string  `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Description *string `protobuf:"bytes,4,opt,name=description,proto3,oneof" json:"description,omitempty"` // 描述
	// 生效开始时间（时间戳）
	StartTime int64 `protobuf:"varint,5,opt,name=startTime,proto3" json:"startTime,omitempty"`
	// 生效结束时间（时间戳,可选）
	EndTime *int64 `protobuf:"varint,6,opt,name=endTime,proto3,oneof" json:"endTime,omitempty"`
	// 数量限制, 0 表示无限制,仅针对全量用户白名单
	Num *int32 `protobuf:"varint,7,opt,name=num,proto3,oneof" json:"num,omitempty"`
	// token
	Token         string `protobuf:"bytes,8,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateWhitelistReq) Reset() {
	*x = CreateWhitelistReq{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWhitelistReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWhitelistReq) ProtoMessage() {}

func (x *CreateWhitelistReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWhitelistReq.ProtoReflect.Descriptor instead.
func (*CreateWhitelistReq) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{0}
}

func (x *CreateWhitelistReq) GetTypeID() string {
	if x != nil {
		return x.TypeID
	}
	return ""
}

func (x *CreateWhitelistReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CreateWhitelistReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateWhitelistReq) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *CreateWhitelistReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *CreateWhitelistReq) GetEndTime() int64 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *CreateWhitelistReq) GetNum() int32 {
	if x != nil && x.Num != nil {
		return *x.Num
	}
	return 0
}

func (x *CreateWhitelistReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type UpdateWhitelistReq struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	Id          string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TypeID      string                 `protobuf:"bytes,2,opt,name=typeID,proto3" json:"typeID,omitempty"`
	Type        int32                  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Name        string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Description *string                `protobuf:"bytes,5,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// 生效开始时间（时间戳）
	StartTime int64 `protobuf:"varint,6,opt,name=startTime,proto3" json:"startTime,omitempty"`
	// 生效结束时间（时间戳,可选）
	EndTime *int64 `protobuf:"varint,7,opt,name=endTime,proto3,oneof" json:"endTime,omitempty"`
	// 数量限制, 0 表示无限制,仅针对全量用户白名单
	Num *int32 `protobuf:"varint,8,opt,name=num,proto3,oneof" json:"num,omitempty"`
	// token
	Token         string `protobuf:"bytes,9,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateWhitelistReq) Reset() {
	*x = UpdateWhitelistReq{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateWhitelistReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWhitelistReq) ProtoMessage() {}

func (x *UpdateWhitelistReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWhitelistReq.ProtoReflect.Descriptor instead.
func (*UpdateWhitelistReq) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateWhitelistReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateWhitelistReq) GetTypeID() string {
	if x != nil {
		return x.TypeID
	}
	return ""
}

func (x *UpdateWhitelistReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *UpdateWhitelistReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateWhitelistReq) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateWhitelistReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *UpdateWhitelistReq) GetEndTime() int64 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *UpdateWhitelistReq) GetNum() int32 {
	if x != nil && x.Num != nil {
		return *x.Num
	}
	return 0
}

func (x *UpdateWhitelistReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type ImportWhitelistUsersReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 白名单 ID
	WhitelistID string `protobuf:"bytes,1,opt,name=whitelistID,proto3" json:"whitelistID,omitempty"`
	FileContent []byte `protobuf:"bytes,2,opt,name=fileContent,proto3" json:"fileContent,omitempty"` // Excel 文件内容（二进制）
	FileName    string `protobuf:"bytes,3,opt,name=fileName,proto3" json:"fileName,omitempty"`       // 原始文件名，用于判断是解析csv还是xlsx
	// token
	Token         string `protobuf:"bytes,8,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImportWhitelistUsersReq) Reset() {
	*x = ImportWhitelistUsersReq{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportWhitelistUsersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportWhitelistUsersReq) ProtoMessage() {}

func (x *ImportWhitelistUsersReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportWhitelistUsersReq.ProtoReflect.Descriptor instead.
func (*ImportWhitelistUsersReq) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{2}
}

func (x *ImportWhitelistUsersReq) GetWhitelistID() string {
	if x != nil {
		return x.WhitelistID
	}
	return ""
}

func (x *ImportWhitelistUsersReq) GetFileContent() []byte {
	if x != nil {
		return x.FileContent
	}
	return nil
}

func (x *ImportWhitelistUsersReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ImportWhitelistUsersReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type ImportWhitelistUsersResp struct {
	state            protoimpl.MessageState                       `protogen:"open.v1"`
	TotalImported    int32                                        `protobuf:"varint,1,opt,name=total_imported,json=totalImported,proto3" json:"total_imported,omitempty"`         // 成功导入的总数
	TotalUpdated     int32                                        `protobuf:"varint,2,opt,name=total_updated,json=totalUpdated,proto3" json:"total_updated,omitempty"`            // 更新现有记录的数量
	ExistingUsers    []*ImportWhitelistUsersResp_ExistingUserInfo `protobuf:"bytes,3,rep,name=existing_users,json=existingUsers,proto3" json:"existing_users,omitempty"`          // 已存在的用户信息
	FailedRecords    []*ImportWhitelistUsersResp_FailedRecord     `protobuf:"bytes,4,rep,name=failed_records,json=failedRecords,proto3" json:"failed_records,omitempty"`          // 导入失败的记录
	DuplicateRecords []*ImportWhitelistUsersResp_DuplicateRecord  `protobuf:"bytes,5,rep,name=duplicate_records,json=duplicateRecords,proto3" json:"duplicate_records,omitempty"` // 导入文件中重复的记录
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ImportWhitelistUsersResp) Reset() {
	*x = ImportWhitelistUsersResp{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportWhitelistUsersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportWhitelistUsersResp) ProtoMessage() {}

func (x *ImportWhitelistUsersResp) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportWhitelistUsersResp.ProtoReflect.Descriptor instead.
func (*ImportWhitelistUsersResp) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{3}
}

func (x *ImportWhitelistUsersResp) GetTotalImported() int32 {
	if x != nil {
		return x.TotalImported
	}
	return 0
}

func (x *ImportWhitelistUsersResp) GetTotalUpdated() int32 {
	if x != nil {
		return x.TotalUpdated
	}
	return 0
}

func (x *ImportWhitelistUsersResp) GetExistingUsers() []*ImportWhitelistUsersResp_ExistingUserInfo {
	if x != nil {
		return x.ExistingUsers
	}
	return nil
}

func (x *ImportWhitelistUsersResp) GetFailedRecords() []*ImportWhitelistUsersResp_FailedRecord {
	if x != nil {
		return x.FailedRecords
	}
	return nil
}

func (x *ImportWhitelistUsersResp) GetDuplicateRecords() []*ImportWhitelistUsersResp_DuplicateRecord {
	if x != nil {
		return x.DuplicateRecords
	}
	return nil
}

// 获取白名单请求
type GetWhitelistReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 白名单 ID
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWhitelistReq) Reset() {
	*x = GetWhitelistReq{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWhitelistReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWhitelistReq) ProtoMessage() {}

func (x *GetWhitelistReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWhitelistReq.ProtoReflect.Descriptor instead.
func (*GetWhitelistReq) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{4}
}

func (x *GetWhitelistReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// 白名单对象
type Whitelist struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                 // 白名单 ID
	TypeId        string                 `protobuf:"bytes,2,opt,name=type_id,json=typeId,proto3" json:"type_id,omitempty"`           // 活动/项目 ID
	Type          int32                  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`                            // 类型（1.一级优先购, 2.一级全量, 3.合成优先, 4.合成全量）
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                             // 标题
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`               // 白名单描述
	StartTime     int64                  `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` // 生效的开始时间
	EndTime       int64                  `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`       // 生效的结束时间
	CreatedAt     int64                  `protobuf:"varint,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // 创建时间
	Num           int32                  `protobuf:"varint,9,opt,name=num,proto3" json:"num,omitempty"`                              // 数量限制(0或NULL表示无限制，仅对全量用户白名单有效)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Whitelist) Reset() {
	*x = Whitelist{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Whitelist) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Whitelist) ProtoMessage() {}

func (x *Whitelist) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Whitelist.ProtoReflect.Descriptor instead.
func (*Whitelist) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{5}
}

func (x *Whitelist) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Whitelist) GetTypeId() string {
	if x != nil {
		return x.TypeId
	}
	return ""
}

func (x *Whitelist) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Whitelist) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Whitelist) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Whitelist) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *Whitelist) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Whitelist) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Whitelist) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

type GetWhitelistResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Whitelist     *Whitelist             `protobuf:"bytes,1,opt,name=whitelist,proto3" json:"whitelist,omitempty"` // 白名单信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWhitelistResp) Reset() {
	*x = GetWhitelistResp{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWhitelistResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWhitelistResp) ProtoMessage() {}

func (x *GetWhitelistResp) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWhitelistResp.ProtoReflect.Descriptor instead.
func (*GetWhitelistResp) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{6}
}

func (x *GetWhitelistResp) GetWhitelist() *Whitelist {
	if x != nil {
		return x.Whitelist
	}
	return nil
}

// 获取白名单列表请求（分页）
type ListWhitelistsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`         // 当前页码
	PageSize      int32                  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"` // 每页数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListWhitelistsReq) Reset() {
	*x = ListWhitelistsReq{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListWhitelistsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWhitelistsReq) ProtoMessage() {}

func (x *ListWhitelistsReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWhitelistsReq.ProtoReflect.Descriptor instead.
func (*ListWhitelistsReq) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{7}
}

func (x *ListWhitelistsReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListWhitelistsReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListWhitelistsResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Whitelists    []*Whitelist           `protobuf:"bytes,1,rep,name=whitelists,proto3" json:"whitelists,omitempty"` // 白名单列表
	Total         int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`          // 总记录数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListWhitelistsResp) Reset() {
	*x = ListWhitelistsResp{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListWhitelistsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWhitelistsResp) ProtoMessage() {}

func (x *ListWhitelistsResp) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWhitelistsResp.ProtoReflect.Descriptor instead.
func (*ListWhitelistsResp) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{8}
}

func (x *ListWhitelistsResp) GetWhitelists() []*Whitelist {
	if x != nil {
		return x.Whitelists
	}
	return nil
}

func (x *ListWhitelistsResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type CheckWhitelistQualificationRPCReq struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	RuleTypeID string                 `protobuf:"bytes,1,opt,name=ruleTypeID,proto3" json:"ruleTypeID,omitempty"`
	UserID     string                 `protobuf:"bytes,2,opt,name=userID,proto3" json:"userID,omitempty"`
	// 购买数量
	Count         int64 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckWhitelistQualificationRPCReq) Reset() {
	*x = CheckWhitelistQualificationRPCReq{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckWhitelistQualificationRPCReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckWhitelistQualificationRPCReq) ProtoMessage() {}

func (x *CheckWhitelistQualificationRPCReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckWhitelistQualificationRPCReq.ProtoReflect.Descriptor instead.
func (*CheckWhitelistQualificationRPCReq) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{9}
}

func (x *CheckWhitelistQualificationRPCReq) GetRuleTypeID() string {
	if x != nil {
		return x.RuleTypeID
	}
	return ""
}

func (x *CheckWhitelistQualificationRPCReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *CheckWhitelistQualificationRPCReq) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type CheckWhitelistQualificationRPCResp struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	HasQualification bool                   `protobuf:"varint,1,opt,name=hasQualification,proto3" json:"hasQualification,omitempty"` // 是否具有白名单资格
	UsageCount       int64                  `protobuf:"varint,2,opt,name=usageCount,proto3" json:"usageCount,omitempty"`             // 已使用次数
	TotalCount       int64                  `protobuf:"varint,3,opt,name=totalCount,proto3" json:"totalCount,omitempty"`             // 总次数 0 表示无限制
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CheckWhitelistQualificationRPCResp) Reset() {
	*x = CheckWhitelistQualificationRPCResp{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckWhitelistQualificationRPCResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckWhitelistQualificationRPCResp) ProtoMessage() {}

func (x *CheckWhitelistQualificationRPCResp) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckWhitelistQualificationRPCResp.ProtoReflect.Descriptor instead.
func (*CheckWhitelistQualificationRPCResp) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{10}
}

func (x *CheckWhitelistQualificationRPCResp) GetHasQualification() bool {
	if x != nil {
		return x.HasQualification
	}
	return false
}

func (x *CheckWhitelistQualificationRPCResp) GetUsageCount() int64 {
	if x != nil {
		return x.UsageCount
	}
	return 0
}

func (x *CheckWhitelistQualificationRPCResp) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type CheckWhitelistQualificationForFuctionRPCReq struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	RuleTypeID string                 `protobuf:"bytes,1,opt,name=ruleTypeID,proto3" json:"ruleTypeID,omitempty"`
	UserID     string                 `protobuf:"bytes,2,opt,name=userID,proto3" json:"userID,omitempty"`
	// 合成数量
	Count         int64 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckWhitelistQualificationForFuctionRPCReq) Reset() {
	*x = CheckWhitelistQualificationForFuctionRPCReq{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckWhitelistQualificationForFuctionRPCReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckWhitelistQualificationForFuctionRPCReq) ProtoMessage() {}

func (x *CheckWhitelistQualificationForFuctionRPCReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckWhitelistQualificationForFuctionRPCReq.ProtoReflect.Descriptor instead.
func (*CheckWhitelistQualificationForFuctionRPCReq) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{11}
}

func (x *CheckWhitelistQualificationForFuctionRPCReq) GetRuleTypeID() string {
	if x != nil {
		return x.RuleTypeID
	}
	return ""
}

func (x *CheckWhitelistQualificationForFuctionRPCReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *CheckWhitelistQualificationForFuctionRPCReq) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type CheckWhitelistQualificationForFuctionRPCResp struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	HasQualification bool                   `protobuf:"varint,1,opt,name=hasQualification,proto3" json:"hasQualification,omitempty"` // 是否具有白名单资格
	UsageCount       int64                  `protobuf:"varint,2,opt,name=usageCount,proto3" json:"usageCount,omitempty"`             // 已使用次数
	TotalCount       int64                  `protobuf:"varint,3,opt,name=totalCount,proto3" json:"totalCount,omitempty"`             // 总次数 0 表示无限制
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CheckWhitelistQualificationForFuctionRPCResp) Reset() {
	*x = CheckWhitelistQualificationForFuctionRPCResp{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckWhitelistQualificationForFuctionRPCResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckWhitelistQualificationForFuctionRPCResp) ProtoMessage() {}

func (x *CheckWhitelistQualificationForFuctionRPCResp) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckWhitelistQualificationForFuctionRPCResp.ProtoReflect.Descriptor instead.
func (*CheckWhitelistQualificationForFuctionRPCResp) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{12}
}

func (x *CheckWhitelistQualificationForFuctionRPCResp) GetHasQualification() bool {
	if x != nil {
		return x.HasQualification
	}
	return false
}

func (x *CheckWhitelistQualificationForFuctionRPCResp) GetUsageCount() int64 {
	if x != nil {
		return x.UsageCount
	}
	return 0
}

func (x *CheckWhitelistQualificationForFuctionRPCResp) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type CreateWhitelistRPCReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 关联业务 ID（如发售/活动等）
	TypeID string `protobuf:"bytes,1,opt,name=typeID,proto3" json:"typeID,omitempty"`
	// 类型：1.一级优先购 2.一级全量 3.合成优先 4.合成全量
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	// 白名单名称
	Name        string  `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Description *string `protobuf:"bytes,4,opt,name=description,proto3,oneof" json:"description,omitempty"` // 描述
	// 生效开始时间（时间戳）
	StartTime int64 `protobuf:"varint,5,opt,name=startTime,proto3" json:"startTime,omitempty"`
	// 生效结束时间（时间戳,可选）
	EndTime *int64 `protobuf:"varint,6,opt,name=endTime,proto3,oneof" json:"endTime,omitempty"`
	// 数量限制, 0 表示无限制,仅针对全量用户白名单
	Num           *int32 `protobuf:"varint,7,opt,name=num,proto3,oneof" json:"num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateWhitelistRPCReq) Reset() {
	*x = CreateWhitelistRPCReq{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWhitelistRPCReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWhitelistRPCReq) ProtoMessage() {}

func (x *CreateWhitelistRPCReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWhitelistRPCReq.ProtoReflect.Descriptor instead.
func (*CreateWhitelistRPCReq) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{13}
}

func (x *CreateWhitelistRPCReq) GetTypeID() string {
	if x != nil {
		return x.TypeID
	}
	return ""
}

func (x *CreateWhitelistRPCReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CreateWhitelistRPCReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateWhitelistRPCReq) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *CreateWhitelistRPCReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *CreateWhitelistRPCReq) GetEndTime() int64 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *CreateWhitelistRPCReq) GetNum() int32 {
	if x != nil && x.Num != nil {
		return *x.Num
	}
	return 0
}

type BatchConfigureUserWhitelistRPCReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 白名单 ID
	WhitelistID    string                                             `protobuf:"bytes,1,opt,name=whitelistID,proto3" json:"whitelistID,omitempty"`
	UserWhitelists []*BatchConfigureUserWhitelistRPCReq_UserWhitelist `protobuf:"bytes,2,rep,name=userWhitelists,proto3" json:"userWhitelists,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BatchConfigureUserWhitelistRPCReq) Reset() {
	*x = BatchConfigureUserWhitelistRPCReq{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchConfigureUserWhitelistRPCReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchConfigureUserWhitelistRPCReq) ProtoMessage() {}

func (x *BatchConfigureUserWhitelistRPCReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchConfigureUserWhitelistRPCReq.ProtoReflect.Descriptor instead.
func (*BatchConfigureUserWhitelistRPCReq) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{14}
}

func (x *BatchConfigureUserWhitelistRPCReq) GetWhitelistID() string {
	if x != nil {
		return x.WhitelistID
	}
	return ""
}

func (x *BatchConfigureUserWhitelistRPCReq) GetUserWhitelists() []*BatchConfigureUserWhitelistRPCReq_UserWhitelist {
	if x != nil {
		return x.UserWhitelists
	}
	return nil
}

type BatchConfigureUserWhitelistRPCResp struct {
	state         protoimpl.MessageState                                 `protogen:"open.v1"`
	TotalCreated  int32                                                  `protobuf:"varint,1,opt,name=total_created,json=totalCreated,proto3" json:"total_created,omitempty"`   // 成功创建的总数
	TotalUpdated  int32                                                  `protobuf:"varint,2,opt,name=total_updated,json=totalUpdated,proto3" json:"total_updated,omitempty"`   // 更新现有记录的数量
	ExistingUsers []*BatchConfigureUserWhitelistRPCResp_ExistingUserInfo `protobuf:"bytes,3,rep,name=existing_users,json=existingUsers,proto3" json:"existing_users,omitempty"` // 已存在的用户信息
	InvalidUsers  []*BatchConfigureUserWhitelistRPCResp_InvalidUserInfo  `protobuf:"bytes,4,rep,name=invalid_users,json=invalidUsers,proto3" json:"invalid_users,omitempty"`    // 无效的用户信息（用户不存在）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchConfigureUserWhitelistRPCResp) Reset() {
	*x = BatchConfigureUserWhitelistRPCResp{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchConfigureUserWhitelistRPCResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchConfigureUserWhitelistRPCResp) ProtoMessage() {}

func (x *BatchConfigureUserWhitelistRPCResp) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchConfigureUserWhitelistRPCResp.ProtoReflect.Descriptor instead.
func (*BatchConfigureUserWhitelistRPCResp) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{15}
}

func (x *BatchConfigureUserWhitelistRPCResp) GetTotalCreated() int32 {
	if x != nil {
		return x.TotalCreated
	}
	return 0
}

func (x *BatchConfigureUserWhitelistRPCResp) GetTotalUpdated() int32 {
	if x != nil {
		return x.TotalUpdated
	}
	return 0
}

func (x *BatchConfigureUserWhitelistRPCResp) GetExistingUsers() []*BatchConfigureUserWhitelistRPCResp_ExistingUserInfo {
	if x != nil {
		return x.ExistingUsers
	}
	return nil
}

func (x *BatchConfigureUserWhitelistRPCResp) GetInvalidUsers() []*BatchConfigureUserWhitelistRPCResp_InvalidUserInfo {
	if x != nil {
		return x.InvalidUsers
	}
	return nil
}

type BatchConfigureUserWhitelisttByPhoneNumberRPCReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 白名单 ID
	WhitelistID    string                                                           `protobuf:"bytes,1,opt,name=whitelistID,proto3" json:"whitelistID,omitempty"`
	UserWhitelists []*BatchConfigureUserWhitelisttByPhoneNumberRPCReq_UserWhitelist `protobuf:"bytes,2,rep,name=userWhitelists,proto3" json:"userWhitelists,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCReq) Reset() {
	*x = BatchConfigureUserWhitelisttByPhoneNumberRPCReq{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchConfigureUserWhitelisttByPhoneNumberRPCReq) ProtoMessage() {}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchConfigureUserWhitelisttByPhoneNumberRPCReq.ProtoReflect.Descriptor instead.
func (*BatchConfigureUserWhitelisttByPhoneNumberRPCReq) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{16}
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCReq) GetWhitelistID() string {
	if x != nil {
		return x.WhitelistID
	}
	return ""
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCReq) GetUserWhitelists() []*BatchConfigureUserWhitelisttByPhoneNumberRPCReq_UserWhitelist {
	if x != nil {
		return x.UserWhitelists
	}
	return nil
}

type BatchConfigureUserWhitelisttByPhoneNumberRPCResp struct {
	state         protoimpl.MessageState                                               `protogen:"open.v1"`
	TotalCreated  int32                                                                `protobuf:"varint,1,opt,name=total_created,json=totalCreated,proto3" json:"total_created,omitempty"`   // 成功创建的总数
	TotalUpdated  int32                                                                `protobuf:"varint,2,opt,name=total_updated,json=totalUpdated,proto3" json:"total_updated,omitempty"`   // 更新现有记录的数量
	ExistingUsers []*BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo `protobuf:"bytes,3,rep,name=existing_users,json=existingUsers,proto3" json:"existing_users,omitempty"` // 已存在的用户信息
	InvalidPhones []*BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo `protobuf:"bytes,4,rep,name=invalid_phones,json=invalidPhones,proto3" json:"invalid_phones,omitempty"` // 无效的手机号信息（用户不存在）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp) Reset() {
	*x = BatchConfigureUserWhitelisttByPhoneNumberRPCResp{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchConfigureUserWhitelisttByPhoneNumberRPCResp) ProtoMessage() {}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchConfigureUserWhitelisttByPhoneNumberRPCResp.ProtoReflect.Descriptor instead.
func (*BatchConfigureUserWhitelisttByPhoneNumberRPCResp) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{17}
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp) GetTotalCreated() int32 {
	if x != nil {
		return x.TotalCreated
	}
	return 0
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp) GetTotalUpdated() int32 {
	if x != nil {
		return x.TotalUpdated
	}
	return 0
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp) GetExistingUsers() []*BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo {
	if x != nil {
		return x.ExistingUsers
	}
	return nil
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp) GetInvalidPhones() []*BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo {
	if x != nil {
		return x.InvalidPhones
	}
	return nil
}

type GetWhitelistsRPCReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前页码
	Page int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页数量
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 搜索条件
	Keyword       *string `protobuf:"bytes,3,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"` // 关键词搜索：白名单名称、描述、关联业务ID
	Type          *int32  `protobuf:"varint,4,opt,name=type,proto3,oneof" json:"type,omitempty"`      // 类型筛选：1.一级优先购 2.一级全量 3.合成优先 4.合成全量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWhitelistsRPCReq) Reset() {
	*x = GetWhitelistsRPCReq{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWhitelistsRPCReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWhitelistsRPCReq) ProtoMessage() {}

func (x *GetWhitelistsRPCReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWhitelistsRPCReq.ProtoReflect.Descriptor instead.
func (*GetWhitelistsRPCReq) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{18}
}

func (x *GetWhitelistsRPCReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetWhitelistsRPCReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetWhitelistsRPCReq) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *GetWhitelistsRPCReq) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

type GetWhitelistsRPCResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Whitelists    []*Whitelist           `protobuf:"bytes,1,rep,name=whitelists,proto3" json:"whitelists,omitempty"` // 白名单列表
	Total         int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`          // 总记录数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWhitelistsRPCResp) Reset() {
	*x = GetWhitelistsRPCResp{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWhitelistsRPCResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWhitelistsRPCResp) ProtoMessage() {}

func (x *GetWhitelistsRPCResp) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWhitelistsRPCResp.ProtoReflect.Descriptor instead.
func (*GetWhitelistsRPCResp) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{19}
}

func (x *GetWhitelistsRPCResp) GetWhitelists() []*Whitelist {
	if x != nil {
		return x.Whitelists
	}
	return nil
}

func (x *GetWhitelistsRPCResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type GetWhitelistUserRuleListRPCReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 白名单 ID
	WhitelistId string `protobuf:"bytes,1,opt,name=whitelist_id,json=whitelistId,proto3" json:"whitelist_id,omitempty"`
	// 当前页码
	Page int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	// 每页数量
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 搜索条件
	Keyword       *string `protobuf:"bytes,4,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"` // 关键词搜索：用户ID、手机号
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWhitelistUserRuleListRPCReq) Reset() {
	*x = GetWhitelistUserRuleListRPCReq{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWhitelistUserRuleListRPCReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWhitelistUserRuleListRPCReq) ProtoMessage() {}

func (x *GetWhitelistUserRuleListRPCReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWhitelistUserRuleListRPCReq.ProtoReflect.Descriptor instead.
func (*GetWhitelistUserRuleListRPCReq) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{20}
}

func (x *GetWhitelistUserRuleListRPCReq) GetWhitelistId() string {
	if x != nil {
		return x.WhitelistId
	}
	return ""
}

func (x *GetWhitelistUserRuleListRPCReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetWhitelistUserRuleListRPCReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetWhitelistUserRuleListRPCReq) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

type GetWhitelistUserRuleListRPCResp struct {
	state         protoimpl.MessageState                               `protogen:"open.v1"`
	UserRules     []*GetWhitelistUserRuleListRPCResp_WhitelistUserRule `protobuf:"bytes,1,rep,name=user_rules,json=userRules,proto3" json:"user_rules,omitempty"`
	Total         int64                                                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"` // 总记录数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWhitelistUserRuleListRPCResp) Reset() {
	*x = GetWhitelistUserRuleListRPCResp{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWhitelistUserRuleListRPCResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWhitelistUserRuleListRPCResp) ProtoMessage() {}

func (x *GetWhitelistUserRuleListRPCResp) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWhitelistUserRuleListRPCResp.ProtoReflect.Descriptor instead.
func (*GetWhitelistUserRuleListRPCResp) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{21}
}

func (x *GetWhitelistUserRuleListRPCResp) GetUserRules() []*GetWhitelistUserRuleListRPCResp_WhitelistUserRule {
	if x != nil {
		return x.UserRules
	}
	return nil
}

func (x *GetWhitelistUserRuleListRPCResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ImportWhitelistUsersResp_ExistingUserInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PhoneNumber   string                 `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OldNum        int32                  `protobuf:"varint,3,opt,name=old_num,json=oldNum,proto3" json:"old_num,omitempty"`
	NewNum        int32                  `protobuf:"varint,4,opt,name=new_num,json=newNum,proto3" json:"new_num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImportWhitelistUsersResp_ExistingUserInfo) Reset() {
	*x = ImportWhitelistUsersResp_ExistingUserInfo{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportWhitelistUsersResp_ExistingUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportWhitelistUsersResp_ExistingUserInfo) ProtoMessage() {}

func (x *ImportWhitelistUsersResp_ExistingUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportWhitelistUsersResp_ExistingUserInfo.ProtoReflect.Descriptor instead.
func (*ImportWhitelistUsersResp_ExistingUserInfo) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ImportWhitelistUsersResp_ExistingUserInfo) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *ImportWhitelistUsersResp_ExistingUserInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ImportWhitelistUsersResp_ExistingUserInfo) GetOldNum() int32 {
	if x != nil {
		return x.OldNum
	}
	return 0
}

func (x *ImportWhitelistUsersResp_ExistingUserInfo) GetNewNum() int32 {
	if x != nil {
		return x.NewNum
	}
	return 0
}

type ImportWhitelistUsersResp_FailedRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RowNumber     int32                  `protobuf:"varint,1,opt,name=row_number,json=rowNumber,proto3" json:"row_number,omitempty"`      // Excel 行号（从2开始，因为第1行是表头）
	PhoneNumber   string                 `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"` // 手机号
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`                              // 失败原因
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImportWhitelistUsersResp_FailedRecord) Reset() {
	*x = ImportWhitelistUsersResp_FailedRecord{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportWhitelistUsersResp_FailedRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportWhitelistUsersResp_FailedRecord) ProtoMessage() {}

func (x *ImportWhitelistUsersResp_FailedRecord) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportWhitelistUsersResp_FailedRecord.ProtoReflect.Descriptor instead.
func (*ImportWhitelistUsersResp_FailedRecord) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{3, 1}
}

func (x *ImportWhitelistUsersResp_FailedRecord) GetRowNumber() int32 {
	if x != nil {
		return x.RowNumber
	}
	return 0
}

func (x *ImportWhitelistUsersResp_FailedRecord) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *ImportWhitelistUsersResp_FailedRecord) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type ImportWhitelistUsersResp_DuplicateRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PhoneNumber   string                 `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OldNum        int32                  `protobuf:"varint,3,opt,name=old_num,json=oldNum,proto3" json:"old_num,omitempty"`
	NewNum        int32                  `protobuf:"varint,4,opt,name=new_num,json=newNum,proto3" json:"new_num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImportWhitelistUsersResp_DuplicateRecord) Reset() {
	*x = ImportWhitelistUsersResp_DuplicateRecord{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportWhitelistUsersResp_DuplicateRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportWhitelistUsersResp_DuplicateRecord) ProtoMessage() {}

func (x *ImportWhitelistUsersResp_DuplicateRecord) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportWhitelistUsersResp_DuplicateRecord.ProtoReflect.Descriptor instead.
func (*ImportWhitelistUsersResp_DuplicateRecord) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{3, 2}
}

func (x *ImportWhitelistUsersResp_DuplicateRecord) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *ImportWhitelistUsersResp_DuplicateRecord) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ImportWhitelistUsersResp_DuplicateRecord) GetOldNum() int32 {
	if x != nil {
		return x.OldNum
	}
	return 0
}

func (x *ImportWhitelistUsersResp_DuplicateRecord) GetNewNum() int32 {
	if x != nil {
		return x.NewNum
	}
	return 0
}

type BatchConfigureUserWhitelistRPCReq_UserWhitelist struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户 ID
	UserID string `protobuf:"bytes,1,opt,name=userID,proto3" json:"userID,omitempty"`
	// 数量限制, 0 表示无限制
	Num           int32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchConfigureUserWhitelistRPCReq_UserWhitelist) Reset() {
	*x = BatchConfigureUserWhitelistRPCReq_UserWhitelist{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchConfigureUserWhitelistRPCReq_UserWhitelist) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchConfigureUserWhitelistRPCReq_UserWhitelist) ProtoMessage() {}

func (x *BatchConfigureUserWhitelistRPCReq_UserWhitelist) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchConfigureUserWhitelistRPCReq_UserWhitelist.ProtoReflect.Descriptor instead.
func (*BatchConfigureUserWhitelistRPCReq_UserWhitelist) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{14, 0}
}

func (x *BatchConfigureUserWhitelistRPCReq_UserWhitelist) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *BatchConfigureUserWhitelistRPCReq_UserWhitelist) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

type BatchConfigureUserWhitelistRPCResp_ExistingUserInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PhoneNumber   string                 `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	OldNum        int32                  `protobuf:"varint,3,opt,name=old_num,json=oldNum,proto3" json:"old_num,omitempty"`
	NewNum        int32                  `protobuf:"varint,4,opt,name=new_num,json=newNum,proto3" json:"new_num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchConfigureUserWhitelistRPCResp_ExistingUserInfo) Reset() {
	*x = BatchConfigureUserWhitelistRPCResp_ExistingUserInfo{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchConfigureUserWhitelistRPCResp_ExistingUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchConfigureUserWhitelistRPCResp_ExistingUserInfo) ProtoMessage() {}

func (x *BatchConfigureUserWhitelistRPCResp_ExistingUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchConfigureUserWhitelistRPCResp_ExistingUserInfo.ProtoReflect.Descriptor instead.
func (*BatchConfigureUserWhitelistRPCResp_ExistingUserInfo) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{15, 0}
}

func (x *BatchConfigureUserWhitelistRPCResp_ExistingUserInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BatchConfigureUserWhitelistRPCResp_ExistingUserInfo) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *BatchConfigureUserWhitelistRPCResp_ExistingUserInfo) GetOldNum() int32 {
	if x != nil {
		return x.OldNum
	}
	return 0
}

func (x *BatchConfigureUserWhitelistRPCResp_ExistingUserInfo) GetNewNum() int32 {
	if x != nil {
		return x.NewNum
	}
	return 0
}

type BatchConfigureUserWhitelistRPCResp_InvalidUserInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Reason        string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"` // 失败原因
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchConfigureUserWhitelistRPCResp_InvalidUserInfo) Reset() {
	*x = BatchConfigureUserWhitelistRPCResp_InvalidUserInfo{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchConfigureUserWhitelistRPCResp_InvalidUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchConfigureUserWhitelistRPCResp_InvalidUserInfo) ProtoMessage() {}

func (x *BatchConfigureUserWhitelistRPCResp_InvalidUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchConfigureUserWhitelistRPCResp_InvalidUserInfo.ProtoReflect.Descriptor instead.
func (*BatchConfigureUserWhitelistRPCResp_InvalidUserInfo) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{15, 1}
}

func (x *BatchConfigureUserWhitelistRPCResp_InvalidUserInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BatchConfigureUserWhitelistRPCResp_InvalidUserInfo) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type BatchConfigureUserWhitelisttByPhoneNumberRPCReq_UserWhitelist struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 手机号
	PhoneNumber string `protobuf:"bytes,1,opt,name=phoneNumber,proto3" json:"phoneNumber,omitempty"`
	// 数量限制, 0 表示无限制
	Num           int32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCReq_UserWhitelist) Reset() {
	*x = BatchConfigureUserWhitelisttByPhoneNumberRPCReq_UserWhitelist{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCReq_UserWhitelist) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchConfigureUserWhitelisttByPhoneNumberRPCReq_UserWhitelist) ProtoMessage() {}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCReq_UserWhitelist) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchConfigureUserWhitelisttByPhoneNumberRPCReq_UserWhitelist.ProtoReflect.Descriptor instead.
func (*BatchConfigureUserWhitelisttByPhoneNumberRPCReq_UserWhitelist) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{16, 0}
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCReq_UserWhitelist) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCReq_UserWhitelist) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

type BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PhoneNumber   string                 `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OldNum        int32                  `protobuf:"varint,3,opt,name=old_num,json=oldNum,proto3" json:"old_num,omitempty"`
	NewNum        int32                  `protobuf:"varint,4,opt,name=new_num,json=newNum,proto3" json:"new_num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo) Reset() {
	*x = BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo) ProtoMessage() {}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo.ProtoReflect.Descriptor instead.
func (*BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{17, 0}
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo) GetOldNum() int32 {
	if x != nil {
		return x.OldNum
	}
	return 0
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo) GetNewNum() int32 {
	if x != nil {
		return x.NewNum
	}
	return 0
}

type BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PhoneNumber   string                 `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Reason        string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"` // 失败原因
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo) Reset() {
	*x = BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo) ProtoMessage() {}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo.ProtoReflect.Descriptor instead.
func (*BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{17, 1}
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type GetWhitelistUserRuleListRPCResp_WhitelistUserRule struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PhoneNumber   string                 `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Num           int32                  `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // 创建时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWhitelistUserRuleListRPCResp_WhitelistUserRule) Reset() {
	*x = GetWhitelistUserRuleListRPCResp_WhitelistUserRule{}
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWhitelistUserRuleListRPCResp_WhitelistUserRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWhitelistUserRuleListRPCResp_WhitelistUserRule) ProtoMessage() {}

func (x *GetWhitelistUserRuleListRPCResp_WhitelistUserRule) ProtoReflect() protoreflect.Message {
	mi := &file_ces_whitelist_whitelist_whitelist_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWhitelistUserRuleListRPCResp_WhitelistUserRule.ProtoReflect.Descriptor instead.
func (*GetWhitelistUserRuleListRPCResp_WhitelistUserRule) Descriptor() ([]byte, []int) {
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP(), []int{21, 0}
}

func (x *GetWhitelistUserRuleListRPCResp_WhitelistUserRule) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetWhitelistUserRuleListRPCResp_WhitelistUserRule) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *GetWhitelistUserRuleListRPCResp_WhitelistUserRule) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *GetWhitelistUserRuleListRPCResp_WhitelistUserRule) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

var File_ces_whitelist_whitelist_whitelist_proto protoreflect.FileDescriptor

const file_ces_whitelist_whitelist_whitelist_proto_rawDesc = "" +
	"\n" +
	"'ces/whitelist/whitelist/whitelist.proto\x12\x17ces.whitelist.whitelist\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\"\xee\x02\n" +
	"\x12CreateWhitelistReq\x12#\n" +
	"\x06typeID\x18\x01 \x01(\tB\v\xe0A\x02\xbaH\x05r\x03\xb0\x01\x01R\x06typeID\x12;\n" +
	"\x04type\x18\x02 \x01(\x05B'\xe0A\x02\xbaH!\xba\x01\x1e\x12\x11invalid rule type\x1a\tthis >= 0R\x04type\x12!\n" +
	"\x04name\x18\x03 \x01(\tB\r\xe0A\x02\xbaH\ar\x05\x10\x01\x18\xff\x01R\x04name\x12*\n" +
	"\vdescription\x18\x04 \x01(\tB\x03\xe0A\x01H\x00R\vdescription\x88\x01\x01\x12(\n" +
	"\tstartTime\x18\x05 \x01(\x03B\n" +
	"\xe0A\x02\xbaH\x04\"\x02 \x00R\tstartTime\x12\"\n" +
	"\aendTime\x18\x06 \x01(\x03B\x03\xe0A\x01H\x01R\aendTime\x88\x01\x01\x12\x1a\n" +
	"\x03num\x18\a \x01(\x05B\x03\xe0A\x01H\x02R\x03num\x88\x01\x01\x12\x19\n" +
	"\x05token\x18\b \x01(\tB\x03\xe0A\x02R\x05tokenB\x0e\n" +
	"\f_descriptionB\n" +
	"\n" +
	"\b_endTimeB\x06\n" +
	"\x04_num\"\x8b\x03\n" +
	"\x12UpdateWhitelistReq\x12\x1b\n" +
	"\x02id\x18\x01 \x01(\tB\v\xe0A\x02\xbaH\x05r\x03\xb0\x01\x01R\x02id\x12#\n" +
	"\x06typeID\x18\x02 \x01(\tB\v\xe0A\x02\xbaH\x05r\x03\xb0\x01\x01R\x06typeID\x12;\n" +
	"\x04type\x18\x03 \x01(\x05B'\xe0A\x02\xbaH!\xba\x01\x1e\x12\x11invalid rule type\x1a\tthis >= 0R\x04type\x12!\n" +
	"\x04name\x18\x04 \x01(\tB\r\xe0A\x02\xbaH\ar\x05\x10\x01\x18\xff\x01R\x04name\x12*\n" +
	"\vdescription\x18\x05 \x01(\tB\x03\xe0A\x01H\x00R\vdescription\x88\x01\x01\x12(\n" +
	"\tstartTime\x18\x06 \x01(\x03B\n" +
	"\xe0A\x02\xbaH\x04\"\x02 \x00R\tstartTime\x12\"\n" +
	"\aendTime\x18\a \x01(\x03B\x03\xe0A\x01H\x01R\aendTime\x88\x01\x01\x12\x1a\n" +
	"\x03num\x18\b \x01(\x05B\x03\xe0A\x01H\x02R\x03num\x88\x01\x01\x12\x19\n" +
	"\x05token\x18\t \x01(\tB\x03\xe0A\x02R\x05tokenB\x0e\n" +
	"\f_descriptionB\n" +
	"\n" +
	"\b_endTimeB\x06\n" +
	"\x04_num\"\xab\x01\n" +
	"\x17ImportWhitelistUsersReq\x12-\n" +
	"\vwhitelistID\x18\x01 \x01(\tB\v\xe0A\x02\xbaH\x05r\x03\xb0\x01\x01R\vwhitelistID\x12%\n" +
	"\vfileContent\x18\x02 \x01(\fB\x03\xe0A\x02R\vfileContent\x12\x1f\n" +
	"\bfileName\x18\x03 \x01(\tB\x03\xe0A\x02R\bfileName\x12\x19\n" +
	"\x05token\x18\b \x01(\tB\x03\xe0A\x02R\x05token\"\x96\x06\n" +
	"\x18ImportWhitelistUsersResp\x12%\n" +
	"\x0etotal_imported\x18\x01 \x01(\x05R\rtotalImported\x12#\n" +
	"\rtotal_updated\x18\x02 \x01(\x05R\ftotalUpdated\x12i\n" +
	"\x0eexisting_users\x18\x03 \x03(\v2B.ces.whitelist.whitelist.ImportWhitelistUsersResp.ExistingUserInfoR\rexistingUsers\x12e\n" +
	"\x0efailed_records\x18\x04 \x03(\v2>.ces.whitelist.whitelist.ImportWhitelistUsersResp.FailedRecordR\rfailedRecords\x12n\n" +
	"\x11duplicate_records\x18\x05 \x03(\v2A.ces.whitelist.whitelist.ImportWhitelistUsersResp.DuplicateRecordR\x10duplicateRecords\x1a\x80\x01\n" +
	"\x10ExistingUserInfo\x12!\n" +
	"\fphone_number\x18\x01 \x01(\tR\vphoneNumber\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x17\n" +
	"\aold_num\x18\x03 \x01(\x05R\x06oldNum\x12\x17\n" +
	"\anew_num\x18\x04 \x01(\x05R\x06newNum\x1ah\n" +
	"\fFailedRecord\x12\x1d\n" +
	"\n" +
	"row_number\x18\x01 \x01(\x05R\trowNumber\x12!\n" +
	"\fphone_number\x18\x02 \x01(\tR\vphoneNumber\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\x1a\x7f\n" +
	"\x0fDuplicateRecord\x12!\n" +
	"\fphone_number\x18\x01 \x01(\tR\vphoneNumber\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x17\n" +
	"\aold_num\x18\x03 \x01(\x05R\x06oldNum\x12\x17\n" +
	"\anew_num\x18\x04 \x01(\x05R\x06newNum\".\n" +
	"\x0fGetWhitelistReq\x12\x1b\n" +
	"\x02id\x18\x01 \x01(\tB\v\xe0A\x02\xbaH\x05r\x03\xb0\x01\x01R\x02id\"\xe9\x01\n" +
	"\tWhitelist\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\atype_id\x18\x02 \x01(\tR\x06typeId\x12\x12\n" +
	"\x04type\x18\x03 \x01(\x05R\x04type\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x05 \x01(\tR\vdescription\x12\x1d\n" +
	"\n" +
	"start_time\x18\x06 \x01(\x03R\tstartTime\x12\x19\n" +
	"\bend_time\x18\a \x01(\x03R\aendTime\x12\x1d\n" +
	"\n" +
	"created_at\x18\b \x01(\x03R\tcreatedAt\x12\x10\n" +
	"\x03num\x18\t \x01(\x05R\x03num\"T\n" +
	"\x10GetWhitelistResp\x12@\n" +
	"\twhitelist\x18\x01 \x01(\v2\".ces.whitelist.whitelist.WhitelistR\twhitelist\"M\n" +
	"\x11ListWhitelistsReq\x12\x17\n" +
	"\x04page\x18\x01 \x01(\x05B\x03\xe0A\x02R\x04page\x12\x1f\n" +
	"\bpageSize\x18\x02 \x01(\x05B\x03\xe0A\x02R\bpageSize\"n\n" +
	"\x12ListWhitelistsResp\x12B\n" +
	"\n" +
	"whitelists\x18\x01 \x03(\v2\".ces.whitelist.whitelist.WhitelistR\n" +
	"whitelists\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x03R\x05total\"\x90\x01\n" +
	"!CheckWhitelistQualificationRPCReq\x12+\n" +
	"\n" +
	"ruleTypeID\x18\x01 \x01(\tB\v\xe0A\x02\xbaH\x05r\x03\xb0\x01\x01R\n" +
	"ruleTypeID\x12#\n" +
	"\x06userID\x18\x02 \x01(\tB\v\xe0A\x02\xbaH\x05r\x03\xb0\x01\x01R\x06userID\x12\x19\n" +
	"\x05count\x18\x03 \x01(\x03B\x03\xe0A\x02R\x05count\"\x90\x01\n" +
	"\"CheckWhitelistQualificationRPCResp\x12*\n" +
	"\x10hasQualification\x18\x01 \x01(\bR\x10hasQualification\x12\x1e\n" +
	"\n" +
	"usageCount\x18\x02 \x01(\x03R\n" +
	"usageCount\x12\x1e\n" +
	"\n" +
	"totalCount\x18\x03 \x01(\x03R\n" +
	"totalCount\"\x9a\x01\n" +
	"+CheckWhitelistQualificationForFuctionRPCReq\x12+\n" +
	"\n" +
	"ruleTypeID\x18\x01 \x01(\tB\v\xe0A\x02\xbaH\x05r\x03\xb0\x01\x01R\n" +
	"ruleTypeID\x12#\n" +
	"\x06userID\x18\x02 \x01(\tB\v\xe0A\x02\xbaH\x05r\x03\xb0\x01\x01R\x06userID\x12\x19\n" +
	"\x05count\x18\x03 \x01(\x03B\x03\xe0A\x02R\x05count\"\x9a\x01\n" +
	",CheckWhitelistQualificationForFuctionRPCResp\x12*\n" +
	"\x10hasQualification\x18\x01 \x01(\bR\x10hasQualification\x12\x1e\n" +
	"\n" +
	"usageCount\x18\x02 \x01(\x03R\n" +
	"usageCount\x12\x1e\n" +
	"\n" +
	"totalCount\x18\x03 \x01(\x03R\n" +
	"totalCount\"\xd6\x02\n" +
	"\x15CreateWhitelistRPCReq\x12#\n" +
	"\x06typeID\x18\x01 \x01(\tB\v\xe0A\x02\xbaH\x05r\x03\xb0\x01\x01R\x06typeID\x12;\n" +
	"\x04type\x18\x02 \x01(\x05B'\xe0A\x02\xbaH!\xba\x01\x1e\x12\x11invalid rule type\x1a\tthis >= 0R\x04type\x12!\n" +
	"\x04name\x18\x03 \x01(\tB\r\xe0A\x02\xbaH\ar\x05\x10\x01\x18\xff\x01R\x04name\x12*\n" +
	"\vdescription\x18\x04 \x01(\tB\x03\xe0A\x01H\x00R\vdescription\x88\x01\x01\x12(\n" +
	"\tstartTime\x18\x05 \x01(\x03B\n" +
	"\xe0A\x02\xbaH\x04\"\x02 \x00R\tstartTime\x12\"\n" +
	"\aendTime\x18\x06 \x01(\x03B\x03\xe0A\x01H\x01R\aendTime\x88\x01\x01\x12\x1a\n" +
	"\x03num\x18\a \x01(\x05B\x03\xe0A\x01H\x02R\x03num\x88\x01\x01B\x0e\n" +
	"\f_descriptionB\n" +
	"\n" +
	"\b_endTimeB\x06\n" +
	"\x04_num\"\x96\x02\n" +
	"!BatchConfigureUserWhitelistRPCReq\x12-\n" +
	"\vwhitelistID\x18\x01 \x01(\tB\v\xe0A\x02\xbaH\x05r\x03\xb0\x01\x01R\vwhitelistID\x12u\n" +
	"\x0euserWhitelists\x18\x02 \x03(\v2H.ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCReq.UserWhitelistB\x03\xe0A\x02R\x0euserWhitelists\x1aK\n" +
	"\rUserWhitelist\x12#\n" +
	"\x06userID\x18\x01 \x01(\tB\v\xe0A\x02\xbaH\x05r\x03\xb0\x01\x01R\x06userID\x12\x15\n" +
	"\x03num\x18\x02 \x01(\x05B\x03\xe0A\x02R\x03num\"\x9c\x04\n" +
	"\"BatchConfigureUserWhitelistRPCResp\x12#\n" +
	"\rtotal_created\x18\x01 \x01(\x05R\ftotalCreated\x12#\n" +
	"\rtotal_updated\x18\x02 \x01(\x05R\ftotalUpdated\x12s\n" +
	"\x0eexisting_users\x18\x03 \x03(\v2L.ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCResp.ExistingUserInfoR\rexistingUsers\x12p\n" +
	"\rinvalid_users\x18\x04 \x03(\v2K.ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCResp.InvalidUserInfoR\finvalidUsers\x1a\x80\x01\n" +
	"\x10ExistingUserInfo\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12!\n" +
	"\fphone_number\x18\x02 \x01(\tR\vphoneNumber\x12\x17\n" +
	"\aold_num\x18\x03 \x01(\x05R\x06oldNum\x12\x17\n" +
	"\anew_num\x18\x04 \x01(\x05R\x06newNum\x1aB\n" +
	"\x0fInvalidUserInfo\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x16\n" +
	"\x06reason\x18\x02 \x01(\tR\x06reason\"\xbf\x02\n" +
	"/BatchConfigureUserWhitelisttByPhoneNumberRPCReq\x12-\n" +
	"\vwhitelistID\x18\x01 \x01(\tB\v\xe0A\x02\xbaH\x05r\x03\xb0\x01\x01R\vwhitelistID\x12\x83\x01\n" +
	"\x0euserWhitelists\x18\x02 \x03(\v2V.ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCReq.UserWhitelistB\x03\xe0A\x02R\x0euserWhitelists\x1aW\n" +
	"\rUserWhitelist\x12/\n" +
	"\vphoneNumber\x18\x01 \x01(\tB\r\xe0A\x02\xbaH\ar\x05\x10\x01\x18\xff\x01R\vphoneNumber\x12\x15\n" +
	"\x03num\x18\x02 \x01(\x05B\x03\xe0A\x02R\x03num\"\xd6\x04\n" +
	"0BatchConfigureUserWhitelisttByPhoneNumberRPCResp\x12#\n" +
	"\rtotal_created\x18\x01 \x01(\x05R\ftotalCreated\x12#\n" +
	"\rtotal_updated\x18\x02 \x01(\x05R\ftotalUpdated\x12\x81\x01\n" +
	"\x0eexisting_users\x18\x03 \x03(\v2Z.ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCResp.ExistingUserInfoR\rexistingUsers\x12\x81\x01\n" +
	"\x0einvalid_phones\x18\x04 \x03(\v2Z.ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCResp.InvalidPhoneInfoR\rinvalidPhones\x1a\x80\x01\n" +
	"\x10ExistingUserInfo\x12!\n" +
	"\fphone_number\x18\x01 \x01(\tR\vphoneNumber\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x17\n" +
	"\aold_num\x18\x03 \x01(\x05R\x06oldNum\x12\x17\n" +
	"\anew_num\x18\x04 \x01(\x05R\x06newNum\x1aM\n" +
	"\x10InvalidPhoneInfo\x12!\n" +
	"\fphone_number\x18\x01 \x01(\tR\vphoneNumber\x12\x16\n" +
	"\x06reason\x18\x02 \x01(\tR\x06reason\"\xa7\x01\n" +
	"\x13GetWhitelistsRPCReq\x12\x17\n" +
	"\x04page\x18\x01 \x01(\x05B\x03\xe0A\x02R\x04page\x12 \n" +
	"\tpage_size\x18\x02 \x01(\x05B\x03\xe0A\x02R\bpageSize\x12\"\n" +
	"\akeyword\x18\x03 \x01(\tB\x03\xe0A\x01H\x00R\akeyword\x88\x01\x01\x12\x1c\n" +
	"\x04type\x18\x04 \x01(\x05B\x03\xe0A\x01H\x01R\x04type\x88\x01\x01B\n" +
	"\n" +
	"\b_keywordB\a\n" +
	"\x05_type\"p\n" +
	"\x14GetWhitelistsRPCResp\x12B\n" +
	"\n" +
	"whitelists\x18\x01 \x03(\v2\".ces.whitelist.whitelist.WhitelistR\n" +
	"whitelists\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x03R\x05total\"\xbb\x01\n" +
	"\x1eGetWhitelistUserRuleListRPCReq\x12.\n" +
	"\fwhitelist_id\x18\x01 \x01(\tB\v\xe0A\x02\xbaH\x05r\x03\xb0\x01\x01R\vwhitelistId\x12\x17\n" +
	"\x04page\x18\x02 \x01(\x05B\x03\xe0A\x02R\x04page\x12 \n" +
	"\tpage_size\x18\x03 \x01(\x05B\x03\xe0A\x02R\bpageSize\x12\"\n" +
	"\akeyword\x18\x04 \x01(\tB\x03\xe0A\x01H\x00R\akeyword\x88\x01\x01B\n" +
	"\n" +
	"\b_keyword\"\xa5\x02\n" +
	"\x1fGetWhitelistUserRuleListRPCResp\x12i\n" +
	"\n" +
	"user_rules\x18\x01 \x03(\v2J.ces.whitelist.whitelist.GetWhitelistUserRuleListRPCResp.WhitelistUserRuleR\tuserRules\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x03R\x05total\x1a\x80\x01\n" +
	"\x11WhitelistUserRule\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12!\n" +
	"\fphone_number\x18\x02 \x01(\tR\vphoneNumber\x12\x10\n" +
	"\x03num\x18\x03 \x01(\x05R\x03num\x12\x1d\n" +
	"\n" +
	"created_at\x18\x04 \x01(\x03R\tcreatedAt2\xf4\r\n" +
	"\x10WhitelistService\x12\x86\x01\n" +
	"\x0fCreateWhitelist\x12+.ces.whitelist.whitelist.CreateWhitelistReq\x1a\".ces.whitelist.whitelist.Whitelist\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/v1/whitelist/configure\x12\x81\x01\n" +
	"\x0fUpdateWhitelist\x12+.ces.whitelist.whitelist.UpdateWhitelistReq\x1a\".ces.whitelist.whitelist.Whitelist\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\x1a\x12/v1/whitelist/{id}\x12\xaa\x01\n" +
	"\x14ImportWhitelistUsers\x120.ces.whitelist.whitelist.ImportWhitelistUsersReq\x1a1.ces.whitelist.whitelist.ImportWhitelistUsersResp\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/v1/whitelist/{whitelistID}/import\x12x\n" +
	"\fGetWhitelist\x12(.ces.whitelist.whitelist.GetWhitelistReq\x1a\".ces.whitelist.whitelist.Whitelist\"\x1a\x82\xd3\xe4\x93\x02\x14\x12\x12/v1/whitelist/{id}\x12\x85\x01\n" +
	"\x0eListWhitelists\x12*.ces.whitelist.whitelist.ListWhitelistsReq\x1a+.ces.whitelist.whitelist.ListWhitelistsResp\"\x1a\x82\xd3\xe4\x93\x02\x14\x12\x12/v1/whitelist/list\x12\x99\x01\n" +
	"\x1eCheckWhitelistQualificationRPC\x12:.ces.whitelist.whitelist.CheckWhitelistQualificationRPCReq\x1a;.ces.whitelist.whitelist.CheckWhitelistQualificationRPCResp\x12\xb7\x01\n" +
	"(CheckWhitelistQualificationForFuctionRPC\x12D.ces.whitelist.whitelist.CheckWhitelistQualificationForFuctionRPCReq\x1aE.ces.whitelist.whitelist.CheckWhitelistQualificationForFuctionRPCResp\x12h\n" +
	"\x12CreateWhitelistRPC\x12..ces.whitelist.whitelist.CreateWhitelistRPCReq\x1a\".ces.whitelist.whitelist.Whitelist\x12\x99\x01\n" +
	"\x1eBatchConfigureUserWhitelistRPC\x12:.ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCReq\x1a;.ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCResp\x12\xc3\x01\n" +
	",BatchConfigureUserWhitelisttByPhoneNumberRPC\x12H.ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCReq\x1aI.ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCResp\x12o\n" +
	"\x10GetWhitelistsRPC\x12,.ces.whitelist.whitelist.GetWhitelistsRPCReq\x1a-.ces.whitelist.whitelist.GetWhitelistsRPCResp\x12\x90\x01\n" +
	"\x1bGetWhitelistUserRuleListRPC\x127.ces.whitelist.whitelist.GetWhitelistUserRuleListRPCReq\x1a8.ces.whitelist.whitelist.GetWhitelistUserRuleListRPCRespB\x0eZ\f/whitelistpbb\x06proto3"

var (
	file_ces_whitelist_whitelist_whitelist_proto_rawDescOnce sync.Once
	file_ces_whitelist_whitelist_whitelist_proto_rawDescData []byte
)

func file_ces_whitelist_whitelist_whitelist_proto_rawDescGZIP() []byte {
	file_ces_whitelist_whitelist_whitelist_proto_rawDescOnce.Do(func() {
		file_ces_whitelist_whitelist_whitelist_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_ces_whitelist_whitelist_whitelist_proto_rawDesc), len(file_ces_whitelist_whitelist_whitelist_proto_rawDesc)))
	})
	return file_ces_whitelist_whitelist_whitelist_proto_rawDescData
}

var file_ces_whitelist_whitelist_whitelist_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_ces_whitelist_whitelist_whitelist_proto_goTypes = []any{
	(*CreateWhitelistReq)(nil),                                                // 0: ces.whitelist.whitelist.CreateWhitelistReq
	(*UpdateWhitelistReq)(nil),                                                // 1: ces.whitelist.whitelist.UpdateWhitelistReq
	(*ImportWhitelistUsersReq)(nil),                                           // 2: ces.whitelist.whitelist.ImportWhitelistUsersReq
	(*ImportWhitelistUsersResp)(nil),                                          // 3: ces.whitelist.whitelist.ImportWhitelistUsersResp
	(*GetWhitelistReq)(nil),                                                   // 4: ces.whitelist.whitelist.GetWhitelistReq
	(*Whitelist)(nil),                                                         // 5: ces.whitelist.whitelist.Whitelist
	(*GetWhitelistResp)(nil),                                                  // 6: ces.whitelist.whitelist.GetWhitelistResp
	(*ListWhitelistsReq)(nil),                                                 // 7: ces.whitelist.whitelist.ListWhitelistsReq
	(*ListWhitelistsResp)(nil),                                                // 8: ces.whitelist.whitelist.ListWhitelistsResp
	(*CheckWhitelistQualificationRPCReq)(nil),                                 // 9: ces.whitelist.whitelist.CheckWhitelistQualificationRPCReq
	(*CheckWhitelistQualificationRPCResp)(nil),                                // 10: ces.whitelist.whitelist.CheckWhitelistQualificationRPCResp
	(*CheckWhitelistQualificationForFuctionRPCReq)(nil),                       // 11: ces.whitelist.whitelist.CheckWhitelistQualificationForFuctionRPCReq
	(*CheckWhitelistQualificationForFuctionRPCResp)(nil),                      // 12: ces.whitelist.whitelist.CheckWhitelistQualificationForFuctionRPCResp
	(*CreateWhitelistRPCReq)(nil),                                             // 13: ces.whitelist.whitelist.CreateWhitelistRPCReq
	(*BatchConfigureUserWhitelistRPCReq)(nil),                                 // 14: ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCReq
	(*BatchConfigureUserWhitelistRPCResp)(nil),                                // 15: ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCResp
	(*BatchConfigureUserWhitelisttByPhoneNumberRPCReq)(nil),                   // 16: ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCReq
	(*BatchConfigureUserWhitelisttByPhoneNumberRPCResp)(nil),                  // 17: ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCResp
	(*GetWhitelistsRPCReq)(nil),                                               // 18: ces.whitelist.whitelist.GetWhitelistsRPCReq
	(*GetWhitelistsRPCResp)(nil),                                              // 19: ces.whitelist.whitelist.GetWhitelistsRPCResp
	(*GetWhitelistUserRuleListRPCReq)(nil),                                    // 20: ces.whitelist.whitelist.GetWhitelistUserRuleListRPCReq
	(*GetWhitelistUserRuleListRPCResp)(nil),                                   // 21: ces.whitelist.whitelist.GetWhitelistUserRuleListRPCResp
	(*ImportWhitelistUsersResp_ExistingUserInfo)(nil),                         // 22: ces.whitelist.whitelist.ImportWhitelistUsersResp.ExistingUserInfo
	(*ImportWhitelistUsersResp_FailedRecord)(nil),                             // 23: ces.whitelist.whitelist.ImportWhitelistUsersResp.FailedRecord
	(*ImportWhitelistUsersResp_DuplicateRecord)(nil),                          // 24: ces.whitelist.whitelist.ImportWhitelistUsersResp.DuplicateRecord
	(*BatchConfigureUserWhitelistRPCReq_UserWhitelist)(nil),                   // 25: ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCReq.UserWhitelist
	(*BatchConfigureUserWhitelistRPCResp_ExistingUserInfo)(nil),               // 26: ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCResp.ExistingUserInfo
	(*BatchConfigureUserWhitelistRPCResp_InvalidUserInfo)(nil),                // 27: ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCResp.InvalidUserInfo
	(*BatchConfigureUserWhitelisttByPhoneNumberRPCReq_UserWhitelist)(nil),     // 28: ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCReq.UserWhitelist
	(*BatchConfigureUserWhitelisttByPhoneNumberRPCResp_ExistingUserInfo)(nil), // 29: ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCResp.ExistingUserInfo
	(*BatchConfigureUserWhitelisttByPhoneNumberRPCResp_InvalidPhoneInfo)(nil), // 30: ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCResp.InvalidPhoneInfo
	(*GetWhitelistUserRuleListRPCResp_WhitelistUserRule)(nil),                 // 31: ces.whitelist.whitelist.GetWhitelistUserRuleListRPCResp.WhitelistUserRule
}
var file_ces_whitelist_whitelist_whitelist_proto_depIdxs = []int32{
	22, // 0: ces.whitelist.whitelist.ImportWhitelistUsersResp.existing_users:type_name -> ces.whitelist.whitelist.ImportWhitelistUsersResp.ExistingUserInfo
	23, // 1: ces.whitelist.whitelist.ImportWhitelistUsersResp.failed_records:type_name -> ces.whitelist.whitelist.ImportWhitelistUsersResp.FailedRecord
	24, // 2: ces.whitelist.whitelist.ImportWhitelistUsersResp.duplicate_records:type_name -> ces.whitelist.whitelist.ImportWhitelistUsersResp.DuplicateRecord
	5,  // 3: ces.whitelist.whitelist.GetWhitelistResp.whitelist:type_name -> ces.whitelist.whitelist.Whitelist
	5,  // 4: ces.whitelist.whitelist.ListWhitelistsResp.whitelists:type_name -> ces.whitelist.whitelist.Whitelist
	25, // 5: ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCReq.userWhitelists:type_name -> ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCReq.UserWhitelist
	26, // 6: ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCResp.existing_users:type_name -> ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCResp.ExistingUserInfo
	27, // 7: ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCResp.invalid_users:type_name -> ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCResp.InvalidUserInfo
	28, // 8: ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCReq.userWhitelists:type_name -> ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCReq.UserWhitelist
	29, // 9: ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCResp.existing_users:type_name -> ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCResp.ExistingUserInfo
	30, // 10: ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCResp.invalid_phones:type_name -> ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCResp.InvalidPhoneInfo
	5,  // 11: ces.whitelist.whitelist.GetWhitelistsRPCResp.whitelists:type_name -> ces.whitelist.whitelist.Whitelist
	31, // 12: ces.whitelist.whitelist.GetWhitelistUserRuleListRPCResp.user_rules:type_name -> ces.whitelist.whitelist.GetWhitelistUserRuleListRPCResp.WhitelistUserRule
	0,  // 13: ces.whitelist.whitelist.WhitelistService.CreateWhitelist:input_type -> ces.whitelist.whitelist.CreateWhitelistReq
	1,  // 14: ces.whitelist.whitelist.WhitelistService.UpdateWhitelist:input_type -> ces.whitelist.whitelist.UpdateWhitelistReq
	2,  // 15: ces.whitelist.whitelist.WhitelistService.ImportWhitelistUsers:input_type -> ces.whitelist.whitelist.ImportWhitelistUsersReq
	4,  // 16: ces.whitelist.whitelist.WhitelistService.GetWhitelist:input_type -> ces.whitelist.whitelist.GetWhitelistReq
	7,  // 17: ces.whitelist.whitelist.WhitelistService.ListWhitelists:input_type -> ces.whitelist.whitelist.ListWhitelistsReq
	9,  // 18: ces.whitelist.whitelist.WhitelistService.CheckWhitelistQualificationRPC:input_type -> ces.whitelist.whitelist.CheckWhitelistQualificationRPCReq
	11, // 19: ces.whitelist.whitelist.WhitelistService.CheckWhitelistQualificationForFuctionRPC:input_type -> ces.whitelist.whitelist.CheckWhitelistQualificationForFuctionRPCReq
	13, // 20: ces.whitelist.whitelist.WhitelistService.CreateWhitelistRPC:input_type -> ces.whitelist.whitelist.CreateWhitelistRPCReq
	14, // 21: ces.whitelist.whitelist.WhitelistService.BatchConfigureUserWhitelistRPC:input_type -> ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCReq
	16, // 22: ces.whitelist.whitelist.WhitelistService.BatchConfigureUserWhitelisttByPhoneNumberRPC:input_type -> ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCReq
	18, // 23: ces.whitelist.whitelist.WhitelistService.GetWhitelistsRPC:input_type -> ces.whitelist.whitelist.GetWhitelistsRPCReq
	20, // 24: ces.whitelist.whitelist.WhitelistService.GetWhitelistUserRuleListRPC:input_type -> ces.whitelist.whitelist.GetWhitelistUserRuleListRPCReq
	5,  // 25: ces.whitelist.whitelist.WhitelistService.CreateWhitelist:output_type -> ces.whitelist.whitelist.Whitelist
	5,  // 26: ces.whitelist.whitelist.WhitelistService.UpdateWhitelist:output_type -> ces.whitelist.whitelist.Whitelist
	3,  // 27: ces.whitelist.whitelist.WhitelistService.ImportWhitelistUsers:output_type -> ces.whitelist.whitelist.ImportWhitelistUsersResp
	5,  // 28: ces.whitelist.whitelist.WhitelistService.GetWhitelist:output_type -> ces.whitelist.whitelist.Whitelist
	8,  // 29: ces.whitelist.whitelist.WhitelistService.ListWhitelists:output_type -> ces.whitelist.whitelist.ListWhitelistsResp
	10, // 30: ces.whitelist.whitelist.WhitelistService.CheckWhitelistQualificationRPC:output_type -> ces.whitelist.whitelist.CheckWhitelistQualificationRPCResp
	12, // 31: ces.whitelist.whitelist.WhitelistService.CheckWhitelistQualificationForFuctionRPC:output_type -> ces.whitelist.whitelist.CheckWhitelistQualificationForFuctionRPCResp
	5,  // 32: ces.whitelist.whitelist.WhitelistService.CreateWhitelistRPC:output_type -> ces.whitelist.whitelist.Whitelist
	15, // 33: ces.whitelist.whitelist.WhitelistService.BatchConfigureUserWhitelistRPC:output_type -> ces.whitelist.whitelist.BatchConfigureUserWhitelistRPCResp
	17, // 34: ces.whitelist.whitelist.WhitelistService.BatchConfigureUserWhitelisttByPhoneNumberRPC:output_type -> ces.whitelist.whitelist.BatchConfigureUserWhitelisttByPhoneNumberRPCResp
	19, // 35: ces.whitelist.whitelist.WhitelistService.GetWhitelistsRPC:output_type -> ces.whitelist.whitelist.GetWhitelistsRPCResp
	21, // 36: ces.whitelist.whitelist.WhitelistService.GetWhitelistUserRuleListRPC:output_type -> ces.whitelist.whitelist.GetWhitelistUserRuleListRPCResp
	25, // [25:37] is the sub-list for method output_type
	13, // [13:25] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_ces_whitelist_whitelist_whitelist_proto_init() }
func file_ces_whitelist_whitelist_whitelist_proto_init() {
	if File_ces_whitelist_whitelist_whitelist_proto != nil {
		return
	}
	file_ces_whitelist_whitelist_whitelist_proto_msgTypes[0].OneofWrappers = []any{}
	file_ces_whitelist_whitelist_whitelist_proto_msgTypes[1].OneofWrappers = []any{}
	file_ces_whitelist_whitelist_whitelist_proto_msgTypes[13].OneofWrappers = []any{}
	file_ces_whitelist_whitelist_whitelist_proto_msgTypes[18].OneofWrappers = []any{}
	file_ces_whitelist_whitelist_whitelist_proto_msgTypes[20].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_ces_whitelist_whitelist_whitelist_proto_rawDesc), len(file_ces_whitelist_whitelist_whitelist_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_ces_whitelist_whitelist_whitelist_proto_goTypes,
		DependencyIndexes: file_ces_whitelist_whitelist_whitelist_proto_depIdxs,
		MessageInfos:      file_ces_whitelist_whitelist_whitelist_proto_msgTypes,
	}.Build()
	File_ces_whitelist_whitelist_whitelist_proto = out.File
	file_ces_whitelist_whitelist_whitelist_proto_goTypes = nil
	file_ces_whitelist_whitelist_whitelist_proto_depIdxs = nil
}
