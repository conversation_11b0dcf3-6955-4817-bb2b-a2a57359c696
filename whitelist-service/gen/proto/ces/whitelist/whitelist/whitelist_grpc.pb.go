// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: ces/whitelist/whitelist/whitelist.proto

package whitelistpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	WhitelistService_CreateWhitelist_FullMethodName                              = "/ces.whitelist.whitelist.WhitelistService/CreateWhitelist"
	WhitelistService_UpdateWhitelist_FullMethodName                              = "/ces.whitelist.whitelist.WhitelistService/UpdateWhitelist"
	WhitelistService_ImportWhitelistUsers_FullMethodName                         = "/ces.whitelist.whitelist.WhitelistService/ImportWhitelistUsers"
	WhitelistService_GetWhitelist_FullMethodName                                 = "/ces.whitelist.whitelist.WhitelistService/GetWhitelist"
	WhitelistService_ListWhitelists_FullMethodName                               = "/ces.whitelist.whitelist.WhitelistService/ListWhitelists"
	WhitelistService_CheckWhitelistQualificationRPC_FullMethodName               = "/ces.whitelist.whitelist.WhitelistService/CheckWhitelistQualificationRPC"
	WhitelistService_CheckWhitelistQualificationForFuctionRPC_FullMethodName     = "/ces.whitelist.whitelist.WhitelistService/CheckWhitelistQualificationForFuctionRPC"
	WhitelistService_CreateWhitelistRPC_FullMethodName                           = "/ces.whitelist.whitelist.WhitelistService/CreateWhitelistRPC"
	WhitelistService_BatchConfigureUserWhitelistRPC_FullMethodName               = "/ces.whitelist.whitelist.WhitelistService/BatchConfigureUserWhitelistRPC"
	WhitelistService_BatchConfigureUserWhitelisttByPhoneNumberRPC_FullMethodName = "/ces.whitelist.whitelist.WhitelistService/BatchConfigureUserWhitelisttByPhoneNumberRPC"
	WhitelistService_GetWhitelistsRPC_FullMethodName                             = "/ces.whitelist.whitelist.WhitelistService/GetWhitelistsRPC"
	WhitelistService_GetWhitelistUserRuleListRPC_FullMethodName                  = "/ces.whitelist.whitelist.WhitelistService/GetWhitelistUserRuleListRPC"
)

// WhitelistServiceClient is the client API for WhitelistService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WhitelistServiceClient interface {
	// 配置（创建）白名单
	CreateWhitelist(ctx context.Context, in *CreateWhitelistReq, opts ...grpc.CallOption) (*Whitelist, error)
	// 更新白名单信息(仅在发售开始前可操作)
	UpdateWhitelist(ctx context.Context, in *UpdateWhitelistReq, opts ...grpc.CallOption) (*Whitelist, error)
	// 批量导入白名单用户（通过excel文件上传）
	ImportWhitelistUsers(ctx context.Context, in *ImportWhitelistUsersReq, opts ...grpc.CallOption) (*ImportWhitelistUsersResp, error)
	// 获取白名单信息
	GetWhitelist(ctx context.Context, in *GetWhitelistReq, opts ...grpc.CallOption) (*Whitelist, error)
	// 获取白名单列表（按时间从近到远）
	ListWhitelists(ctx context.Context, in *ListWhitelistsReq, opts ...grpc.CallOption) (*ListWhitelistsResp, error)
	// 一级市场白名单资格校验 RPC
	CheckWhitelistQualificationRPC(ctx context.Context, in *CheckWhitelistQualificationRPCReq, opts ...grpc.CallOption) (*CheckWhitelistQualificationRPCResp, error)
	// 合成白名单资格校验 RPC
	CheckWhitelistQualificationForFuctionRPC(ctx context.Context, in *CheckWhitelistQualificationForFuctionRPCReq, opts ...grpc.CallOption) (*CheckWhitelistQualificationForFuctionRPCResp, error)
	// 创建白名单RPC
	CreateWhitelistRPC(ctx context.Context, in *CreateWhitelistRPCReq, opts ...grpc.CallOption) (*Whitelist, error)
	// 批量配置用户白名单RPC
	BatchConfigureUserWhitelistRPC(ctx context.Context, in *BatchConfigureUserWhitelistRPCReq, opts ...grpc.CallOption) (*BatchConfigureUserWhitelistRPCResp, error)
	// 根据手机号配置用户白名单RPC
	BatchConfigureUserWhitelisttByPhoneNumberRPC(ctx context.Context, in *BatchConfigureUserWhitelisttByPhoneNumberRPCReq, opts ...grpc.CallOption) (*BatchConfigureUserWhitelisttByPhoneNumberRPCResp, error)
	// 获取白名单列表RPC
	GetWhitelistsRPC(ctx context.Context, in *GetWhitelistsRPCReq, opts ...grpc.CallOption) (*GetWhitelistsRPCResp, error)
	// 获取白名单用户规则列表RPC
	GetWhitelistUserRuleListRPC(ctx context.Context, in *GetWhitelistUserRuleListRPCReq, opts ...grpc.CallOption) (*GetWhitelistUserRuleListRPCResp, error)
}

type whitelistServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWhitelistServiceClient(cc grpc.ClientConnInterface) WhitelistServiceClient {
	return &whitelistServiceClient{cc}
}

func (c *whitelistServiceClient) CreateWhitelist(ctx context.Context, in *CreateWhitelistReq, opts ...grpc.CallOption) (*Whitelist, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Whitelist)
	err := c.cc.Invoke(ctx, WhitelistService_CreateWhitelist_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *whitelistServiceClient) UpdateWhitelist(ctx context.Context, in *UpdateWhitelistReq, opts ...grpc.CallOption) (*Whitelist, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Whitelist)
	err := c.cc.Invoke(ctx, WhitelistService_UpdateWhitelist_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *whitelistServiceClient) ImportWhitelistUsers(ctx context.Context, in *ImportWhitelistUsersReq, opts ...grpc.CallOption) (*ImportWhitelistUsersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ImportWhitelistUsersResp)
	err := c.cc.Invoke(ctx, WhitelistService_ImportWhitelistUsers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *whitelistServiceClient) GetWhitelist(ctx context.Context, in *GetWhitelistReq, opts ...grpc.CallOption) (*Whitelist, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Whitelist)
	err := c.cc.Invoke(ctx, WhitelistService_GetWhitelist_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *whitelistServiceClient) ListWhitelists(ctx context.Context, in *ListWhitelistsReq, opts ...grpc.CallOption) (*ListWhitelistsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListWhitelistsResp)
	err := c.cc.Invoke(ctx, WhitelistService_ListWhitelists_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *whitelistServiceClient) CheckWhitelistQualificationRPC(ctx context.Context, in *CheckWhitelistQualificationRPCReq, opts ...grpc.CallOption) (*CheckWhitelistQualificationRPCResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckWhitelistQualificationRPCResp)
	err := c.cc.Invoke(ctx, WhitelistService_CheckWhitelistQualificationRPC_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *whitelistServiceClient) CheckWhitelistQualificationForFuctionRPC(ctx context.Context, in *CheckWhitelistQualificationForFuctionRPCReq, opts ...grpc.CallOption) (*CheckWhitelistQualificationForFuctionRPCResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckWhitelistQualificationForFuctionRPCResp)
	err := c.cc.Invoke(ctx, WhitelistService_CheckWhitelistQualificationForFuctionRPC_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *whitelistServiceClient) CreateWhitelistRPC(ctx context.Context, in *CreateWhitelistRPCReq, opts ...grpc.CallOption) (*Whitelist, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Whitelist)
	err := c.cc.Invoke(ctx, WhitelistService_CreateWhitelistRPC_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *whitelistServiceClient) BatchConfigureUserWhitelistRPC(ctx context.Context, in *BatchConfigureUserWhitelistRPCReq, opts ...grpc.CallOption) (*BatchConfigureUserWhitelistRPCResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchConfigureUserWhitelistRPCResp)
	err := c.cc.Invoke(ctx, WhitelistService_BatchConfigureUserWhitelistRPC_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *whitelistServiceClient) BatchConfigureUserWhitelisttByPhoneNumberRPC(ctx context.Context, in *BatchConfigureUserWhitelisttByPhoneNumberRPCReq, opts ...grpc.CallOption) (*BatchConfigureUserWhitelisttByPhoneNumberRPCResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchConfigureUserWhitelisttByPhoneNumberRPCResp)
	err := c.cc.Invoke(ctx, WhitelistService_BatchConfigureUserWhitelisttByPhoneNumberRPC_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *whitelistServiceClient) GetWhitelistsRPC(ctx context.Context, in *GetWhitelistsRPCReq, opts ...grpc.CallOption) (*GetWhitelistsRPCResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetWhitelistsRPCResp)
	err := c.cc.Invoke(ctx, WhitelistService_GetWhitelistsRPC_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *whitelistServiceClient) GetWhitelistUserRuleListRPC(ctx context.Context, in *GetWhitelistUserRuleListRPCReq, opts ...grpc.CallOption) (*GetWhitelistUserRuleListRPCResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetWhitelistUserRuleListRPCResp)
	err := c.cc.Invoke(ctx, WhitelistService_GetWhitelistUserRuleListRPC_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WhitelistServiceServer is the server API for WhitelistService service.
// All implementations should embed UnimplementedWhitelistServiceServer
// for forward compatibility.
type WhitelistServiceServer interface {
	// 配置（创建）白名单
	CreateWhitelist(context.Context, *CreateWhitelistReq) (*Whitelist, error)
	// 更新白名单信息(仅在发售开始前可操作)
	UpdateWhitelist(context.Context, *UpdateWhitelistReq) (*Whitelist, error)
	// 批量导入白名单用户（通过excel文件上传）
	ImportWhitelistUsers(context.Context, *ImportWhitelistUsersReq) (*ImportWhitelistUsersResp, error)
	// 获取白名单信息
	GetWhitelist(context.Context, *GetWhitelistReq) (*Whitelist, error)
	// 获取白名单列表（按时间从近到远）
	ListWhitelists(context.Context, *ListWhitelistsReq) (*ListWhitelistsResp, error)
	// 一级市场白名单资格校验 RPC
	CheckWhitelistQualificationRPC(context.Context, *CheckWhitelistQualificationRPCReq) (*CheckWhitelistQualificationRPCResp, error)
	// 合成白名单资格校验 RPC
	CheckWhitelistQualificationForFuctionRPC(context.Context, *CheckWhitelistQualificationForFuctionRPCReq) (*CheckWhitelistQualificationForFuctionRPCResp, error)
	// 创建白名单RPC
	CreateWhitelistRPC(context.Context, *CreateWhitelistRPCReq) (*Whitelist, error)
	// 批量配置用户白名单RPC
	BatchConfigureUserWhitelistRPC(context.Context, *BatchConfigureUserWhitelistRPCReq) (*BatchConfigureUserWhitelistRPCResp, error)
	// 根据手机号配置用户白名单RPC
	BatchConfigureUserWhitelisttByPhoneNumberRPC(context.Context, *BatchConfigureUserWhitelisttByPhoneNumberRPCReq) (*BatchConfigureUserWhitelisttByPhoneNumberRPCResp, error)
	// 获取白名单列表RPC
	GetWhitelistsRPC(context.Context, *GetWhitelistsRPCReq) (*GetWhitelistsRPCResp, error)
	// 获取白名单用户规则列表RPC
	GetWhitelistUserRuleListRPC(context.Context, *GetWhitelistUserRuleListRPCReq) (*GetWhitelistUserRuleListRPCResp, error)
}

// UnimplementedWhitelistServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedWhitelistServiceServer struct{}

func (UnimplementedWhitelistServiceServer) CreateWhitelist(context.Context, *CreateWhitelistReq) (*Whitelist, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWhitelist not implemented")
}
func (UnimplementedWhitelistServiceServer) UpdateWhitelist(context.Context, *UpdateWhitelistReq) (*Whitelist, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWhitelist not implemented")
}
func (UnimplementedWhitelistServiceServer) ImportWhitelistUsers(context.Context, *ImportWhitelistUsersReq) (*ImportWhitelistUsersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportWhitelistUsers not implemented")
}
func (UnimplementedWhitelistServiceServer) GetWhitelist(context.Context, *GetWhitelistReq) (*Whitelist, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWhitelist not implemented")
}
func (UnimplementedWhitelistServiceServer) ListWhitelists(context.Context, *ListWhitelistsReq) (*ListWhitelistsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWhitelists not implemented")
}
func (UnimplementedWhitelistServiceServer) CheckWhitelistQualificationRPC(context.Context, *CheckWhitelistQualificationRPCReq) (*CheckWhitelistQualificationRPCResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckWhitelistQualificationRPC not implemented")
}
func (UnimplementedWhitelistServiceServer) CheckWhitelistQualificationForFuctionRPC(context.Context, *CheckWhitelistQualificationForFuctionRPCReq) (*CheckWhitelistQualificationForFuctionRPCResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckWhitelistQualificationForFuctionRPC not implemented")
}
func (UnimplementedWhitelistServiceServer) CreateWhitelistRPC(context.Context, *CreateWhitelistRPCReq) (*Whitelist, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWhitelistRPC not implemented")
}
func (UnimplementedWhitelistServiceServer) BatchConfigureUserWhitelistRPC(context.Context, *BatchConfigureUserWhitelistRPCReq) (*BatchConfigureUserWhitelistRPCResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchConfigureUserWhitelistRPC not implemented")
}
func (UnimplementedWhitelistServiceServer) BatchConfigureUserWhitelisttByPhoneNumberRPC(context.Context, *BatchConfigureUserWhitelisttByPhoneNumberRPCReq) (*BatchConfigureUserWhitelisttByPhoneNumberRPCResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchConfigureUserWhitelisttByPhoneNumberRPC not implemented")
}
func (UnimplementedWhitelistServiceServer) GetWhitelistsRPC(context.Context, *GetWhitelistsRPCReq) (*GetWhitelistsRPCResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWhitelistsRPC not implemented")
}
func (UnimplementedWhitelistServiceServer) GetWhitelistUserRuleListRPC(context.Context, *GetWhitelistUserRuleListRPCReq) (*GetWhitelistUserRuleListRPCResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWhitelistUserRuleListRPC not implemented")
}
func (UnimplementedWhitelistServiceServer) testEmbeddedByValue() {}

// UnsafeWhitelistServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WhitelistServiceServer will
// result in compilation errors.
type UnsafeWhitelistServiceServer interface {
	mustEmbedUnimplementedWhitelistServiceServer()
}

func RegisterWhitelistServiceServer(s grpc.ServiceRegistrar, srv WhitelistServiceServer) {
	// If the following call pancis, it indicates UnimplementedWhitelistServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&WhitelistService_ServiceDesc, srv)
}

func _WhitelistService_CreateWhitelist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWhitelistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhitelistServiceServer).CreateWhitelist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhitelistService_CreateWhitelist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhitelistServiceServer).CreateWhitelist(ctx, req.(*CreateWhitelistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WhitelistService_UpdateWhitelist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWhitelistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhitelistServiceServer).UpdateWhitelist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhitelistService_UpdateWhitelist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhitelistServiceServer).UpdateWhitelist(ctx, req.(*UpdateWhitelistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WhitelistService_ImportWhitelistUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportWhitelistUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhitelistServiceServer).ImportWhitelistUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhitelistService_ImportWhitelistUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhitelistServiceServer).ImportWhitelistUsers(ctx, req.(*ImportWhitelistUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WhitelistService_GetWhitelist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWhitelistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhitelistServiceServer).GetWhitelist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhitelistService_GetWhitelist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhitelistServiceServer).GetWhitelist(ctx, req.(*GetWhitelistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WhitelistService_ListWhitelists_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWhitelistsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhitelistServiceServer).ListWhitelists(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhitelistService_ListWhitelists_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhitelistServiceServer).ListWhitelists(ctx, req.(*ListWhitelistsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WhitelistService_CheckWhitelistQualificationRPC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckWhitelistQualificationRPCReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhitelistServiceServer).CheckWhitelistQualificationRPC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhitelistService_CheckWhitelistQualificationRPC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhitelistServiceServer).CheckWhitelistQualificationRPC(ctx, req.(*CheckWhitelistQualificationRPCReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WhitelistService_CheckWhitelistQualificationForFuctionRPC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckWhitelistQualificationForFuctionRPCReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhitelistServiceServer).CheckWhitelistQualificationForFuctionRPC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhitelistService_CheckWhitelistQualificationForFuctionRPC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhitelistServiceServer).CheckWhitelistQualificationForFuctionRPC(ctx, req.(*CheckWhitelistQualificationForFuctionRPCReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WhitelistService_CreateWhitelistRPC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWhitelistRPCReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhitelistServiceServer).CreateWhitelistRPC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhitelistService_CreateWhitelistRPC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhitelistServiceServer).CreateWhitelistRPC(ctx, req.(*CreateWhitelistRPCReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WhitelistService_BatchConfigureUserWhitelistRPC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchConfigureUserWhitelistRPCReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhitelistServiceServer).BatchConfigureUserWhitelistRPC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhitelistService_BatchConfigureUserWhitelistRPC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhitelistServiceServer).BatchConfigureUserWhitelistRPC(ctx, req.(*BatchConfigureUserWhitelistRPCReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WhitelistService_BatchConfigureUserWhitelisttByPhoneNumberRPC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchConfigureUserWhitelisttByPhoneNumberRPCReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhitelistServiceServer).BatchConfigureUserWhitelisttByPhoneNumberRPC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhitelistService_BatchConfigureUserWhitelisttByPhoneNumberRPC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhitelistServiceServer).BatchConfigureUserWhitelisttByPhoneNumberRPC(ctx, req.(*BatchConfigureUserWhitelisttByPhoneNumberRPCReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WhitelistService_GetWhitelistsRPC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWhitelistsRPCReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhitelistServiceServer).GetWhitelistsRPC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhitelistService_GetWhitelistsRPC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhitelistServiceServer).GetWhitelistsRPC(ctx, req.(*GetWhitelistsRPCReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WhitelistService_GetWhitelistUserRuleListRPC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWhitelistUserRuleListRPCReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhitelistServiceServer).GetWhitelistUserRuleListRPC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhitelistService_GetWhitelistUserRuleListRPC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhitelistServiceServer).GetWhitelistUserRuleListRPC(ctx, req.(*GetWhitelistUserRuleListRPCReq))
	}
	return interceptor(ctx, in, info, handler)
}

// WhitelistService_ServiceDesc is the grpc.ServiceDesc for WhitelistService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WhitelistService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ces.whitelist.whitelist.WhitelistService",
	HandlerType: (*WhitelistServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateWhitelist",
			Handler:    _WhitelistService_CreateWhitelist_Handler,
		},
		{
			MethodName: "UpdateWhitelist",
			Handler:    _WhitelistService_UpdateWhitelist_Handler,
		},
		{
			MethodName: "ImportWhitelistUsers",
			Handler:    _WhitelistService_ImportWhitelistUsers_Handler,
		},
		{
			MethodName: "GetWhitelist",
			Handler:    _WhitelistService_GetWhitelist_Handler,
		},
		{
			MethodName: "ListWhitelists",
			Handler:    _WhitelistService_ListWhitelists_Handler,
		},
		{
			MethodName: "CheckWhitelistQualificationRPC",
			Handler:    _WhitelistService_CheckWhitelistQualificationRPC_Handler,
		},
		{
			MethodName: "CheckWhitelistQualificationForFuctionRPC",
			Handler:    _WhitelistService_CheckWhitelistQualificationForFuctionRPC_Handler,
		},
		{
			MethodName: "CreateWhitelistRPC",
			Handler:    _WhitelistService_CreateWhitelistRPC_Handler,
		},
		{
			MethodName: "BatchConfigureUserWhitelistRPC",
			Handler:    _WhitelistService_BatchConfigureUserWhitelistRPC_Handler,
		},
		{
			MethodName: "BatchConfigureUserWhitelisttByPhoneNumberRPC",
			Handler:    _WhitelistService_BatchConfigureUserWhitelisttByPhoneNumberRPC_Handler,
		},
		{
			MethodName: "GetWhitelistsRPC",
			Handler:    _WhitelistService_GetWhitelistsRPC_Handler,
		},
		{
			MethodName: "GetWhitelistUserRuleListRPC",
			Handler:    _WhitelistService_GetWhitelistUserRuleListRPC_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ces/whitelist/whitelist/whitelist.proto",
}
