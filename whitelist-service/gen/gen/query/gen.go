// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                    = new(Query)
	Whitelist            *whitelist
	WhitelistUsageRecord *whitelistUsageRecord
	WhitelistUserRule    *whitelistUserRule
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	Whitelist = &Q.Whitelist
	WhitelistUsageRecord = &Q.WhitelistUsageRecord
	WhitelistUserRule = &Q.WhitelistUserRule
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                   db,
		Whitelist:            newWhitelist(db, opts...),
		WhitelistUsageRecord: newWhitelistUsageRecord(db, opts...),
		WhitelistUserRule:    newWhitelistUserRule(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	Whitelist            whitelist
	WhitelistUsageRecord whitelistUsageRecord
	WhitelistUserRule    whitelistUserRule
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                   db,
		Whitelist:            q.Whitelist.clone(db),
		WhitelistUsageRecord: q.WhitelistUsageRecord.clone(db),
		WhitelistUserRule:    q.WhitelistUserRule.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                   db,
		Whitelist:            q.Whitelist.replaceDB(db),
		WhitelistUsageRecord: q.WhitelistUsageRecord.replaceDB(db),
		WhitelistUserRule:    q.WhitelistUserRule.replaceDB(db),
	}
}

type queryCtx struct {
	Whitelist            IWhitelistDo
	WhitelistUsageRecord IWhitelistUsageRecordDo
	WhitelistUserRule    IWhitelistUserRuleDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		Whitelist:            q.Whitelist.WithContext(ctx),
		WhitelistUsageRecord: q.WhitelistUsageRecord.WithContext(ctx),
		WhitelistUserRule:    q.WhitelistUserRule.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
