// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/whitelist-service/gen/gen/model"
)

func newWhitelistUsageRecord(db *gorm.DB, opts ...gen.DOOption) whitelistUsageRecord {
	_whitelistUsageRecord := whitelistUsageRecord{}

	_whitelistUsageRecord.whitelistUsageRecordDo.UseDB(db, opts...)
	_whitelistUsageRecord.whitelistUsageRecordDo.UseModel(&model.WhitelistUsageRecord{})

	tableName := _whitelistUsageRecord.whitelistUsageRecordDo.TableName()
	_whitelistUsageRecord.ALL = field.NewAsterisk(tableName)
	_whitelistUsageRecord.ID = field.NewString(tableName, "id")
	_whitelistUsageRecord.UserID = field.NewString(tableName, "user_id")
	_whitelistUsageRecord.WhitelistUserRuleID = field.NewString(tableName, "whitelist_user_rule_id")
	_whitelistUsageRecord.UsedAt = field.NewTime(tableName, "used_at")

	_whitelistUsageRecord.fillFieldMap()

	return _whitelistUsageRecord
}

type whitelistUsageRecord struct {
	whitelistUsageRecordDo whitelistUsageRecordDo

	ALL                 field.Asterisk
	ID                  field.String
	UserID              field.String // 用户 ID
	WhitelistUserRuleID field.String // 白名单用户规则 ID
	UsedAt              field.Time   // 使用时间

	fieldMap map[string]field.Expr
}

func (w whitelistUsageRecord) Table(newTableName string) *whitelistUsageRecord {
	w.whitelistUsageRecordDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w whitelistUsageRecord) As(alias string) *whitelistUsageRecord {
	w.whitelistUsageRecordDo.DO = *(w.whitelistUsageRecordDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *whitelistUsageRecord) updateTableName(table string) *whitelistUsageRecord {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewString(table, "id")
	w.UserID = field.NewString(table, "user_id")
	w.WhitelistUserRuleID = field.NewString(table, "whitelist_user_rule_id")
	w.UsedAt = field.NewTime(table, "used_at")

	w.fillFieldMap()

	return w
}

func (w *whitelistUsageRecord) WithContext(ctx context.Context) IWhitelistUsageRecordDo {
	return w.whitelistUsageRecordDo.WithContext(ctx)
}

func (w whitelistUsageRecord) TableName() string { return w.whitelistUsageRecordDo.TableName() }

func (w whitelistUsageRecord) Alias() string { return w.whitelistUsageRecordDo.Alias() }

func (w whitelistUsageRecord) Columns(cols ...field.Expr) gen.Columns {
	return w.whitelistUsageRecordDo.Columns(cols...)
}

func (w *whitelistUsageRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *whitelistUsageRecord) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 4)
	w.fieldMap["id"] = w.ID
	w.fieldMap["user_id"] = w.UserID
	w.fieldMap["whitelist_user_rule_id"] = w.WhitelistUserRuleID
	w.fieldMap["used_at"] = w.UsedAt
}

func (w whitelistUsageRecord) clone(db *gorm.DB) whitelistUsageRecord {
	w.whitelistUsageRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w whitelistUsageRecord) replaceDB(db *gorm.DB) whitelistUsageRecord {
	w.whitelistUsageRecordDo.ReplaceDB(db)
	return w
}

type whitelistUsageRecordDo struct{ gen.DO }

type IWhitelistUsageRecordDo interface {
	gen.SubQuery
	Debug() IWhitelistUsageRecordDo
	WithContext(ctx context.Context) IWhitelistUsageRecordDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWhitelistUsageRecordDo
	WriteDB() IWhitelistUsageRecordDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWhitelistUsageRecordDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWhitelistUsageRecordDo
	Not(conds ...gen.Condition) IWhitelistUsageRecordDo
	Or(conds ...gen.Condition) IWhitelistUsageRecordDo
	Select(conds ...field.Expr) IWhitelistUsageRecordDo
	Where(conds ...gen.Condition) IWhitelistUsageRecordDo
	Order(conds ...field.Expr) IWhitelistUsageRecordDo
	Distinct(cols ...field.Expr) IWhitelistUsageRecordDo
	Omit(cols ...field.Expr) IWhitelistUsageRecordDo
	Join(table schema.Tabler, on ...field.Expr) IWhitelistUsageRecordDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWhitelistUsageRecordDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWhitelistUsageRecordDo
	Group(cols ...field.Expr) IWhitelistUsageRecordDo
	Having(conds ...gen.Condition) IWhitelistUsageRecordDo
	Limit(limit int) IWhitelistUsageRecordDo
	Offset(offset int) IWhitelistUsageRecordDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWhitelistUsageRecordDo
	Unscoped() IWhitelistUsageRecordDo
	Create(values ...*model.WhitelistUsageRecord) error
	CreateInBatches(values []*model.WhitelistUsageRecord, batchSize int) error
	Save(values ...*model.WhitelistUsageRecord) error
	First() (*model.WhitelistUsageRecord, error)
	Take() (*model.WhitelistUsageRecord, error)
	Last() (*model.WhitelistUsageRecord, error)
	Find() ([]*model.WhitelistUsageRecord, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WhitelistUsageRecord, err error)
	FindInBatches(result *[]*model.WhitelistUsageRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WhitelistUsageRecord) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWhitelistUsageRecordDo
	Assign(attrs ...field.AssignExpr) IWhitelistUsageRecordDo
	Joins(fields ...field.RelationField) IWhitelistUsageRecordDo
	Preload(fields ...field.RelationField) IWhitelistUsageRecordDo
	FirstOrInit() (*model.WhitelistUsageRecord, error)
	FirstOrCreate() (*model.WhitelistUsageRecord, error)
	FindByPage(offset int, limit int) (result []*model.WhitelistUsageRecord, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWhitelistUsageRecordDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w whitelistUsageRecordDo) Debug() IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Debug())
}

func (w whitelistUsageRecordDo) WithContext(ctx context.Context) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w whitelistUsageRecordDo) ReadDB() IWhitelistUsageRecordDo {
	return w.Clauses(dbresolver.Read)
}

func (w whitelistUsageRecordDo) WriteDB() IWhitelistUsageRecordDo {
	return w.Clauses(dbresolver.Write)
}

func (w whitelistUsageRecordDo) Session(config *gorm.Session) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Session(config))
}

func (w whitelistUsageRecordDo) Clauses(conds ...clause.Expression) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w whitelistUsageRecordDo) Returning(value interface{}, columns ...string) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w whitelistUsageRecordDo) Not(conds ...gen.Condition) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w whitelistUsageRecordDo) Or(conds ...gen.Condition) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w whitelistUsageRecordDo) Select(conds ...field.Expr) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w whitelistUsageRecordDo) Where(conds ...gen.Condition) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w whitelistUsageRecordDo) Order(conds ...field.Expr) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w whitelistUsageRecordDo) Distinct(cols ...field.Expr) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w whitelistUsageRecordDo) Omit(cols ...field.Expr) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w whitelistUsageRecordDo) Join(table schema.Tabler, on ...field.Expr) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w whitelistUsageRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w whitelistUsageRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w whitelistUsageRecordDo) Group(cols ...field.Expr) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w whitelistUsageRecordDo) Having(conds ...gen.Condition) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w whitelistUsageRecordDo) Limit(limit int) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w whitelistUsageRecordDo) Offset(offset int) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w whitelistUsageRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w whitelistUsageRecordDo) Unscoped() IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Unscoped())
}

func (w whitelistUsageRecordDo) Create(values ...*model.WhitelistUsageRecord) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w whitelistUsageRecordDo) CreateInBatches(values []*model.WhitelistUsageRecord, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w whitelistUsageRecordDo) Save(values ...*model.WhitelistUsageRecord) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w whitelistUsageRecordDo) First() (*model.WhitelistUsageRecord, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WhitelistUsageRecord), nil
	}
}

func (w whitelistUsageRecordDo) Take() (*model.WhitelistUsageRecord, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WhitelistUsageRecord), nil
	}
}

func (w whitelistUsageRecordDo) Last() (*model.WhitelistUsageRecord, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WhitelistUsageRecord), nil
	}
}

func (w whitelistUsageRecordDo) Find() ([]*model.WhitelistUsageRecord, error) {
	result, err := w.DO.Find()
	return result.([]*model.WhitelistUsageRecord), err
}

func (w whitelistUsageRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WhitelistUsageRecord, err error) {
	buf := make([]*model.WhitelistUsageRecord, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w whitelistUsageRecordDo) FindInBatches(result *[]*model.WhitelistUsageRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w whitelistUsageRecordDo) Attrs(attrs ...field.AssignExpr) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w whitelistUsageRecordDo) Assign(attrs ...field.AssignExpr) IWhitelistUsageRecordDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w whitelistUsageRecordDo) Joins(fields ...field.RelationField) IWhitelistUsageRecordDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w whitelistUsageRecordDo) Preload(fields ...field.RelationField) IWhitelistUsageRecordDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w whitelistUsageRecordDo) FirstOrInit() (*model.WhitelistUsageRecord, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WhitelistUsageRecord), nil
	}
}

func (w whitelistUsageRecordDo) FirstOrCreate() (*model.WhitelistUsageRecord, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WhitelistUsageRecord), nil
	}
}

func (w whitelistUsageRecordDo) FindByPage(offset int, limit int) (result []*model.WhitelistUsageRecord, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w whitelistUsageRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w whitelistUsageRecordDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w whitelistUsageRecordDo) Delete(models ...*model.WhitelistUsageRecord) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *whitelistUsageRecordDo) withDO(do gen.Dao) *whitelistUsageRecordDo {
	w.DO = *do.(*gen.DO)
	return w
}
