// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/whitelist-service/gen/gen/model"
)

func newWhitelist(db *gorm.DB, opts ...gen.DOOption) whitelist {
	_whitelist := whitelist{}

	_whitelist.whitelistDo.UseDB(db, opts...)
	_whitelist.whitelistDo.UseModel(&model.Whitelist{})

	tableName := _whitelist.whitelistDo.TableName()
	_whitelist.ALL = field.NewAsterisk(tableName)
	_whitelist.ID = field.NewString(tableName, "id")
	_whitelist.RuleType = field.NewInt16(tableName, "rule_type")
	_whitelist.RuleTypeID = field.NewString(tableName, "rule_type_id")
	_whitelist.StartTime = field.NewTime(tableName, "start_time")
	_whitelist.EndTime = field.NewTime(tableName, "end_time")
	_whitelist.Num = field.NewInt32(tableName, "num")
	_whitelist.Name = field.NewString(tableName, "name")
	_whitelist.Description = field.NewString(tableName, "description")
	_whitelist.CreatedAt = field.NewTime(tableName, "created_at")

	_whitelist.fillFieldMap()

	return _whitelist
}

type whitelist struct {
	whitelistDo whitelistDo

	ALL         field.Asterisk
	ID          field.String
	RuleType    field.Int16  // 规则类型：1.某个用户优先购,2.全量用户白名单
	RuleTypeID  field.String // 规则关联对象 ID，例如活动 ID、发售计划 ID 等
	StartTime   field.Time   // 白名单生效开始时间
	EndTime     field.Time   // 白名单生效结束时间（可选）
	Num         field.Int32  // 白名单数量限制，NULL 表示不限制,只对全量用户白名单有效
	Name        field.String // 白名单名称
	Description field.String // 白名单描述
	CreatedAt   field.Time   // 白名单创建时间

	fieldMap map[string]field.Expr
}

func (w whitelist) Table(newTableName string) *whitelist {
	w.whitelistDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w whitelist) As(alias string) *whitelist {
	w.whitelistDo.DO = *(w.whitelistDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *whitelist) updateTableName(table string) *whitelist {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewString(table, "id")
	w.RuleType = field.NewInt16(table, "rule_type")
	w.RuleTypeID = field.NewString(table, "rule_type_id")
	w.StartTime = field.NewTime(table, "start_time")
	w.EndTime = field.NewTime(table, "end_time")
	w.Num = field.NewInt32(table, "num")
	w.Name = field.NewString(table, "name")
	w.Description = field.NewString(table, "description")
	w.CreatedAt = field.NewTime(table, "created_at")

	w.fillFieldMap()

	return w
}

func (w *whitelist) WithContext(ctx context.Context) IWhitelistDo {
	return w.whitelistDo.WithContext(ctx)
}

func (w whitelist) TableName() string { return w.whitelistDo.TableName() }

func (w whitelist) Alias() string { return w.whitelistDo.Alias() }

func (w whitelist) Columns(cols ...field.Expr) gen.Columns { return w.whitelistDo.Columns(cols...) }

func (w *whitelist) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *whitelist) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 9)
	w.fieldMap["id"] = w.ID
	w.fieldMap["rule_type"] = w.RuleType
	w.fieldMap["rule_type_id"] = w.RuleTypeID
	w.fieldMap["start_time"] = w.StartTime
	w.fieldMap["end_time"] = w.EndTime
	w.fieldMap["num"] = w.Num
	w.fieldMap["name"] = w.Name
	w.fieldMap["description"] = w.Description
	w.fieldMap["created_at"] = w.CreatedAt
}

func (w whitelist) clone(db *gorm.DB) whitelist {
	w.whitelistDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w whitelist) replaceDB(db *gorm.DB) whitelist {
	w.whitelistDo.ReplaceDB(db)
	return w
}

type whitelistDo struct{ gen.DO }

type IWhitelistDo interface {
	gen.SubQuery
	Debug() IWhitelistDo
	WithContext(ctx context.Context) IWhitelistDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWhitelistDo
	WriteDB() IWhitelistDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWhitelistDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWhitelistDo
	Not(conds ...gen.Condition) IWhitelistDo
	Or(conds ...gen.Condition) IWhitelistDo
	Select(conds ...field.Expr) IWhitelistDo
	Where(conds ...gen.Condition) IWhitelistDo
	Order(conds ...field.Expr) IWhitelistDo
	Distinct(cols ...field.Expr) IWhitelistDo
	Omit(cols ...field.Expr) IWhitelistDo
	Join(table schema.Tabler, on ...field.Expr) IWhitelistDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWhitelistDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWhitelistDo
	Group(cols ...field.Expr) IWhitelistDo
	Having(conds ...gen.Condition) IWhitelistDo
	Limit(limit int) IWhitelistDo
	Offset(offset int) IWhitelistDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWhitelistDo
	Unscoped() IWhitelistDo
	Create(values ...*model.Whitelist) error
	CreateInBatches(values []*model.Whitelist, batchSize int) error
	Save(values ...*model.Whitelist) error
	First() (*model.Whitelist, error)
	Take() (*model.Whitelist, error)
	Last() (*model.Whitelist, error)
	Find() ([]*model.Whitelist, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Whitelist, err error)
	FindInBatches(result *[]*model.Whitelist, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Whitelist) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWhitelistDo
	Assign(attrs ...field.AssignExpr) IWhitelistDo
	Joins(fields ...field.RelationField) IWhitelistDo
	Preload(fields ...field.RelationField) IWhitelistDo
	FirstOrInit() (*model.Whitelist, error)
	FirstOrCreate() (*model.Whitelist, error)
	FindByPage(offset int, limit int) (result []*model.Whitelist, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWhitelistDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w whitelistDo) Debug() IWhitelistDo {
	return w.withDO(w.DO.Debug())
}

func (w whitelistDo) WithContext(ctx context.Context) IWhitelistDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w whitelistDo) ReadDB() IWhitelistDo {
	return w.Clauses(dbresolver.Read)
}

func (w whitelistDo) WriteDB() IWhitelistDo {
	return w.Clauses(dbresolver.Write)
}

func (w whitelistDo) Session(config *gorm.Session) IWhitelistDo {
	return w.withDO(w.DO.Session(config))
}

func (w whitelistDo) Clauses(conds ...clause.Expression) IWhitelistDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w whitelistDo) Returning(value interface{}, columns ...string) IWhitelistDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w whitelistDo) Not(conds ...gen.Condition) IWhitelistDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w whitelistDo) Or(conds ...gen.Condition) IWhitelistDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w whitelistDo) Select(conds ...field.Expr) IWhitelistDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w whitelistDo) Where(conds ...gen.Condition) IWhitelistDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w whitelistDo) Order(conds ...field.Expr) IWhitelistDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w whitelistDo) Distinct(cols ...field.Expr) IWhitelistDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w whitelistDo) Omit(cols ...field.Expr) IWhitelistDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w whitelistDo) Join(table schema.Tabler, on ...field.Expr) IWhitelistDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w whitelistDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWhitelistDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w whitelistDo) RightJoin(table schema.Tabler, on ...field.Expr) IWhitelistDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w whitelistDo) Group(cols ...field.Expr) IWhitelistDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w whitelistDo) Having(conds ...gen.Condition) IWhitelistDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w whitelistDo) Limit(limit int) IWhitelistDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w whitelistDo) Offset(offset int) IWhitelistDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w whitelistDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWhitelistDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w whitelistDo) Unscoped() IWhitelistDo {
	return w.withDO(w.DO.Unscoped())
}

func (w whitelistDo) Create(values ...*model.Whitelist) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w whitelistDo) CreateInBatches(values []*model.Whitelist, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w whitelistDo) Save(values ...*model.Whitelist) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w whitelistDo) First() (*model.Whitelist, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Whitelist), nil
	}
}

func (w whitelistDo) Take() (*model.Whitelist, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Whitelist), nil
	}
}

func (w whitelistDo) Last() (*model.Whitelist, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Whitelist), nil
	}
}

func (w whitelistDo) Find() ([]*model.Whitelist, error) {
	result, err := w.DO.Find()
	return result.([]*model.Whitelist), err
}

func (w whitelistDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Whitelist, err error) {
	buf := make([]*model.Whitelist, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w whitelistDo) FindInBatches(result *[]*model.Whitelist, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w whitelistDo) Attrs(attrs ...field.AssignExpr) IWhitelistDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w whitelistDo) Assign(attrs ...field.AssignExpr) IWhitelistDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w whitelistDo) Joins(fields ...field.RelationField) IWhitelistDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w whitelistDo) Preload(fields ...field.RelationField) IWhitelistDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w whitelistDo) FirstOrInit() (*model.Whitelist, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Whitelist), nil
	}
}

func (w whitelistDo) FirstOrCreate() (*model.Whitelist, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Whitelist), nil
	}
}

func (w whitelistDo) FindByPage(offset int, limit int) (result []*model.Whitelist, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w whitelistDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w whitelistDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w whitelistDo) Delete(models ...*model.Whitelist) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *whitelistDo) withDO(do gen.Dao) *whitelistDo {
	w.DO = *do.(*gen.DO)
	return w
}
