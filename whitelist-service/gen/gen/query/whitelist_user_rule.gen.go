// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/whitelist-service/gen/gen/model"
)

func newWhitelistUserRule(db *gorm.DB, opts ...gen.DOOption) whitelistUserRule {
	_whitelistUserRule := whitelistUserRule{}

	_whitelistUserRule.whitelistUserRuleDo.UseDB(db, opts...)
	_whitelistUserRule.whitelistUserRuleDo.UseModel(&model.WhitelistUserRule{})

	tableName := _whitelistUserRule.whitelistUserRuleDo.TableName()
	_whitelistUserRule.ALL = field.NewAsterisk(tableName)
	_whitelistUserRule.ID = field.NewString(tableName, "id")
	_whitelistUserRule.UserID = field.NewString(tableName, "user_id")
	_whitelistUserRule.WhitelistID = field.NewString(tableName, "whitelist_id")
	_whitelistUserRule.UsedCount = field.NewInt32(tableName, "used_count")
	_whitelistUserRule.Num = field.NewInt32(tableName, "num")
	_whitelistUserRule.PhoneNumber = field.NewString(tableName, "phone_number")
	_whitelistUserRule.CreatedAt = field.NewTime(tableName, "created_at")

	_whitelistUserRule.fillFieldMap()

	return _whitelistUserRule
}

type whitelistUserRule struct {
	whitelistUserRuleDo whitelistUserRuleDo

	ALL         field.Asterisk
	ID          field.String
	UserID      field.String // 用户 ID
	WhitelistID field.String // 所属白名单 ID
	UsedCount   field.Int32
	Num         field.Int32  // 个性化数量限制（如最大购入/空投份额），NULL 表示不限制
	PhoneNumber field.String // 手机号码
	CreatedAt   field.Time   // 记录创建时间

	fieldMap map[string]field.Expr
}

func (w whitelistUserRule) Table(newTableName string) *whitelistUserRule {
	w.whitelistUserRuleDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w whitelistUserRule) As(alias string) *whitelistUserRule {
	w.whitelistUserRuleDo.DO = *(w.whitelistUserRuleDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *whitelistUserRule) updateTableName(table string) *whitelistUserRule {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewString(table, "id")
	w.UserID = field.NewString(table, "user_id")
	w.WhitelistID = field.NewString(table, "whitelist_id")
	w.UsedCount = field.NewInt32(table, "used_count")
	w.Num = field.NewInt32(table, "num")
	w.PhoneNumber = field.NewString(table, "phone_number")
	w.CreatedAt = field.NewTime(table, "created_at")

	w.fillFieldMap()

	return w
}

func (w *whitelistUserRule) WithContext(ctx context.Context) IWhitelistUserRuleDo {
	return w.whitelistUserRuleDo.WithContext(ctx)
}

func (w whitelistUserRule) TableName() string { return w.whitelistUserRuleDo.TableName() }

func (w whitelistUserRule) Alias() string { return w.whitelistUserRuleDo.Alias() }

func (w whitelistUserRule) Columns(cols ...field.Expr) gen.Columns {
	return w.whitelistUserRuleDo.Columns(cols...)
}

func (w *whitelistUserRule) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *whitelistUserRule) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 7)
	w.fieldMap["id"] = w.ID
	w.fieldMap["user_id"] = w.UserID
	w.fieldMap["whitelist_id"] = w.WhitelistID
	w.fieldMap["used_count"] = w.UsedCount
	w.fieldMap["num"] = w.Num
	w.fieldMap["phone_number"] = w.PhoneNumber
	w.fieldMap["created_at"] = w.CreatedAt
}

func (w whitelistUserRule) clone(db *gorm.DB) whitelistUserRule {
	w.whitelistUserRuleDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w whitelistUserRule) replaceDB(db *gorm.DB) whitelistUserRule {
	w.whitelistUserRuleDo.ReplaceDB(db)
	return w
}

type whitelistUserRuleDo struct{ gen.DO }

type IWhitelistUserRuleDo interface {
	gen.SubQuery
	Debug() IWhitelistUserRuleDo
	WithContext(ctx context.Context) IWhitelistUserRuleDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWhitelistUserRuleDo
	WriteDB() IWhitelistUserRuleDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWhitelistUserRuleDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWhitelistUserRuleDo
	Not(conds ...gen.Condition) IWhitelistUserRuleDo
	Or(conds ...gen.Condition) IWhitelistUserRuleDo
	Select(conds ...field.Expr) IWhitelistUserRuleDo
	Where(conds ...gen.Condition) IWhitelistUserRuleDo
	Order(conds ...field.Expr) IWhitelistUserRuleDo
	Distinct(cols ...field.Expr) IWhitelistUserRuleDo
	Omit(cols ...field.Expr) IWhitelistUserRuleDo
	Join(table schema.Tabler, on ...field.Expr) IWhitelistUserRuleDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWhitelistUserRuleDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWhitelistUserRuleDo
	Group(cols ...field.Expr) IWhitelistUserRuleDo
	Having(conds ...gen.Condition) IWhitelistUserRuleDo
	Limit(limit int) IWhitelistUserRuleDo
	Offset(offset int) IWhitelistUserRuleDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWhitelistUserRuleDo
	Unscoped() IWhitelistUserRuleDo
	Create(values ...*model.WhitelistUserRule) error
	CreateInBatches(values []*model.WhitelistUserRule, batchSize int) error
	Save(values ...*model.WhitelistUserRule) error
	First() (*model.WhitelistUserRule, error)
	Take() (*model.WhitelistUserRule, error)
	Last() (*model.WhitelistUserRule, error)
	Find() ([]*model.WhitelistUserRule, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WhitelistUserRule, err error)
	FindInBatches(result *[]*model.WhitelistUserRule, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WhitelistUserRule) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWhitelistUserRuleDo
	Assign(attrs ...field.AssignExpr) IWhitelistUserRuleDo
	Joins(fields ...field.RelationField) IWhitelistUserRuleDo
	Preload(fields ...field.RelationField) IWhitelistUserRuleDo
	FirstOrInit() (*model.WhitelistUserRule, error)
	FirstOrCreate() (*model.WhitelistUserRule, error)
	FindByPage(offset int, limit int) (result []*model.WhitelistUserRule, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWhitelistUserRuleDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w whitelistUserRuleDo) Debug() IWhitelistUserRuleDo {
	return w.withDO(w.DO.Debug())
}

func (w whitelistUserRuleDo) WithContext(ctx context.Context) IWhitelistUserRuleDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w whitelistUserRuleDo) ReadDB() IWhitelistUserRuleDo {
	return w.Clauses(dbresolver.Read)
}

func (w whitelistUserRuleDo) WriteDB() IWhitelistUserRuleDo {
	return w.Clauses(dbresolver.Write)
}

func (w whitelistUserRuleDo) Session(config *gorm.Session) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Session(config))
}

func (w whitelistUserRuleDo) Clauses(conds ...clause.Expression) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w whitelistUserRuleDo) Returning(value interface{}, columns ...string) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w whitelistUserRuleDo) Not(conds ...gen.Condition) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w whitelistUserRuleDo) Or(conds ...gen.Condition) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w whitelistUserRuleDo) Select(conds ...field.Expr) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w whitelistUserRuleDo) Where(conds ...gen.Condition) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w whitelistUserRuleDo) Order(conds ...field.Expr) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w whitelistUserRuleDo) Distinct(cols ...field.Expr) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w whitelistUserRuleDo) Omit(cols ...field.Expr) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w whitelistUserRuleDo) Join(table schema.Tabler, on ...field.Expr) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w whitelistUserRuleDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWhitelistUserRuleDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w whitelistUserRuleDo) RightJoin(table schema.Tabler, on ...field.Expr) IWhitelistUserRuleDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w whitelistUserRuleDo) Group(cols ...field.Expr) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w whitelistUserRuleDo) Having(conds ...gen.Condition) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w whitelistUserRuleDo) Limit(limit int) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w whitelistUserRuleDo) Offset(offset int) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w whitelistUserRuleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w whitelistUserRuleDo) Unscoped() IWhitelistUserRuleDo {
	return w.withDO(w.DO.Unscoped())
}

func (w whitelistUserRuleDo) Create(values ...*model.WhitelistUserRule) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w whitelistUserRuleDo) CreateInBatches(values []*model.WhitelistUserRule, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w whitelistUserRuleDo) Save(values ...*model.WhitelistUserRule) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w whitelistUserRuleDo) First() (*model.WhitelistUserRule, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WhitelistUserRule), nil
	}
}

func (w whitelistUserRuleDo) Take() (*model.WhitelistUserRule, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WhitelistUserRule), nil
	}
}

func (w whitelistUserRuleDo) Last() (*model.WhitelistUserRule, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WhitelistUserRule), nil
	}
}

func (w whitelistUserRuleDo) Find() ([]*model.WhitelistUserRule, error) {
	result, err := w.DO.Find()
	return result.([]*model.WhitelistUserRule), err
}

func (w whitelistUserRuleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WhitelistUserRule, err error) {
	buf := make([]*model.WhitelistUserRule, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w whitelistUserRuleDo) FindInBatches(result *[]*model.WhitelistUserRule, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w whitelistUserRuleDo) Attrs(attrs ...field.AssignExpr) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w whitelistUserRuleDo) Assign(attrs ...field.AssignExpr) IWhitelistUserRuleDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w whitelistUserRuleDo) Joins(fields ...field.RelationField) IWhitelistUserRuleDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w whitelistUserRuleDo) Preload(fields ...field.RelationField) IWhitelistUserRuleDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w whitelistUserRuleDo) FirstOrInit() (*model.WhitelistUserRule, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WhitelistUserRule), nil
	}
}

func (w whitelistUserRuleDo) FirstOrCreate() (*model.WhitelistUserRule, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WhitelistUserRule), nil
	}
}

func (w whitelistUserRuleDo) FindByPage(offset int, limit int) (result []*model.WhitelistUserRule, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w whitelistUserRuleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w whitelistUserRuleDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w whitelistUserRuleDo) Delete(models ...*model.WhitelistUserRule) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *whitelistUserRuleDo) withDO(do gen.Dao) *whitelistUserRuleDo {
	w.DO = *do.(*gen.DO)
	return w
}
