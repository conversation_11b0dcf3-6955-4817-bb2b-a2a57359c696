// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameWhitelistUserRule = "whitelist_user_rule"

// WhitelistUserRule mapped from table <whitelist_user_rule>
type WhitelistUserRule struct {
	ID          string    `gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid()" json:"id"`
	UserID      string    `gorm:"column:user_id;type:uuid;not null;comment:用户 ID" json:"user_id"`              // 用户 ID
	WhitelistID string    `gorm:"column:whitelist_id;type:uuid;not null;comment:所属白名单 ID" json:"whitelist_id"` // 所属白名单 ID
	UsedCount   int32     `gorm:"column:used_count;type:integer;not null" json:"used_count"`
	Num         *int32    `gorm:"column:num;type:integer;comment:个性化数量限制（如最大购入/空投份额），NULL 表示不限制" json:"num"`                                                             // 个性化数量限制（如最大购入/空投份额），NULL 表示不限制
	PhoneNumber string    `gorm:"column:phone_number;type:text;not null;comment:手机号码" json:"phone_number"`                                                               // 手机号码
	CreatedAt   time.Time `gorm:"column:created_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoCreateTime;comment:记录创建时间" json:"created_at"` // 记录创建时间
}

// TableName WhitelistUserRule's table name
func (*WhitelistUserRule) TableName() string {
	return TableNameWhitelistUserRule
}
