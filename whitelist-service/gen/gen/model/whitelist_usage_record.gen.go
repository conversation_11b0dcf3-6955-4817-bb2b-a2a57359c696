// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameWhitelistUsageRecord = "whitelist_usage_record"

// WhitelistUsageRecord mapped from table <whitelist_usage_record>
type WhitelistUsageRecord struct {
	ID                  string     `gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid()" json:"id"`
	UserID              string     `gorm:"column:user_id;type:uuid;not null;comment:用户 ID" json:"user_id"`                                        // 用户 ID
	WhitelistUserRuleID string     `gorm:"column:whitelist_user_rule_id;type:uuid;not null;comment:白名单用户规则 ID" json:"whitelist_user_rule_id"`     // 白名单用户规则 ID
	UsedAt              *time.Time `gorm:"column:used_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP;comment:使用时间" json:"used_at"` // 使用时间
}

// TableName WhitelistUsageRecord's table name
func (*WhitelistUsageRecord) TableName() string {
	return TableNameWhitelistUsageRecord
}
