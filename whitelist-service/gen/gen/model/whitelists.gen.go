// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameWhitelist = "whitelists"

// Whitelist mapped from table <whitelists>
type Whitelist struct {
	ID          string     `gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid()" json:"id"`
	RuleType    int16      `gorm:"column:rule_type;type:smallint;not null;comment:规则类型：1.某个用户优先购,2.全量用户白名单" json:"rule_type"`                                              // 规则类型：1.某个用户优先购,2.全量用户白名单
	RuleTypeID  string     `gorm:"column:rule_type_id;type:uuid;not null;comment:规则关联对象 ID，例如活动 ID、发售计划 ID 等" json:"rule_type_id"`                                         // 规则关联对象 ID，例如活动 ID、发售计划 ID 等
	StartTime   time.Time  `gorm:"column:start_time;type:timestamp(6) with time zone;not null;comment:白名单生效开始时间" json:"start_time"`                                        // 白名单生效开始时间
	EndTime     *time.Time `gorm:"column:end_time;type:timestamp(6) with time zone;default:NULL;comment:白名单生效结束时间（可选）" json:"end_time"`                                    // 白名单生效结束时间（可选）
	Num         *int32     `gorm:"column:num;type:integer;comment:白名单数量限制，NULL 表示不限制,只对全量用户白名单有效" json:"num"`                                                              // 白名单数量限制，NULL 表示不限制,只对全量用户白名单有效
	Name        string     `gorm:"column:name;type:text;not null;comment:白名单名称" json:"name"`                                                                               // 白名单名称
	Description *string    `gorm:"column:description;type:text;comment:白名单描述" json:"description"`                                                                          // 白名单描述
	CreatedAt   time.Time  `gorm:"column:created_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoCreateTime;comment:白名单创建时间" json:"created_at"` // 白名单创建时间
}

// TableName Whitelist's table name
func (*Whitelist) TableName() string {
	return TableNameWhitelist
}
