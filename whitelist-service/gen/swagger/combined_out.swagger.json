{"swagger": "2.0", "info": {"title": "ces/whitelist/whitelist/whitelist.proto", "version": "version not set"}, "tags": [{"name": "WhitelistService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/whitelist/configure": {"post": {"summary": "配置（创建）白名单", "operationId": "WhitelistService_Create<PERSON><PERSON>elist", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/whitelist<PERSON><PERSON><PERSON>st"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/whitelistCreateWhitelistReq"}}], "tags": ["WhitelistService"]}}, "/v1/whitelist/{id}": {"get": {"summary": "获取白名单信息", "operationId": "WhitelistService_GetWhitelist", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/whitelist<PERSON><PERSON><PERSON>st"}}}, "parameters": [{"name": "id", "description": "白名单 ID", "in": "path", "required": true, "type": "string"}], "tags": ["WhitelistService"]}, "put": {"summary": "更新白名单信息(仅在发售开始前可操作)", "operationId": "WhitelistService_UpdateWhitelist", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/whitelist<PERSON><PERSON><PERSON>st"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/WhitelistServiceUpdateWhitelistBody"}}], "tags": ["WhitelistService"]}}, "/v1/whitelist/{whitelistID}/import": {"post": {"summary": "批量导入白名单用户（通过excel文件上传）", "operationId": "WhitelistService_ImportWhitelistUsers", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/whitelistImportWhitelistUsersResp"}}}, "parameters": [{"name": "whitelistID", "description": "白名单 ID", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/WhitelistServiceImportWhitelistUsersBody"}}], "tags": ["WhitelistService"]}}, "/v1/whitelist/list": {"get": {"summary": "获取白名单列表（按时间从近到远）", "operationId": "WhitelistService_ListWhitelists", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/whitelistListWhitelistsResp"}}}, "parameters": [{"name": "page", "description": "当前页码", "in": "query", "required": true, "type": "integer", "format": "int32"}, {"name": "pageSize", "description": "每页数量", "in": "query", "required": true, "type": "integer", "format": "int32"}], "tags": ["WhitelistService"]}}}, "definitions": {"BatchConfigureUserWhitelistRPCRespInvalidUserInfo": {"type": "object", "properties": {"user_id": {"type": "string"}, "reason": {"type": "string", "title": "失败原因"}}}, "BatchConfigureUserWhitelisttByPhoneNumberRPCRespInvalidPhoneInfo": {"type": "object", "properties": {"phone_number": {"type": "string"}, "reason": {"type": "string", "title": "失败原因"}}}, "GetWhitelistUserRuleListRPCRespWhitelistUserRule": {"type": "object", "properties": {"user_id": {"type": "string"}, "phone_number": {"type": "string"}, "num": {"type": "integer", "format": "int32"}, "created_at": {"type": "string", "format": "int64", "title": "创建时间"}}}, "ImportWhitelistUsersRespDuplicateRecord": {"type": "object", "properties": {"phone_number": {"type": "string"}, "user_id": {"type": "string"}, "old_num": {"type": "integer", "format": "int32"}, "new_num": {"type": "integer", "format": "int32"}}}, "ImportWhitelistUsersRespFailedRecord": {"type": "object", "properties": {"row_number": {"type": "integer", "format": "int32", "title": "Excel 行号（从2开始，因为第1行是表头）"}, "phone_number": {"type": "string", "title": "手机号"}, "reason": {"type": "string", "title": "失败原因"}}}, "WhitelistServiceImportWhitelistUsersBody": {"type": "object", "properties": {"fileContent": {"type": "string", "format": "byte", "title": "Excel 文件内容（二进制）"}, "fileName": {"type": "string", "title": "原始文件名，用于判断是解析csv还是xlsx"}, "token": {"type": "string", "title": "token"}}, "required": ["fileContent", "fileName", "token"]}, "WhitelistServiceUpdateWhitelistBody": {"type": "object", "properties": {"typeID": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "description": {"type": "string"}, "startTime": {"type": "string", "format": "int64", "title": "生效开始时间（时间戳）"}, "endTime": {"type": "string", "format": "int64", "title": "生效结束时间（时间戳,可选）"}, "num": {"type": "integer", "format": "int32", "title": "数量限制, 0 表示无限制,仅针对全量用户白名单"}, "token": {"type": "string", "title": "token"}}, "required": ["typeID", "type", "name", "startTime", "token"]}, "whitelistBatchConfigureUserWhitelistRPCReqUserWhitelist": {"type": "object", "properties": {"userID": {"type": "string", "title": "用户 ID"}, "num": {"type": "integer", "format": "int32", "title": "数量限制, 0 表示无限制"}}, "required": ["userID", "num"]}, "whitelistBatchConfigureUserWhitelistRPCResp": {"type": "object", "properties": {"total_created": {"type": "integer", "format": "int32", "title": "成功创建的总数"}, "total_updated": {"type": "integer", "format": "int32", "title": "更新现有记录的数量"}, "existing_users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/whitelistBatchConfigureUserWhitelistRPCRespExistingUserInfo"}, "title": "已存在的用户信息"}, "invalid_users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/BatchConfigureUserWhitelistRPCRespInvalidUserInfo"}, "title": "无效的用户信息（用户不存在）"}}}, "whitelistBatchConfigureUserWhitelistRPCRespExistingUserInfo": {"type": "object", "properties": {"user_id": {"type": "string"}, "phone_number": {"type": "string"}, "old_num": {"type": "integer", "format": "int32"}, "new_num": {"type": "integer", "format": "int32"}}}, "whitelistBatchConfigureUserWhitelisttByPhoneNumberRPCReqUserWhitelist": {"type": "object", "properties": {"phoneNumber": {"type": "string", "title": "手机号"}, "num": {"type": "integer", "format": "int32", "title": "数量限制, 0 表示无限制"}}, "required": ["phoneNumber", "num"]}, "whitelistBatchConfigureUserWhitelisttByPhoneNumberRPCResp": {"type": "object", "properties": {"total_created": {"type": "integer", "format": "int32", "title": "成功创建的总数"}, "total_updated": {"type": "integer", "format": "int32", "title": "更新现有记录的数量"}, "existing_users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/whitelistBatchConfigureUserWhitelisttByPhoneNumberRPCRespExistingUserInfo"}, "title": "已存在的用户信息"}, "invalid_phones": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/BatchConfigureUserWhitelisttByPhoneNumberRPCRespInvalidPhoneInfo"}, "title": "无效的手机号信息（用户不存在）"}}}, "whitelistBatchConfigureUserWhitelisttByPhoneNumberRPCRespExistingUserInfo": {"type": "object", "properties": {"phone_number": {"type": "string"}, "user_id": {"type": "string"}, "old_num": {"type": "integer", "format": "int32"}, "new_num": {"type": "integer", "format": "int32"}}}, "whitelistCheckWhitelistQualificationForFuctionRPCResp": {"type": "object", "properties": {"hasQualification": {"type": "boolean", "title": "是否具有白名单资格"}, "usageCount": {"type": "string", "format": "int64", "title": "已使用次数"}, "totalCount": {"type": "string", "format": "int64", "title": "总次数 0 表示无限制"}}}, "whitelistCheckWhitelistQualificationRPCResp": {"type": "object", "properties": {"hasQualification": {"type": "boolean", "title": "是否具有白名单资格"}, "usageCount": {"type": "string", "format": "int64", "title": "已使用次数"}, "totalCount": {"type": "string", "format": "int64", "title": "总次数 0 表示无限制"}}}, "whitelistCreateWhitelistReq": {"type": "object", "properties": {"typeID": {"type": "string", "title": "关联业务 ID（如发售/活动等）"}, "type": {"type": "integer", "format": "int32", "title": "类型：1.一级优先购 2.一级全量 3.合成优先 4.合成全量"}, "name": {"type": "string", "title": "白名单名称"}, "description": {"type": "string", "title": "描述"}, "startTime": {"type": "string", "format": "int64", "title": "生效开始时间（时间戳）"}, "endTime": {"type": "string", "format": "int64", "title": "生效结束时间（时间戳,可选）"}, "num": {"type": "integer", "format": "int32", "title": "数量限制, 0 表示无限制,仅针对全量用户白名单"}, "token": {"type": "string", "title": "token"}}, "required": ["typeID", "type", "name", "startTime", "token"]}, "whitelistGetWhitelistUserRuleListRPCResp": {"type": "object", "properties": {"user_rules": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/GetWhitelistUserRuleListRPCRespWhitelistUserRule"}}, "total": {"type": "string", "format": "int64", "title": "总记录数"}}}, "whitelistGetWhitelistsRPCResp": {"type": "object", "properties": {"whitelists": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/whitelist<PERSON><PERSON><PERSON>st"}, "title": "白名单列表"}, "total": {"type": "string", "format": "int64", "title": "总记录数"}}}, "whitelistImportWhitelistUsersResp": {"type": "object", "properties": {"total_imported": {"type": "integer", "format": "int32", "title": "成功导入的总数"}, "total_updated": {"type": "integer", "format": "int32", "title": "更新现有记录的数量"}, "existing_users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/whitelistImportWhitelistUsersRespExistingUserInfo"}, "title": "已存在的用户信息"}, "failed_records": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/ImportWhitelistUsersRespFailedRecord"}, "title": "导入失败的记录"}, "duplicate_records": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/ImportWhitelistUsersRespDuplicateRecord"}, "title": "导入文件中重复的记录"}}}, "whitelistImportWhitelistUsersRespExistingUserInfo": {"type": "object", "properties": {"phone_number": {"type": "string"}, "user_id": {"type": "string"}, "old_num": {"type": "integer", "format": "int32"}, "new_num": {"type": "integer", "format": "int32"}}}, "whitelistListWhitelistsResp": {"type": "object", "properties": {"whitelists": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/whitelist<PERSON><PERSON><PERSON>st"}, "title": "白名单列表"}, "total": {"type": "string", "format": "int64", "title": "总记录数"}}}, "whitelistWhitelist": {"type": "object", "properties": {"id": {"type": "string", "title": "白名单 ID"}, "type_id": {"type": "string", "title": "活动/项目 ID"}, "type": {"type": "integer", "format": "int32", "title": "类型（1.一级优先购, 2.一级全量, 3.合成优先, 4.合成全量）"}, "name": {"type": "string", "title": "标题"}, "description": {"type": "string", "title": "白名单描述"}, "start_time": {"type": "string", "format": "int64", "title": "生效的开始时间"}, "end_time": {"type": "string", "format": "int64", "title": "生效的结束时间"}, "created_at": {"type": "string", "format": "int64", "title": "创建时间"}, "num": {"type": "integer", "format": "int32", "title": "数量限制(0或NULL表示无限制，仅对全量用户白名单有效)"}}, "title": "白名单对象"}}}