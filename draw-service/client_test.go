package main

import (
	poolpb "cnb.cool/cymirror/ces-services/draw-service/gen/proto/ces/draw/pool"
	"context"
	"fmt"
	"google.golang.org/grpc/credentials/insecure"
	"math/rand"
	"testing"
	"time"

	"github.com/hashicorp/consul/api" // 引入官方 Consul API 包
	"google.golang.org/grpc"
)

// TestGrpcConnectionWithConsulAPI 使用官方包手动进行服务发现并测试连接
func TestGrpcConnectionWithConsulAPI(t *testing.T) {
	// --- 测试的准备阶段 (Arrange) ---
	consulAddr := "127.0.0.1:8500" // 你的 Consul 地址
	serviceName := "draw-service"  // 你的 gRPC 服务在 Consul 中注册的名称

	// 1. 初始化 Consul API 客户端
	config := api.DefaultConfig()
	config.Address = consulAddr
	// 【修改点 1】将 Consul 客户端命名为 consulClient，以明确其职责
	consulClient, err := api.NewClient(config)
	if err != nil {
		t.Fatalf("无法创建 Consul 客户端: %v", err)
	}

	// 2. 从 Consul 查询健康的服务实例
	// 第三个参数 true 表示 "passingOnly"，只返回通过健康检查的服务
	// 【修改点 2】使用新的变量名 consulClient
	services, _, err := consulClient.Health().Service(serviceName, "", true, nil)
	if err != nil {
		t.Fatalf("从 Consul 查询服务失败: %v", err)
	}

	if len(services) == 0 {
		t.Fatalf("在 Consul 中未找到健康的 '%s' 服务实例", serviceName)
	}

	// 3. 实现一个简单的负载均衡策略：随机选择一个健康实例
	// 使用当前时间作为随机数种子，确保每次运行的选择都可能不同
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	instance := services[r.Intn(len(services))]
	targetAddr := fmt.Sprintf("%s:%d", instance.Service.Address, instance.Service.Port)

	// --- 执行阶段 (Act) ---
	t.Logf("通过 Consul 发现服务 '%s'，随机选择实例: %s", serviceName, targetAddr)

	// 4. 尝试建立 gRPC 连接
	conn, err := grpc.Dial(
		targetAddr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	if err != nil {
		t.Fatalf("gRPC Dial 失败: %v", err)
	}
	defer conn.Close()

	// 【修改点 3】将 gRPC 客户端命名为 poolClient，避免冲突
	poolClient := poolpb.NewPoolServiceClient(conn)

	// 现在您可以使用 poolClient 进行 RPC 调用了
	//比如取消这行注释来进行测试：

	//resp, err := creat(poolClient)

	// 修改
	//_, err = poolClient.UpdatePoolRPC(context.Background(), &poolpb.UpdatePoolRPCReq{
	//	PoolId: resp.PoolId,
	//	Pool: &poolpb.Pool{
	//		Name: "test2",
	//	},
	//})

	// 获取签池信息

	// 用户报名信息

	// 报名
	//for _, userId := range userIds {
	//	_, err = poolClient.SignUp(context.Background(), &poolpb.SignUpReq{
	//		UserId: userId,
	//		PoolId: "54ba315b-532f-477e-b6a0-0f24923c60f3",
	//	})
	//	if err != nil {
	//		t.Fatalf("RPC 调用失败: %v", err)
	//	}
	//}

	//// 添加白名单签数
	for _, userId := range whitelistUserIds {
		_, err = poolClient.AddWhitelistUsers(context.Background(), &poolpb.AddWhitelistUsersReq{
			UserId:      userId,
			PoolId:      "54ba315b-532f-477e-b6a0-0f24923c60f3",
			TicketCount: RandInt32(1, 3),
		})
		if err != nil {
			t.Fatalf("RPC 调用失败: %v", err)
		}
	}

	if err != nil {
		t.Fatalf("RPC 调用失败: %v", err)
	}
	//t.Logf("RPC 调用成功: %v", resp)
	t.Logf("成功连接到 gRPC 实例并创建客户端: %s", targetAddr)

	// 断言 poolClient 不为 nil 来确认客户端已创建 (可选)
	if poolClient == nil {
		t.Fatal("创建的 poolClient 不应为 nil")
	}
}

// 创建
func creat(poolClient poolpb.PoolServiceClient) (*poolpb.CreatePoolRPCResp, error) {
	marketThreshold := int32(1)
	inviteThreshold := int32(1)
	maxWinningSigns := int32(5)
	ticketCap := int32(20)

	// 创建
	resp, err := poolClient.CreatePoolRPC(context.Background(), &poolpb.CreatePoolRPCReq{
		Pool: &poolpb.Pool{
			Name:                  "test",
			PrizeName:             "test",
			ImageUrl:              "test",
			MarketThreshold:       &marketThreshold,
			InviteThreshold:       &inviteThreshold,
			ProjectId:             "a8835fe5-2429-4861-beff-5a1321a7ae65",
			SnapshotScheduleId:    "a8835fe5-2429-4861-beff-5a1321a7ae65",
			WhitelistId:           "a8835fe5-2429-4861-beff-5a1321a7ae65",
			RegistrationStartTime: time.Now().Unix(),
			RegistrationEndTime:   time.Now().Add(10 * time.Minute).Unix(),
			SnapshotTime:          time.Now().Add(15 * time.Minute).Unix(),
			DrawTime:              time.Now().Add(20 * time.Minute).Unix(),
			Status:                poolpb.PoolStatus_POOL_STATUS_PUBLISHED,
			WinningSupply:         100,
			MaxWinningSigns:       &maxWinningSigns,
		},
		Sources: []*poolpb.Source{
			{
				Type:             poolpb.SourceType_SOURCE_TYPE_MARKET_CAP,
				MarketType:       poolpb.MarketType_MARKET_TYPE_CUSTODY,
				TicketsPerAction: 1,
				TicketCap:        &ticketCap,
			},
			{
				Type:             poolpb.SourceType_SOURCE_TYPE_MARKET_CAP,
				MarketType:       poolpb.MarketType_MARKET_TYPE_FLASH,
				TicketsPerAction: 1,
				TicketCap:        &ticketCap,
			},
			{
				Type:             poolpb.SourceType_SOURCE_TYPE_INVITE,
				MarketType:       poolpb.MarketType_MARKET_TYPE_CUSTODY,
				TicketsPerAction: 1,
				TicketCap:        &ticketCap,
			},
			{
				Type:             poolpb.SourceType_SOURCE_TYPE_INVITE,
				MarketType:       poolpb.MarketType_MARKET_TYPE_FLASH,
				TicketsPerAction: 1,
				TicketCap:        &ticketCap,
			},
		},
	})

	return resp, err
}

func RandInt32(min, max int32) int32 {
	return min + rand.Int31n(max-min+1)
}

var userIds = []string{
	"a542444e-2708-4b38-8939-ff81842820fd",
	"53778d5b-5d4d-455e-b2f2-d42325b28f99",
	"a1c43b8c-7dcf-4e1d-b0fc-07469ce57d99",
	"74ca96cf-2458-406a-864d-ff079ce77ba0",
	"b4f1dd3f-3e23-4c67-ba01-4d5377af9ede",
	"ae9a1a15-fb13-4135-841d-5fef48cbf46d",
	"8a4ddefc-c32e-44e9-b286-59b97a4bfcca",
	"c4501759-7bfc-4684-9ea5-bfb1ce7f96fb",
	"7eef0a4b-c1be-4215-b257-59d766c133f3",
	"e6dfb5e5-bfb2-48e2-8cfd-bf84d95d0c8c",
	"401e03a2-ef0c-4ce8-a008-67321ed477eb",
	"9290c618-8ba4-4cd2-a079-43e235761d16",
	"088c5df5-3629-42cf-a0fe-9f522bb9d236",
	"51046e2f-759b-4543-81e2-251a43e5a5ba",
	"6bcf355c-24ab-4c52-ae37-dfefbd9d6886",
	"62a912f6-f980-47c3-bb17-b815c85e2447",
	"71e3201c-6577-46b0-85d0-110d62d8ad1e",
	"b657aab3-d10c-4e80-af42-e91ea4ee8e73",
	"0c03dabc-0103-4808-bb7d-260246383f89",
	"205f56b3-944b-496b-9f9f-1d9f406dc691",
	"ea50f270-4a7c-4225-8cb8-44a7e4cc8d58",
	"19e736f1-909a-4ea0-966c-685f0843c6c3",
	"82ab06c7-b965-4cfc-ab90-695893075618",
	"1da065b8-5386-476a-9d0e-0310761262ae",
	"81d71c46-a494-4734-a13f-713c590c6846",
	"b6a5588e-116a-4899-a291-5b7c8a0f1fc3",
	"d7664796-ab48-4299-925e-8e52723da4d6",
	"23ac5d31-d48e-4b31-9baf-348a839bd9ce",
	"4411011d-7c6b-4504-9f0d-ca1453c6d0af",
	"d560992a-5ab3-46ea-a65e-a2352c7b4523",
	"245d68c8-a4fa-43e0-a58b-a44b23670c40",
	"0e214c13-0fce-4ad0-a9a8-58cc6bca8809",
	"a16fa366-c5b9-4c73-ad7b-0ae9a0968fab",
	"16b4498d-ed48-4f23-84f4-d3da7c537b2e",
	"98d70d0d-a5df-42bb-89b4-dba44f886f1f",
	"4fe7c3a2-ad8f-4693-8974-5d1c2aa94f36",
	"6a1279a8-c7ec-4d94-ae1b-561b08beb4c9",
	"a4aa76b8-837d-492b-8b1b-49260dcc31bb",
	"3628da68-4682-4c54-81ff-4b426440bd17",
	"5a372828-775a-451e-aec1-c1b0fecd63cb",
	"b3097207-4ba2-4e89-a149-d6a8f0c98a7b",
	"fc452f33-f50c-4a87-af44-14a311ac70ee",
	"67ac9406-9e70-4d36-83c2-ab62a5d2f62a",
	"b0cc2595-c00f-4f52-a447-47b375c84536",
	"3218f51c-cbe0-4773-ab04-450f2007b19f",
	"010c6a5b-095d-4152-aabb-aafdee019124",
	"06376e46-d2d6-4b8c-99b0-16e4cbc42f4c",
	"3d6e20b3-bfa7-4482-885d-3c4e7cbf48b7",
	"b4f84270-fd5b-4540-aed4-eb9e2176b13b",
	"170d2de5-f72e-4a51-8463-6e43be75e13d",
	"f215b281-8106-4f8d-a405-2516485f3857",
	"0981a2ac-239a-4004-88cb-073832d25667",
	"72114792-5629-4a41-a865-dfc3256cf724",
	"e396d26e-9da8-4327-ad24-5604648ead67",
	"170f9808-4b95-4955-8fab-d43b09484c82",
	"13f5f0c0-c906-4281-b769-05cb74272adf",
	"1cd48b0b-e516-461e-b146-186956c963ea",
	"2ed7b99e-0469-4ba1-a03f-b68ac00d4600",
	"cb1507ad-4f8a-4c75-90b4-74f31ea0d170",
	"c654ae1e-e008-4bf3-bdd9-064dae00939e",
	"16333118-75d6-4bcc-b0f1-0a07e1cabe1c",
	"7aad3174-fefd-473e-936f-92e6d2c8c462",
	"7c23614c-6e54-4af8-a63b-8533ef2af000",
	"31a7616d-cce6-4a41-afef-658dc66c3422",
	"460ce5c3-2de3-415d-be1b-f5190c78f6fd",
	"1df714bf-06f1-4e22-8ccf-8cd3969ef470",
	"5e568f3b-3627-432d-922c-cdd196d48391",
	"a934c778-7b72-4edb-90ed-47c09bed9015",
	"12f54e5a-89f8-4ab8-b406-b7f3d8ed9ed4",
	"87a219ab-79c7-4463-a20b-d95c10374fbb",
	"9fbc04f8-fda1-4188-8032-45f603cc15da",
	"a5d42f29-418e-4cb3-aefb-4e3a27bd87cb",
	"f8718f01-e602-403e-ad2d-54578c344608",
	"b21a3060-9ec0-4c5f-b6f5-0c9bb989e3bb",
	"ec41310f-2101-4f51-a647-aa9a90a19dad",
	"fc3dd62d-1a40-4111-b3a3-baca56b8bc9c",
	"85f587d8-6bd5-4a41-97bf-ab1c23de6967",
	"603bcd7a-e598-4185-a691-91e7c439b068",
	"9538dbd7-d335-4c77-9711-ad23d664ff12",
	"6f839242-7527-4219-a38d-af40dbe3cd32",
	"5fe07373-a4e8-4b31-9da0-e8a76c7fd660",
	"4a84e8d4-430b-4b3d-bd09-5b70966fd86c",
	"40154c5d-fbee-466f-af94-0857da97679d",
	"33c9c998-5e87-4647-a814-60bccf80f760",
	"5fbee440-1d65-4cfa-b827-3cf8b413ccc1",
	"709f624f-f553-44e1-ac29-1513670dcda2",
	"d670965c-2fa6-47eb-b7e6-009bcf3936c2",
}

var whitelistUserIds = []string{
	"a542444e-2708-4b38-8939-ff81842820fd",
	"53778d5b-5d4d-455e-b2f2-d42325b28f99",
	"6c22fe7b-9e27-4953-854a-66b8c66d844f",
	"e6dfb5e5-bfb2-48e2-8cfd-bf84d95d0c8c",
	"401e03a2-ef0c-4ce8-a008-67321ed477eb",
	"9290c618-8ba4-4cd2-a079-43e235761d16",
	"088c5df5-3629-42cf-a0fe-9f522bb9d236",
	"170f9808-4b95-4955-8fab-d43b09484c82",
	"13f5f0c0-c906-4281-b769-05cb74272adf",
	"1cd48b0b-e516-461e-b146-186956c963ea",
	"2ed7b99e-0469-4ba1-a03f-b68ac00d4600",
	"cb1507ad-4f8a-4c75-90b4-74f31ea0d170",
	"c654ae1e-e008-4bf3-bdd9-064dae00939e",
	"16333118-75d6-4bcc-b0f1-0a07e1cabe1c",
	"7aad3174-fefd-473e-936f-92e6d2c8c462",
	"7c23614c-6e54-4af8-a63b-8533ef2af000",
	"31a7616d-cce6-4a41-afef-658dc66c3422",
	"460ce5c3-2de3-415d-be1b-f5190c78f6fd",
	"1df714bf-06f1-4e22-8ccf-8cd3969ef470",
	"5e568f3b-3627-432d-922c-cdd196d48391",
	"d670965c-2fa6-47eb-b7e6-009bcf3936c2",
}
