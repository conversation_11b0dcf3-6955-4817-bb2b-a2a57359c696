package repository

import (
	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/common/server"
	"cnb.cool/cymirror/ces-services/draw-service/gen/gen/model"
	"cnb.cool/cymirror/ces-services/draw-service/gen/gen/query"
	"cnb.cool/cymirror/ces-services/draw-service/internal/domain/pool"
	inframodel "cnb.cool/cymirror/ces-services/draw-service/internal/infra/repository/model"
	"context"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var _ pool.Repo = (*PoolRepository)(nil)

type PoolRepository struct {
	db *db.DB[*query.Query]
}

func NewPoolRepository(db *db.DB[*query.Query]) *PoolRepository {
	return &PoolRepository{db: db}
}



func (a PoolRepository) CreatePool(ctx context.Context, p *pool.Pool) error {
	d := a.db.Get(ctx)
	m := p.ToModel()
	err := d.DrawPool.WithContext(ctx).
		Create(m)
	if err != nil {
		zap.L().Error("failed to create pool", zap.Error(err))
		return err
	}
	*p = *pool.NewPoolFromModel(m)
	return nil
}

func (a PoolRepository) UpdatePool(ctx context.Context, p *pool.Pool) error {
	d := a.db.Get(ctx)
	m := p.ToModel()
	result, err := d.DrawPool.WithContext(ctx).
		Where(d.DrawPool.ID.Eq(m.ID)).
		Updates(m)
	if err != nil {
		zap.L().Error("UpdatePool failed", zap.Error(err), zap.String("poolId", m.ID))
		return server.InternalStatus
	}

	if result.RowsAffected == 0 {
		zap.L().Info("UpdatePool no changes made", zap.String("poolId", m.ID))
		return status.Error(codes.NotFound, "pool not found or no changes made")
	}
	*p = *pool.NewPoolFromModel(m)
	return nil
}

func (a PoolRepository) GetPoolByPoolId(ctx context.Context, poolID string) (*pool.Pool, error) {
	d := a.db.Get(ctx)

	p, err := d.DrawPool.WithContext(ctx).
		Where(d.DrawPool.ID.Eq(poolID)).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, status.Error(codes.NotFound, "pool not found")
		}
		zap.L().Error("GetPoolByPoolId failed", zap.Error(err), zap.String("poolID", poolID))
		return nil, server.InternalStatus
	}
	return pool.NewPoolFromModel(p), nil
}

func (a PoolRepository) GetPoolByPoolIds(ctx context.Context, poolIDs []string) ([]*pool.Pool, error) {
	d := a.db.Get(ctx)

	ps, err := d.DrawPool.WithContext(ctx).
		Where(d.DrawPool.ID.In(poolIDs...)).
		Find()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		zap.L().Error("GetPoolByPoolIds failed", zap.Error(err), zap.Any("poolIDs", poolIDs))
		return nil, server.InternalStatus
	}
	if ps == nil {
		return nil, nil
	}
	result := make([]*pool.Pool, 0, len(ps))
	for _, p := range ps {
		result = append(result, pool.NewPoolFromModel(p))
	}
	return result, nil
}

func (a PoolRepository) GetPoolTicketByPoolId(ctx context.Context, poolID string) (*inframodel.PoolTicket, error) {
	d := a.db.Get(ctx)
	var result inframodel.PoolTicket

	// 使用底层 *gorm.DB 进行原生表达式统计，单条 SQL 完成统计
	// Use raw SQL strings instead of gorm.Expr to avoid parsing issues
	whitelistCountSQL := fmt.Sprintf("SUM(CASE WHEN %s = true THEN 1 ELSE 0 END) AS %s",
		d.DrawPoolTicket.IsWhitelistTicket.ColumnName(),
		inframodel.EmptyPoolTicket.WhitelistTicketsName())
	winnerCountSQL := fmt.Sprintf("SUM(CASE WHEN %s = true THEN 1 ELSE 0 END) AS %s",
		d.DrawPoolTicket.IsWinner.ColumnName(),
		inframodel.EmptyPoolTicket.WinnerCountName())

	err := d.DrawPoolTicket.WithContext(ctx).UnderlyingDB().
		Select(
			d.DrawPoolTicket.PoolID.As(inframodel.EmptyPoolTicket.PoolIDName()),
			d.DrawPoolTicket.PoolID.Count().As(inframodel.EmptyPoolTicket.CountName()),
			whitelistCountSQL,
			winnerCountSQL).
		Where(d.DrawPoolTicket.PoolID.Eq(poolID)).
		Group(d.DrawPoolTicket.PoolID.ColumnName().String()).
		Scan(&result).Error

	if err != nil {
		zap.L().Error("GetPoolTicketByPoolId failed", zap.Error(err), zap.String("poolID", poolID))
		return nil, err
	}
	return &result, nil
}

func (a PoolRepository) CreatePoolSources(ctx context.Context, sources []*pool.PoolSource) error {
	d := a.db.Get(ctx)
	models := make([]*model.DrawPoolSource, 0, len(sources))
	for _, source := range sources {
		models = append(models, source.ToModel())
	}
	err := d.DrawPoolSource.WithContext(ctx).Create(models...)
	if err != nil {
		zap.L().Error("failed to create pool sources", zap.Error(err))
		return err
	}
	for i, m := range models {
		if i < len(sources) {
			*sources[i] = *pool.NewPoolSourceFromModel(m)
		}
	}
	return nil
}

func (a PoolRepository) UpdatePoolSources(ctx context.Context, sources []*pool.PoolSource) error {
	d := a.db.Get(ctx)

	models := make([]*model.DrawPoolSource, 0, len(sources))
	for _, source := range sources {
		models = append(models, source.ToModel())
	}
	err := d.DrawPoolSource.WithContext(ctx).Save(models...)
	if err != nil {
		zap.L().Error("failed to update pool sources", zap.Error(err))
		return err
	}

	for i, m := range models {
		if i < len(sources) {
			*sources[i] = *pool.NewPoolSourceFromModel(m)
		}
	}

	return nil
}

func (a PoolRepository) GetPoolSourcesByPoolId(ctx context.Context, poolID string) ([]*pool.PoolSource, error) {
	d := a.db.Get(ctx)

	sources, err := d.DrawPoolSource.WithContext(ctx).
		Where(d.DrawPoolSource.PoolID.Eq(poolID)).
		Find()

	if err != nil {
		zap.L().Error("GetPoolSourcesByPoolId failed", zap.Error(err), zap.String("poolID", poolID))
		return nil, server.InternalStatus
	}

	result := make([]*pool.PoolSource, 0, len(sources))
	for _, source := range sources {
		result = append(result, pool.NewPoolSourceFromModel(source))
	}
	return result, nil
}

func (a PoolRepository) CreatPoolRegistrations(ctx context.Context, registration *pool.PoolRegistration) error {
	d := a.db.Get(ctx)

	m := registration.ToModel()
	err := d.DrawPoolRegistration.WithContext(ctx).
		Create(m)
	if err != nil {
		zap.L().Error("failed to create pool registration", zap.Error(err))
		return err
	}
	*registration = *pool.NewPoolRegistrationFromModel(m)
	return nil
}

func (a PoolRepository) GetPoolRegistrationsByUserId(ctx context.Context, userID string, poolID string) (*pool.PoolRegistration, error) {
	d := a.db.Get(ctx)

	r, err := d.DrawPoolRegistration.WithContext(ctx).
		Where(d.DrawPoolRegistration.PoolID.Eq(poolID)).
		Where(d.DrawPoolRegistration.UserID.Eq(userID)).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		zap.L().Error("GetPoolRegistrationsByUserId failed", zap.Error(err), zap.String("poolID", poolID), zap.String("userID", userID))
		return nil, server.InternalStatus
	}

	return pool.NewPoolRegistrationFromModel(r), nil

}

func (a PoolRepository) GetPoolRegistrationsByUserIds(ctx context.Context, poolID string, userIDs []string) ([]*pool.PoolRegistration, error) {
	d := a.db.Get(ctx)

	rs, err := d.DrawPoolRegistration.WithContext(ctx).
		Where(d.DrawPoolRegistration.PoolID.Eq(poolID)).
		Where(d.DrawPoolRegistration.UserID.In(userIDs...)).
		Find()

	if err != nil {
		zap.L().Error("GetPoolRegistrationsByUserIds failed", zap.Error(err), zap.String("poolID", poolID), zap.Any("userIDs", userIDs))
		return nil, server.InternalStatus
	}

	if rs == nil {
		return nil, nil
	}

	result := make([]*pool.PoolRegistration, 0, len(rs))
	for _, r := range rs {
		result = append(result, pool.NewPoolRegistrationFromModel(r))
	}
	return result, nil
}

func (a PoolRepository) GetUserTicketGroupBySourceIds(ctx context.Context, userId string, sourceIds []string) ([]*inframodel.UserSource, error) {
	d := a.db.Get(ctx)

	var results []*inframodel.UserSource
	err := d.DrawPoolTicket.WithContext(ctx).
		Select(d.DrawPoolTicket.SourceID.As(inframodel.EmptyUserSource.SourceIDName()),
			d.DrawPoolTicket.ID.Count().As(inframodel.EmptyUserSource.CountName()),
			d.DrawPoolTicket.IsWinner.Count().As(inframodel.EmptyUserSource.WinnerCountName())).
		Where(d.DrawPoolTicket.UserID.Eq(userId)).
		Where(d.DrawPoolTicket.SourceID.In(sourceIds...)).
		Group(d.DrawPoolTicket.SourceID).
		Scan(&results)

	if err != nil {
		zap.L().Error("GetUserTicketGroupBySourceIds failed", zap.Error(err), zap.String("userID", userId), zap.Any("sourceIDs", sourceIds))
		return nil, server.InternalStatus
	}

	return results, nil
}

// UpdatePoolRegistrations 批量更新报名信息
func (a PoolRepository) UpdatePoolRegistrations(ctx context.Context, registrations []*pool.PoolRegistration) error {
	d := a.db.Get(ctx)
	if len(registrations) == 0 {
		return nil
	}

	models := make([]*model.DrawPoolRegistration, 0, len(registrations))
	for _, r := range registrations {
		if r == nil {
			continue
		}
		models = append(models, r.ToModel())
	}
	if len(models) == 0 {
		return nil
	}

	if err := d.DrawPoolRegistration.WithContext(ctx).Save(models...); err != nil {
		zap.L().Error("failed to update pool registrations", zap.Error(err))
		return err
	}
	for i, m := range models {
		if i < len(registrations) && registrations[i] != nil {
			*registrations[i] = *pool.NewPoolRegistrationFromModel(m)
		}
	}
	return nil
}

// MarkUsersTicketsAsWhitelist 批量将多个用户的签码标记为白名单签
func (a PoolRepository) MarkUsersTicketsAsWhitelist(ctx context.Context, poolID string, userIDs []string) error {
	d := a.db.Get(ctx)

	if len(userIDs) == 0 {
		return nil
	}

	_, err := d.DrawPoolTicket.WithContext(ctx).
		Where(d.DrawPoolTicket.PoolID.Eq(poolID)).
		Where(d.DrawPoolTicket.UserID.In(userIDs...)).
		Update(d.DrawPoolTicket.IsWhitelistTicket, true)

	if err != nil {
		zap.L().Error("MarkUsersTicketsAsWhitelist failed", zap.Error(err), zap.String("poolID", poolID), zap.Any("userIDs", userIDs))
		return err
	}
	return nil
}

func (a PoolRepository) GetRegistrationsTicketCountByUserId(ctx context.Context, poolID string, userID string) (*inframodel.UserTicket, error) {
	d := a.db.Get(ctx)

	var result inframodel.UserTicket

	// Use raw SQL string instead of gorm.Expr to avoid parsing issues
	winnerCountSQL := fmt.Sprintf("SUM(CASE WHEN %s = true THEN 1 ELSE 0 END) AS %s",
		d.DrawPoolTicket.IsWinner.ColumnName(),
		inframodel.EmptyUserTicket.WinnerCountFiled())

	err := d.DrawPoolTicket.WithContext(ctx).UnderlyingDB().
		Select(
			d.DrawPoolTicket.UserID.As(inframodel.EmptyUserTicket.UserIDName()),
			d.DrawPoolTicket.ID.Count().As(inframodel.EmptyUserTicket.CountName()),
			winnerCountSQL).
		Where(d.DrawPoolTicket.PoolID.Eq(poolID)).
		Where(d.DrawPoolTicket.UserID.Eq(userID)).
		Group(d.DrawPoolTicket.UserID.ColumnName().String()).
		Scan(&result).Error
	if err != nil {
		zap.L().Error("GetRegistrationsTicketCountByUserId failed", zap.Error(err), zap.String("poolID", poolID), zap.Any("userID", userID))
		return nil, err
	}

	return &result, nil
}

func (a PoolRepository) GetPoolJobByStatus(ctx context.Context, status []pool.TaskStatus) ([]*pool.Pool, error) {
	d := a.db.Get(ctx)

	s := make([]int16, 0, len(status))
	for _, v := range status {
		s = append(s, v.Int16())
	}

	pools, err := d.DrawPool.WithContext(ctx).
		Clauses(clause.Locking{
			Strength: clause.LockingStrengthUpdate,
			Options:  clause.LockingOptionsSkipLocked,
		}).Where(d.DrawPool.Status.Eq(pool.PoolStatusPublished.Int16())).
		Where(d.DrawPool.Status.In(s...)).
		Find()

	if err != nil {
		zap.L().Error("GetPoolJobByStatus failed", zap.Error(err), zap.Any("status", status))
		return nil, server.InternalStatus
	}

	results := make([]*pool.Pool, 0, len(pools))
	for _, p := range pools {
		results = append(results, pool.NewPoolFromModel(p))
	}

	return results, nil
}

func (a PoolRepository) GetPoolRegistrationsCountByPoolId(ctx context.Context, poolID string) (int64, error) {

	d := a.db.Get(ctx)

	count, err := d.DrawPoolRegistration.WithContext(ctx).
		Where(d.DrawPoolRegistration.PoolID.Eq(poolID)).
		Count()
	if err != nil {
		zap.L().Error("GetPoolRegistrationsCountByPoolId failed", zap.Error(err), zap.String("poolID", poolID))
		return 0, server.InternalStatus
	}
	return count, nil

}

func (a PoolRepository) GetPoolRegistrationsByPoolId(ctx context.Context, poolID string, page int, size int) (int64, []string, error) {
	d := a.db.Get(ctx)

	type result struct {
		UserID string `gorm:"column:user_id"`
	}

	var r []*result

	count, err := d.DrawPoolRegistration.WithContext(ctx).
		Select(d.DrawPoolRegistration.UserID).
		Where(d.DrawPoolRegistration.PoolID.Eq(poolID)).
		ScanByPage(&r, (page-1)*size, size)

	if err != nil {
		zap.L().Error("GetPoolRegistrationsByPoolId failed", zap.Error(err), zap.String("poolID", poolID), zap.Int("page", page), zap.Int("size", size))
		return 0, nil, server.InternalStatus
	}

	var userIDs []string
	for _, item := range r {
		userIDs = append(userIDs, item.UserID)
	}

	return count, userIDs, nil
}

func (a PoolRepository) CreatRetryTask(ctx context.Context, message *pool.PoolRetryableMessage) error {
	d := a.db.Get(ctx)

	m := message.ToModel()
	err := d.DrawPoolRetryableMessage.WithContext(ctx).
		Create(m)

	if err != nil {
		zap.L().Error("failed to create retry task", zap.Error(err))
		return err
	}
	*message = *pool.NewPoolRetryableMessageFromModel(m)
	return nil
}

func (a PoolRepository) StreamNonWhitelistTickets(ctx context.Context, poolID, cursor string, limit int) ([]pool.Ticket, error) {
	d := a.db.Get(ctx)

	tickets, err := d.DrawPoolTicket.WithContext(ctx).
		Where(d.DrawPoolTicket.PoolID.Eq(poolID)).
		Where(d.DrawPoolTicket.ID.Gt(cursor)).
		Where(d.DrawPoolTicket.IsWhitelistTicket.Is(false)).
		Order(d.DrawPoolTicket.ID).
		Limit(limit).
		Find()

	if err != nil {
		zap.L().Error("StreamNonWhitelistTickets failed", zap.Error(err), zap.String("poolID", poolID), zap.String("cursor", cursor), zap.Int("limit", limit))
		return nil, err
	}

	var result []pool.Ticket
	for _, ticket := range tickets {
		result = append(result, pool.Ticket{
			ID:     ticket.ID,
			UserID: ticket.UserID,
		})
	}

	return result, nil

}

func (a PoolRepository) MarkUsersTicketsAsWinner(ctx context.Context, poolID string, winners []string) error {
	d := a.db.Get(ctx)

	if len(winners) == 0 {
		return nil
	}

	resultInfo, err := d.DrawPoolTicket.WithContext(ctx).
		Where(d.DrawPoolTicket.ID.In(winners...)).
		Where(d.DrawPoolTicket.PoolID.Eq(poolID)).
		Update(d.DrawPoolTicket.IsWinner, true)
	if err != nil {
		return err
	}

	if resultInfo.RowsAffected == 0 {
		zap.L().Info("MarkUsersTicketsAsWinner no changes made", zap.String("poolID", poolID), zap.Any("winners", winners))
		return status.Error(codes.NotFound, "no tickets found to mark as winner")
	}

	return nil

}

func (a PoolRepository) CreatePoolTickets(ctx context.Context, create []*model.DrawPoolTicket) error {
	d := a.db.Get(ctx)
	if len(create) == 0 {
		return nil
	}

	err := d.DrawPoolTicket.WithContext(ctx).Create(create...)
	if err != nil {
		zap.L().Error("failed to create pool tickets", zap.Error(err))
		return err
	}

	return nil
}

// GetWhitelistRegistrationsBatch 批量获取白名单报名信息
func (a PoolRepository) GetWhitelistRegistrationsBatch(ctx context.Context, poolID string, cursor string, size int, status pool.PoolRegistrationStatus) ([]*pool.PoolRegistration, error) {
	d := a.db.Get(ctx)

	registrations, err := d.DrawPoolRegistration.WithContext(ctx).
		Where(d.DrawPoolRegistration.PoolID.Eq(poolID)).
		Where(d.DrawPoolRegistration.ID.Gt(cursor)).
		Where(d.DrawPoolRegistration.IsWhitelistUser.Is(true)).
		Where(d.DrawPoolRegistration.LotteryStatus.Eq(status.Int16())).
		Order(d.DrawPoolRegistration.ID).
		Limit(size).
		Find()

	if err != nil {
		zap.L().Error("GetWhitelistRegistrationsBatch failed", zap.Error(err), zap.String("poolID", poolID), zap.String("cursor", cursor), zap.Int("size", size))
		return nil, err
	}

	var result []*pool.PoolRegistration
	for _, registration := range registrations {
		result = append(result, pool.NewPoolRegistrationFromModel(registration))
	}

	return result, nil
}

func (a PoolRepository) GetPoolTicketsByUserIds(ctx context.Context, poolID string, userIDs []string) ([]*pool.PoolTicket, error) {
	d := a.db.Get(ctx)

	tickets, err := d.DrawPoolTicket.WithContext(ctx).
		Where(d.DrawPoolTicket.PoolID.Eq(poolID)).
		Where(d.DrawPoolTicket.UserID.In(userIDs...)).
		Find()

	if err != nil {
		zap.L().Error("GetPoolTicketsByUserIds failed", zap.Error(err), zap.String("poolID", poolID), zap.Any("userIDs", userIDs))
		return nil, err
	}

	var result []*pool.PoolTicket
	for _, ticket := range tickets {
		result = append(result, pool.NewPoolTicketFromModel(ticket))
	}

	return result, nil
}

func (a PoolRepository) GetPoolTicketsCountByUserIds(ctx context.Context, poolID, cursor string, size1000 int) ([]*inframodel.UserTicket, error) {
	d := a.db.Get(ctx)

	var results []*inframodel.UserTicket
	err := d.DrawPoolTicket.WithContext(ctx).
		Select(d.DrawPoolTicket.UserID.As(inframodel.EmptyUserTicket.UserIDName()),
			d.DrawPoolTicket.ID.Count().As(inframodel.EmptyUserTicket.WinnerCountName())).
		Where(d.DrawPoolTicket.PoolID.Eq(poolID)).
		Where(d.DrawPoolTicket.IsWinner.Is(true)).
		Where(d.DrawPoolTicket.UserID.Gt(cursor)).
		Group(d.DrawPoolTicket.UserID).
		Order(d.DrawPoolTicket.UserID).
		Limit(size1000).
		Scan(&results)

	if err != nil {
		zap.L().Error("GetPoolTicketsCountByUserIds failed", zap.Error(err), zap.String("poolID", poolID), zap.String("cursor", cursor), zap.Int("size", size1000))
		return nil, err
	}

	return results, nil
}
