package divide

import (
	"context"
	"google.golang.org/protobuf/types/known/emptypb"

	dividepb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/divide"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/divide"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"github.com/shopspring/decimal"
)

type Application struct {
	divideRepo    divide.Repo
	orderRepo     order.Repo
	divideService *divide.Service
}

func NewApplication(divideRepo divide.Repo, orderRepo order.Repo, divideService *divide.Service) *Application {
	return &Application{divideRepo: divideRepo, orderRepo: orderRepo, divideService: divideService}
}

const (
	// ProjectID 金刚经项目id
	specialProjectID = "2f07d61d-b758-4a29-8418-1a1b06f22bf8"
	// specialFeeRate 金刚经特殊分账比例
	specialFeeRate = 0.06
	// normalFeeRate 普通分账比例
	normalFeeRate = 0.05
)

func (a *Application) GetDivideFeeByOrderIDs(ctx context.Context, req *dividepb.GetDivideFeeByTimeRPCReq) (*dividepb.GetDivideFeeByTimeRPCResp, error) {
	orders, err := a.orderRepo.GetOrdersByTime(ctx, req.StartTime, req.EndTime)
	if err != nil {
		return nil, err
	}

	resp := &dividepb.GetDivideFeeByTimeRPCResp{
		DivideFees: make([]*dividepb.GetDivideFeeByTimeRPCResp_DivideFee, 0, len(orders)),
	}
	var orderIDs []string
	orderUserMap := make(map[string]string)
	for _, order := range orders {
		orderIDs = append(orderIDs, order.ID)
		orderUserMap[order.ID] = order.UserID()
	}

	secondaryOrders, err := a.orderRepo.GetSecondaryOrdersByOrderIDs(ctx, orderIDs)
	if err != nil {
		return nil, err
	}

	normalFeeRateFactor := decimal.NewFromFloat(normalFeeRate)
	specialFeeRateFactor := decimal.NewFromFloat(specialFeeRate)

	for _, order := range secondaryOrders {
		orderAmount := order.Price
		var feeAmount decimal.Decimal

		// 针对金刚经项目修改分账比例，百分之6
		if order.ProjectID == specialProjectID {
			feeAmount = orderAmount.Mul(specialFeeRateFactor)
		} else {
			feeAmount = orderAmount.Mul(normalFeeRateFactor)
		}

		resp.DivideFees = append(resp.DivideFees, &dividepb.GetDivideFeeByTimeRPCResp_DivideFee{
			UserID:  order.UserID,
			OrderID: order.OrderID,
			Fee:     feeAmount.String(),
			NftID:   order.NftID,
		})
	}

	return resp, nil
}

func (a *Application) DivideSecondaryMarketOrder(ctx context.Context, req *dividepb.DivideSecondaryMarketOrderRPCReq) (*emptypb.Empty, error) {
	o, err := a.orderRepo.GetOrderByID(ctx, req.OrderID)
	if err != nil {
		return nil, err
	}
	_, err = a.divideService.DivideSecondaryMarketOrder(ctx, o)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
