package application

import (
	"cnb.cool/cymirror/ces-services/payment-service/internal/application/cron"
	"cnb.cool/cymirror/ces-services/payment-service/internal/application/divide"
	"cnb.cool/cymirror/ces-services/payment-service/internal/application/notify"
	"cnb.cool/cymirror/ces-services/payment-service/internal/application/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/application/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/application/wallet"
	"github.com/google/wire"
)

// AppProviderSet Application providers
var AppProviderSet = wire.NewSet(
	notify.NewApplication,
	order.NewApplication,
	pay.NewApplication,
	wallet.NewApplication,
	divide.NewApplication,
	cron.CronProviderSet,
)
