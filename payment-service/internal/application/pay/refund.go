package pay

import (
	paypb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/refund"
	"context"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"time"
)

// RefundRPC 根据订单退款，目前只支持易宝，退款必须先分账到商户下，再退款！！！，代码没有做兜底处理
func (a *Application) RefundRPC(ctx context.Context, req *paypb.RefundRPCReq) (*paypb.RefundRPCResp, error) {
	zap.L().Info("RefundRPC", zap.Any("req", req))

	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	refundAmount, err := decimal.NewFromString(req.RefundAmount)
	if err != nil {
		zap.L().Error("refund amount is invalid", zap.Error(err))
		return nil, status.Error(codes.InvalidArgument, "refund amount is invalid")
	}

	s, err := a.refundService.Refund(ctx, &refund.RefundCmd{
		OrderID:         req.OrderID,
		RefundRequestNo: req.RefundRequestNo,
		RefundAmount:    refundAmount,
		Remark:          req.Remark,
	})
	if err != nil {
		return nil, err
	}
	if s == refund.StatusFail {
		return nil, status.Error(codes.FailedPrecondition, "refund failed")
	}

	resp := &paypb.RefundRPCResp{
		Status: s.ToRefundPB(),
	}

	return resp, nil
}
