package pay

import (
	"cnb.cool/1024hub/kfpay-go-sdk"
	"cnb.cool/cymirror/ces-services/common/auth"
	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/common/server"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/query"
	paypb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/notify"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"context"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
	"strings"
	"time"
)

func (a *Application) KfpayBalancePayOrder(ctx context.Context, req *paypb.KfpayBalancePayOrderReq) (*emptypb.Empty, error) {
	// 验证用户是否登录
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 获取用户钱包
	kfWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	if kfWallet == nil || !kfWallet.IsOpen() {
		return nil, status.Error(codes.Unavailable, "wallet is not open")
	}

	if err = a.walletService.ValidateKfpayPassword(ctx, kfWallet, req.PayPassword); err != nil {
		return nil, err
	}

	// 获取订单
	o, err := a.orderRepo.GetOrderByIDWithUserID(ctx, req.OrderId, userID)
	if err != nil {
		return nil, err
	}
	// 检查订单是否可以支付
	err = o.CanPay()
	if err != nil {
		return nil, err
	}

	// 获取余额检查是否足够支付
	balanceResp, err := a.kfpayClient.QueryBalance(ctx, &kfpay.QueryBalanceCmd{
		SecMerchantID: *kfWallet.SecMerchantID(),
		OrderNo:       strings.ReplaceAll(uuid.New().String(), "-", "")[:30],
	})
	if err != nil {
		return nil, err
	}
	if balanceResp.Balance.LessThan(o.Price()) {
		return nil, status.Error(codes.FailedPrecondition, "balance is not enough")
	}

	tradeLog := wallet.NewKfpayTradeLog(wallet.KfpayTradeLogTradeTypePay, o.Price(),
		userID, o.Title(), "")

	// 创建支付订单
	err = db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
		err = a.walletRepo.CreateKfpayTradeLog(ctx, tradeLog)
		if err != nil {
			return err
		}

		payOrder := pay.NewKfpay(tradeLog.RequestNo(), userID, o.ID)
		err = a.payRepo.CreateKfpayPaymentInfo(ctx, payOrder)
		if err != nil {
			return err
		}

		err = o.PayOrder(order.PayTypeKfpayBalance, payOrder.ID)
		if err != nil {
			return err
		}
		err = a.orderRepo.UpdateOrder(ctx, o)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	cmd := &kfpay.TransferCmd{
		SecMerchantId:     *kfWallet.SecMerchantID(),
		OrderNo:           tradeLog.RequestNo(),
		PayeeMerchantId:   a.kfpayClient.SecMerchantID,
		PayeeMerchantName: a.kfpayClient.SecMerchantName,
		TradeName:         o.Title(),
		TradeTime:         time.Now(),
		Amount:            o.Price(),
		Currency:          kfpay.CurrencyTypeCNY,
	}
	transferResult, err := a.kfpayClient.Transfer(ctx, cmd)
	if err != nil {
		return nil, err
	}
	zap.L().Info("KfpayBalancePayOrder transfer result", zap.Any("result", transferResult), zap.Any("cmd", cmd), zap.Any("context", ctx))

	if transferResult.Status == kfpay.StatusSuccess {
		tradeLog.SetStatus(wallet.KfpayTradeLogStatusSuccess)
		err := a.walletRepo.UpdateKfpayTradeLog(ctx, tradeLog)
		if err != nil {
			return nil, err
		}

		// 更新订单状态
		err = a.notifyService.ProcessPayNotifyCore(ctx, &notify.PayNotify{
			PaySuccessDate: time.Now(),
			OrderId:        o.ID,
			UniqueOrderNo:  tradeLog.RequestNo(),
		})
		if err != nil {
			return nil, err
		}
	}

	return &emptypb.Empty{}, nil
}

func (a *Application) KfpayBankCardPayOrderRequest(ctx context.Context, req *paypb.KfpayBankCardPayOrderRequestReq) (*paypb.KfpayBankCardPayOrderRequestResp, error) {
	// 验证用户是否登录
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 获取订单
	o, err := a.orderRepo.GetOrderByIDWithUserID(ctx, req.OrderId, userID)
	if err != nil {
		return nil, err
	}

	// 检查订单是否可以支付
	err = o.CanPay()
	if err != nil {
		return nil, err
	}

	card, err := a.walletRepo.GetKfpayUserBandCard(ctx, req.BankCardId)
	if err != nil {
		return nil, server.InternalStatus
	}
	if card == nil || card.UserID() != userID {
		return nil, status.Error(codes.InvalidArgument, "bank card not found")
	}

	kfpayWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	if !kfpayWallet.IsOpen() {
		return nil, status.Error(codes.Unavailable, "user not open kfpay wallet")
	}

	// 创建订单记录
	cardType := card.CardType().ToSDKBankCardType()
	cmd := &kfpay.SmsCollectCmd{
		SecMerchantId:            kfpayWallet.SecondaryMerchantID(),
		OrderNo:                  strings.ReplaceAll(uuid.New().String(), "-", "")[:30],
		TerminalIp:               "127.0.0.1", // TODO 确认是否需要传真实ip
		TradeName:                o.Title(),
		TradeTime:                time.Now(),
		Amount:                   o.Price(),
		Currency:                 kfpay.CurrencyTypeCNY,
		CustBankNo:               card.BankCode(),
		CustBankAccountNo:        card.BankCardNumber(),
		CustBindPhoneNo:          card.RegisteredMobileNumber(),
		CustName:                 kfpayWallet.RealName(),
		CustBankAcctType:         &kfpay.BankAcctTypePersonal,
		CustAccountCreditOrDebit: &cardType,
		CustCertificationType:    kfpay.CertificationTypeIDCard,
		CustID:                   kfpayWallet.Card(),            // 身份证
		CustPhone:                card.RegisteredMobileNumber(), // 预留手机号
		PayerMerchantId:          kfpayWallet.SecondaryMerchantID(),
	}

	if card.CardType() == wallet.CardTypeCredit {
		cmd.CustCardValidDate = card.CreditCardExpirationDate()
		cmd.CustCardCvv2 = card.CreditCardCvv()
	}

	// 创建支付订单
	err = db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
		payOrder := pay.NewKfpayWithBankCardID(cmd.OrderNo, userID, o.ID, req.BankCardId)
		err = a.payRepo.CreateKfpayPaymentInfo(ctx, payOrder)
		if err != nil {
			return err
		}

		//err = o.PayOrder(order.PayTypeKfpayBankCard, payOrder.ID)
		//if err != nil {
		//	return err
		//}
		err = a.orderRepo.UpdateOrder(ctx, o)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, server.InternalStatus
	}

	// 发起短信快捷支付申请
	scResp, err := a.kfpayClient.SmsCollect(ctx, cmd)
	if err != nil {
		zap.L().Error("sms collect failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	if scResp.Status != kfpay.StatusSuccess {
		zap.L().Error("sms collect failed", zap.String("status", scResp.Status.String()), zap.String("failure details", scResp.FailureDetails), zap.Any("cmd", cmd))
		return nil, server.InternalStatus
	}

	return &paypb.KfpayBankCardPayOrderRequestResp{}, nil
}

func (a *Application) KfpayBankCardPayOrderConfirm(ctx context.Context, req *paypb.KfpayBankCardPayOrderConfirmReq) (*paypb.KfpayBankCardPayOrderConfirmResp, error) {
	return nil, status.Error(codes.Unimplemented, "not implemented")
	// 验证用户是否登录
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 校验订单是否可以支付
	o, err := a.orderRepo.GetOrderByIDWithUserID(ctx, req.OrderId, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	err = o.CanPay()
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, "order can not pay")
	}

	kfpayWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	if !kfpayWallet.IsOpen() {
		return nil, status.Error(codes.Unavailable, "wallet is not open")
	}

	// 获取最新的子订单记录
	kfpayOrder, err := a.payRepo.GetLatestKfpayOrderByOrderID(ctx, req.OrderId)
	if err != nil {
		return nil, server.InternalStatus
	}
	if kfpayOrder == nil {
		return nil, status.Error(codes.NotFound, "kfpay order not found")
	}
	if kfpayOrder.KfpayUserBankCardID() == nil {
		return nil, status.Error(codes.NotFound, "kfpay user bank card id not found")
	}

	// 根据银行卡获取绑定卡号对应的手机号
	card, err := a.walletRepo.GetKfpayUserBandCard(ctx, kfpayOrder.ID)
	if err != nil {
		return nil, server.InternalStatus
	}

	// 确认支付
	cpcResp, err := a.kfpayClient.SmsPay(ctx, &kfpay.SmsPayCmd{
		SecMerchantId:   *kfpayWallet.SecMerchantID(),
		OrderNo:         kfpayOrder.RequestNo(),
		SmsCode:         req.SmsCode,
		ConfirmFlag:     kfpay.ConfirmFlagConfirm,
		CustBindPhoneNo: card.RegisteredMobileNumber(),
	})
	if err != nil {
		zap.L().Error("consume pay confirm failed",
			zap.Error(err),
			zap.String("orderNo", kfpayOrder.OrderID()),
		)
		return nil, err
	}
	if cpcResp.Status != kfpay.StatusSuccess {
		zap.L().Error("consume pay confirm failed",
			zap.String("orderNo", o.ID),
			zap.Int("http code", cpcResp.Code),
			zap.String("status", cpcResp.Status.String()),
			zap.String("failure details", cpcResp.FailureDetails),
		)
		return nil, status.Error(codes.Internal, "consume pay confirm failed")
	}

	// TODO T0转账

	return &paypb.KfpayBankCardPayOrderConfirmResp{}, nil
}
