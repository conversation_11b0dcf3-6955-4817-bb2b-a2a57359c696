package pay

import (
	kycpb "cnb.cool/cymirror/ces-services/account-service/gen/proto/ces/account/kyc"
	"cnb.cool/cymirror/ces-services/common/auth"
	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/query"
	paypb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"
	"context"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func (a *Application) YeepayQuickPayOrder(ctx context.Context, req *paypb.YeepayQuickPayOrderReq) (*paypb.YeepayQuickPayOrderResp, error) {
	// 验证用户是否登录
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 检查订单是否可以支付
	// 获取订单
	o, err := a.orderRepo.GetOrderByIDWithUserID(ctx, req.OrderID, userID)
	if err != nil {
		return nil, err
	}
	err = o.CanPay()
	if err != nil {
		return nil, err
	}

	// 获取身份证信息
	getIDCardResp, err := a.kycServiceClient.GetIDCardByUserIDRPC(ctx, &kycpb.GetIDCardByUserIDRPCReq{
		UserID: userID,
	})
	if err != nil {
		zap.L().Error("get id card by user id rpc failed", zap.Error(err))
		return nil, err
	}
	if !getIDCardResp.IsAuth {
		return nil, status.Error(codes.InvalidArgument, "real name authentication required")
	}

	// 易宝快捷支付
	payCmd := yeepay.QuickTradeOrderCmd{
		OrderID:     o.ID,
		OrderAmount: o.Price(),
		RedirectURL: req.RedirectURL,
		ExpiredTime: o.ExpiredTime(),
		GoodsName:   o.Title(),
		RealName:    getIDCardResp.Name,
		IDCardNo:    getIDCardResp.IdCard,
	}
	divideType := pay.NewYeepayDivideTypeFromOrderType(o.OrderType()) // 二级市场计算分账
	payCmd.IsNeedDivide = divideType.Equal(pay.DivideTypeDelaySettle)
	// 易宝快捷支付下单
	tradeOrder, err := a.yeepayClient.QuickTradeOrder(&payCmd)
	if err != nil {
		return nil, err
	}
	// 构建支付 URL
	payUrl, err := a.yeepayClient.BuildPayURL(&yeepay.BuildPayURLCmd{
		Token:  tradeOrder.Token,
		UserID: userID,
	})
	if err != nil {
		return nil, err
	}
	// 易宝支付订单
	payOrder := pay.NewYeepay(o, tradeOrder.UniqueOrderNo, payUrl, pay.TypeYeePayQuick, divideType)
	err = o.PayOrder(order.PayTypeYeePayQuick, payOrder.ID)
	if err != nil {
		return nil, err
	}

	err = db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
		err = a.orderRepo.UpdateOrder(ctx, o)
		if err != nil {
			return err
		}

		// 插入易宝支付订单
		err = a.payRepo.CreateYeepayPaymentInfo(ctx, payOrder)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	return &paypb.YeepayQuickPayOrderResp{
		PayURL: payUrl,
	}, nil
}

func (a *Application) YeepayWalletPayOrder(ctx context.Context, req *paypb.YeepayWalletPayOrderReq) (*paypb.YeepayWalletPayOrderResp, error) {
	// 验证用户是否登录
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 检查订单是否可以支付
	o, err := a.orderRepo.GetOrderByIDWithUserID(ctx, req.OrderID, userID)
	if err != nil {
		return nil, err
	}
	err = o.CanPay()
	if err != nil {
		return nil, err
	}

	// 检查是否开通易宝钱包
	yeepayWallet, err := a.walletRepo.GetYeePayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if yeepayWallet == nil || yeepayWallet.MemberID() == nil {
		return nil, status.Error(codes.InvalidArgument, "user not open yeepay wallet")
	}

	// 调用易宝支付
	payCmd := yeepay.WalletTradeOrderCmd{
		OrderID:      o.ID,
		OrderAmount:  o.Price(),
		RedirectURL:  req.RedirectURL,
		ExpiredTime:  o.ExpiredTime(),
		GoodsName:    o.Title(),
		RealName:     yeepayWallet.Name(),
		IDCardNo:     yeepayWallet.IDCard(),
		YeepayUserID: *yeepayWallet.MemberID(),
	}
	divideType := pay.NewYeepayDivideTypeFromOrderType(o.OrderType())
	payCmd.IsNeedDivide = divideType.Equal(pay.DivideTypeDelaySettle)
	// 调用易宝支付下单
	tradeOrder, err := a.yeepayClient.WalletTradeOrder(&payCmd)
	if err != nil {
		return nil, err
	}
	// 构建支付 URL
	payUrl, err := a.yeepayClient.BuildPayURL(&yeepay.BuildPayURLCmd{
		Token:    tradeOrder.Token,
		UserID:   userID,
		MemberID: *yeepayWallet.MemberID(),
	})
	if err != nil {
		return nil, err
	}
	// 易宝支付订单
	payOrder := pay.NewYeepay(o, tradeOrder.UniqueOrderNo, payUrl, pay.TypeYeePayWallet, divideType)
	err = o.PayOrder(order.PayTypeYeePayWallet, payOrder.ID)
	if err != nil {
		return nil, err
	}

	err = db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
		err = a.orderRepo.UpdateOrder(ctx, o)
		if err != nil {
			return err
		}

		// 插入易宝钱包支付订单
		err = a.payRepo.CreateYeepayPaymentInfo(ctx, payOrder)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	return &paypb.YeepayWalletPayOrderResp{
		PayURL: payUrl,
	}, nil
}
