package pay

import (
	"context"

	"cnb.cool/cymirror/ces-services/common/auth"

	"cnb.cool/1024hub/kfpay-go-sdk"
	kycpb "cnb.cool/cymirror/ces-services/account-service/gen/proto/ces/account/kyc"
	paypb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/divide"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/notify"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/refund"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"
	profilepb "cnb.cool/cymirror/ces-services/user-service/gen/proto/ces/user/profile"
	"github.com/bsm/redislock"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type Application struct {
	orderRepo        order.Repo
	kycServiceClient kycpb.KYCServiceClient
	yeepayClient     *yeepay.Client
	walletRepo       wallet.Repo
	payRepo          pay.Repo
	divideRepo       divide.Repo
	refundRepo       refund.Repo
	divideService    *divide.Service
	kfpayClient      *kfpay.Client
	profileClient    profilepb.ProfileServiceClient
	lock             *redislock.Client
	refundService    *refund.Service
	notifyService    *notify.Service
	walletService    *wallet.Service
}

func NewApplication(orderRepo order.Repo, kycServiceClient kycpb.KYCServiceClient, yeepayClient *yeepay.Client, walletRepo wallet.Repo, payRepo pay.Repo, divideRepo divide.Repo, refundRepo refund.Repo, divideService *divide.Service, kfpayClient *kfpay.Client, profileClient profilepb.ProfileServiceClient, lock *redislock.Client, refundService *refund.Service, notifyService *notify.Service, walletService *wallet.Service) *Application {
	return &Application{orderRepo: orderRepo, kycServiceClient: kycServiceClient, yeepayClient: yeepayClient, walletRepo: walletRepo, payRepo: payRepo, divideRepo: divideRepo, refundRepo: refundRepo, divideService: divideService, kfpayClient: kfpayClient, profileClient: profileClient, lock: lock, refundService: refundService, notifyService: notifyService, walletService: walletService}
}

func (a *Application) DivideRPC(ctx context.Context, req *paypb.DivideRPCReq) (*paypb.DivideRPCResp, error) {
	zap.L().Info("DivideRPC", zap.Any("req", req))

	o, err := a.orderRepo.GetOrderByID(ctx, req.OrderID)
	if err != nil {
		return nil, err
	}
	if !o.CanDivide() {
		zap.L().Error("order can not divide", zap.Any("order", o))
		return nil, status.Error(codes.InvalidArgument, "order can not divide")
	}

	// 获取用户钱包
	needDivideUserIDs := make([]string, 0, len(req.DivideDetails))
	for _, d := range req.DivideDetails {
		if d.MemberType == paypb.DivideMemberType_SELLER_SIDE {
			needDivideUserIDs = append(needDivideUserIDs, d.UserID)
		}
	}
	wallets, err := a.walletRepo.GetWalletsByUserIDsAndPayType(ctx, needDivideUserIDs, o.PayType())
	if err != nil {
		return nil, err
	}
	userID2WalletMap := lo.SliceToMap(wallets, func(item wallet.Wallet) (string, wallet.Wallet) {
		return item.UserID(), item
	})

	divides := make([]*divide.Divide, 0, len(req.DivideDetails))
	for _, d := range req.DivideDetails {
		amount, err := decimal.NewFromString(d.Amount)
		if err != nil {
			zap.L().Error("divide seller side amount is invalid", zap.Error(err), zap.String("amount", d.Amount))
			return nil, err
		}

		if d.MemberType == paypb.DivideMemberType_SELLER_SIDE {
			w, _ := userID2WalletMap[d.UserID]
			if !w.IsOpen() {
				zap.L().Error("divide seller side wallet is not open", zap.String("user_id", d.UserID))
				return nil, status.Error(codes.InvalidArgument, "wallet is not open")
			}
			divideRecord, err := divide.NewMemberSideDivide(req.DivideRequestNo, w, o, amount)
			if err != nil {
				zap.L().Error("divide seller side failed", zap.Error(err), zap.String("amount", d.Amount))
				return nil, err
			}
			divides = append(divides, divideRecord)

		} else if d.MemberType == paypb.DivideMemberType_PROJECT_SIDE {
			divideRecord, err := divide.NewProjectSideDivide(req.DivideRequestNo, req.OrderID, amount)
			if err != nil {
				zap.L().Error("divide project side failed", zap.Error(err), zap.String("amount", d.Amount))
				return nil, err
			}
			divides = append(divides, divideRecord)
		}
	}

	var s divide.Status
	switch o.PayType() {
	case order.PayTypeYeePayQuick, order.PayTypeYeePayWallet:
		s, err = a.divideService.YeepayDivide(ctx, divides)
		if err != nil {
			return nil, err
		}
	//case order.PayTypeKfpayBalance, order.PayTypeKfpayBankCard:
	case order.PayTypeKfpayBalance:
		s, err = a.divideService.KfpayDivide(ctx, divides, userID2WalletMap)
		if err != nil {
			return nil, err
		}
	default:
		zap.L().Error("invalid pay type", zap.Any("pay type", o.PayType()))
		return nil, status.Error(codes.Unimplemented, "not implement")
	}

	if s == divide.StatusFailed {
		return nil, status.Error(codes.FailedPrecondition, "divide failed")
	}

	resp := &paypb.DivideRPCResp{
		Status: s.ToDividePB(),
	}

	return resp, nil
}

func (a *Application) GetKfpayTradeLogs(ctx context.Context, req *paypb.GetKfpayTradeLogsReq) (*paypb.GetKfpayTradeLogsResp, error) {
	// 从鉴权上下文中获取用户ID
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 查询快付通交易日志（按创建时间降序）
	logs, total, err := a.payRepo.GetKfpayTradeLogs(ctx, userID, req.Page, 20, req.TradeType, req.FilterMonth)
	if err != nil {
		zap.L().Error("failed to get kfpay trade logs", zap.Error(err), zap.String("userID", userID))
		return nil, status.Error(codes.Internal, "failed to get trade logs")
	}

	// 转换为响应格式
	resp := &paypb.GetKfpayTradeLogsResp{
		Total: total,
		Logs:  make([]*paypb.GetKfpayTradeLogsResp_KfpayTradeLog, 0, len(logs)),
	}

	for _, log := range logs {
		resp.Logs = append(resp.Logs, &paypb.GetKfpayTradeLogsResp_KfpayTradeLog{
			TradeType: int32(log.TradeType().Int16()),
			Amount:    log.Amount().String(),
			Status:    int32(log.Status().Int16()),
			CreatedAt: log.CreatedAt().UnixMilli(),
		})
	}

	return resp, nil
}
