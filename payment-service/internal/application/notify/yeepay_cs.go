package notify

import (
	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/common/server"
	nftpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/nft"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/query"
	notifypb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/notify"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"context"
	"github.com/bytedance/sonic"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"
	"time"
)

// yeepayCsNotify 清算回调 https://open.yeepay.com/docs/apis/bzshsfk/options__rest__v1.0__aggpay__pre-pay#anchor7
type yeepayCsNotify struct {
	OrderAmount   decimal.Decimal `json:"orderAmount"`   // 订单金额
	OrderId       string          `json:"orderId"`       // 订单号
	UniqueOrderNo string          `json:"uniqueOrderNo"` // 商户收款请求号对应在易宝的收款订单号
	Status        string          `json:"status"`        // 清算状态 SUCCESS（成功）
	CsSuccessDate string          `json:"csSuccessDate"` // 清算时间
	MerchantFee   decimal.Decimal `json:"merchantFee"`   // 商户手续费
	UnSplitAmount decimal.Decimal `json:"unSplitAmount"` // 剩余可分账金额
}

// ProcessYeepayCsNotify 清算回调
func (a *Application) ProcessYeepayCsNotify(ctx context.Context, req *notifypb.ProcessCsNotifyRPCReq) (*emptypb.Empty, error) {
	// 解密获取易宝钱包通知
	decrypt, err := a.yeepayClient.Decrypt(req.ReqBody)
	if err != nil {
		return nil, err
	}
	zap.L().Info("decrypt cs notify", zap.String("decrypt", decrypt))
	var notify yeepayCsNotify
	err = sonic.UnmarshalString(decrypt, &notify)
	if err != nil {
		zap.L().Error("unmarshal cs notify error", zap.Error(err), zap.String("notify_string", decrypt))
		return nil, server.InternalStatus
	}

	// 清算回调不成功
	if notify.Status != "SUCCESS" {
		zap.L().Warn("notify status is not success", zap.String("status", notify.Status), zap.String("notify_string", decrypt))
		return &emptypb.Empty{}, nil
	}

	o, err := a.orderRepo.GetOrderByID(ctx, notify.OrderId)
	if err != nil {
		return nil, err
	}

	if !o.Status().Equal(order.StatusSuccess) {
		zap.L().Error("order status not success", zap.String("order_id", notify.OrderId))
		return nil, server.InternalStatus
	}

	switch o.OrderType() {
	case order.TypePrimaryMarket:
		return &emptypb.Empty{}, nil
	case order.TypeSecondaryMarket:
		return a.processSecondaryMarketCsNotify(ctx, &notify, o)
	case order.TypePurchaseRequest:
		return a.processPurchaseRequestCsNotify(ctx, &notify, o)
	default:
		zap.L().Error("order type not support", zap.String("order_id", o.ID))
		return nil, server.InternalStatus
	}
}

func (a *Application) processSecondaryMarketCsNotify(ctx context.Context, notify *yeepayCsNotify, o *order.Order) (*emptypb.Empty, error) {
	if err := a.makeYeepayOrderCsSuccessfully(ctx, notify, o); err != nil {
		return nil, err
	}

	transferResult, err := a.nftServiceClient.CheckIsNftTransferByOrderIDRPC(ctx, &nftpb.CheckIsNftTransferByOrderIDRPCReq{
		OrderID: o.ID,
	})
	if err != nil {
		zap.L().Error("failed to check nft transfer", zap.Error(err), zap.String("order_id", o.ID))
		return nil, server.InternalStatus
	}
	if !transferResult.IsLogExist {
		zap.L().Warn("nft transfer not exist", zap.String("order_id", o.ID))
		return nil, server.InternalStatus
	}

	_, err = a.divideService.DivideSecondaryMarketOrder(ctx, o)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (a *Application) processPurchaseRequestCsNotify(ctx context.Context, notify *yeepayCsNotify, o *order.Order) (*emptypb.Empty, error) {
	if err := a.makeYeepayOrderCsSuccessfully(ctx, notify, o); err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (a *Application) makeYeepayOrderCsSuccessfully(ctx context.Context, notify *yeepayCsNotify, o *order.Order) error {
	err := db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
		// 获取订单
		yeepayOrder, err := a.payRepo.GetYeepayOrderByOrderID(ctx, notify.OrderId)
		if yeepayOrder == nil {
			zap.L().Error("get yeepay order by order id failed", zap.String("order_id", notify.OrderId))
			return server.InternalStatus
		}
		if err != nil {
			return err
		}

		csAt, err := time.ParseInLocation(time.DateTime, notify.CsSuccessDate, time.Local)
		if err != nil {
			zap.L().Error("parse cs success date failed", zap.Error(err))
			return err
		}
		yeepayOrder.SetCsAt(csAt)
		yeepayOrder.SetUnSplitAmount(notify.UnSplitAmount)
		yeepayOrder.SetMerchantFee(notify.MerchantFee)

		// 更新状态
		err = a.payRepo.UpdateYeepayOrder(ctx, yeepayOrder)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
