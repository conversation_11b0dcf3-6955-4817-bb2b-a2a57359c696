package notify

import (
	"cnb.cool/cymirror/ces-services/common/server"
	notifypb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/notify"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"context"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"
)

// yeepayWalletNotify 易宝钱包通知 https://open.yeepay.com/docs/apis/docking-wallet/post__rest__v2.0__m-wallet__wallet__index
type yeepayWalletNotify struct {
	BusinessNo     string `json:"businessNo"`     // 易宝唯一订单号
	MerchantUserNo string `json:"merchantUserNo"` // 商户用户标识 用户在商户侧的用户id 示例值：User89849
	WalletUserNo   string `json:"walletUserNo"`   // 易宝钱包用户标识 用户在易宝钱包侧的用户id 示例值：User89849
}

func (a *Application) ProcessYeepayWalletNotify(ctx context.Context, req *notifypb.ProcessWalletNotifyRPCReq) (*emptypb.Empty, error) {
	decrypt, err := a.yeepayClient.Decrypt(req.ReqBody)
	if err != nil {
		return nil, err
	}

	zap.L().Info("decrypt wallet notify", zap.String("decrypt", decrypt))

	var notify yeepayWalletNotify
	err = sonic.UnmarshalString(decrypt, &notify)
	if err != nil {
		zap.L().Error("unmarshal wallet notify error", zap.Error(err), zap.String("notify_string", decrypt))
		return nil, server.InternalStatus
	}

	w := wallet.NewYeePayWallet(notify.BusinessNo, notify.MerchantUserNo, notify.WalletUserNo)
	err = a.walletRepo.ApplyWalletCallback(ctx, w)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
