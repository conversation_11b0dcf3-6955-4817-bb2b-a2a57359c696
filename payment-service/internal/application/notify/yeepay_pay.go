package notify

import (
	"context"
	"time"

	"cnb.cool/cymirror/ces-services/common/server"
	notifypb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/notify"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/notify"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"
)

// yeepayPayNotify 易宝支付通知 https://open.yeepay.com/docs/apis/bzshsfk/options__rest__v1.0__aggpay__pre-pay
type yeepayPayNotify struct {
	PaySuccessDate string `json:"paySuccessDate"` // 支付成功时间
	OrderId        string `json:"orderId"`        // 交易下单传入的商户收款请求号
	UniqueOrderNo  string `json:"uniqueOrderNo"`  // 易宝订单号
	Status         string `json:"status"`         // 支付状态 SUCCESS（订单支付成功）
}

func (a *Application) ProcessPayNotify(ctx context.Context, req *notifypb.ProcessPayNotifyRPCReq) (*emptypb.Empty, error) {
	// 解密获取易宝钱包通知
	decrypt, err := a.yeepayClient.Decrypt(req.ReqBody)
	if err != nil {
		return nil, err
	}

	zap.L().Info("decrypt pay notify", zap.String("decrypt", decrypt))

	var yeepayNotify yeepayPayNotify
	err = sonic.UnmarshalString(decrypt, &yeepayNotify)
	if err != nil {
		zap.L().Error("unmarshal pay notify error", zap.Error(err), zap.String("notify_string", decrypt))
		return nil, server.InternalStatus
	}

	// 支付状态不是成功
	if yeepayNotify.Status != "SUCCESS" {
		zap.L().Warn("pay notify status is not success", zap.String("notify_string", decrypt))
		return &emptypb.Empty{}, nil
	}

	payTime, err := time.ParseInLocation(time.DateTime, yeepayNotify.PaySuccessDate, time.Local)
	if err != nil {
		zap.L().Error("parse pay success date failed", zap.Error(err))
		return nil, server.InternalStatus
	}

	// 转换为通用的支付通知结构体
	domainNotify := &notify.PayNotify{
		PaySuccessDate: payTime,
		OrderId:        yeepayNotify.OrderId,
		UniqueOrderNo:  yeepayNotify.UniqueOrderNo,
	}

	// 使用通知服务处理支付通知核心逻辑
	return nil, a.notifyService.ProcessPayNotifyCore(ctx, domainNotify)
}
