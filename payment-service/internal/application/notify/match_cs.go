package notify

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	matchpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/match"
	notifypb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/notify"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
)

// NftMatchedTradeLogs 撮合引擎回调时传递的交易记录
type NftMatchedTradeLogs struct {
	TradeId string `json:"trade_id"`
}

// ProcessMatchEngineNotify 处理匹配引擎回调
func (a *Application) ProcessMatchEngineNotify(ctx context.Context, req *notifypb.MatchEngineCsNotifyRPCReq) error {
	// 一次解析json，再进行aes解密
	type callbackRequest struct {
		Payload string `json:"payload"`
	}
	var unmarshalRequest callbackRequest
	err := json.Unmarshal([]byte(req.NftMatchedTradeLog), &unmarshalRequest)
	if err != nil {
		zap.L().Error("unmarshal match engine callback request error", zap.Error(err), zap.String("json_str", req.NftMatchedTradeLog))
		return err
	}

	// AES 解密
	decryptedPayload, err := a.notifyService.DecryptAES(unmarshalRequest.Payload)
	if err != nil {
		zap.L().Error("AES decrypt payload error", zap.Error(err), zap.String("encrypted_payload", req.NftMatchedTradeLog))
		return err
	}
	tradeNotify := new(NftMatchedTradeLogs)

	if err := json.Unmarshal([]byte(decryptedPayload), tradeNotify); err != nil {
		zap.L().Error("unmarshal nft matched trade log error", zap.Error(err), zap.String("nft_matched_trade_log", req.NftMatchedTradeLog))
		return err
	}
	zap.L().Info("receive match engine callback", zap.String("trade_id", tradeNotify.TradeId))
	// 创建撮合回调历史信息
	err = a.notifyRepo.CreateMatchCallbackHistory(ctx, tradeNotify.TradeId)
	if err != nil {
		zap.L().Error("create match callback history error", zap.Error(err), zap.String("trade_id", tradeNotify.TradeId))
		return err
	}
	// grpc获取matchTradeLog
	tradeLogResp, err := a.matchServiceClient.GetMatchedTradeLogRPC(ctx, &matchpb.GetMatchedTradeLogRPCReq{
		TradeId: tradeNotify.TradeId,
	})
	if err != nil || tradeLogResp == nil {
		zap.L().Error("get matched trade log error", zap.Error(err), zap.String("trade_id", tradeNotify.TradeId))
		return err
	}

	tradeLog := tradeLogResp.TradeLog
	takerMarketType := order.NewMarketType(int16(tradeLog.TakerMarketType))
	// 判断这笔交易的买家与卖家
	var sellerMatchOrderId, buyerMatchOrderId string
	var sellerUserId, buyerUserId string
	takerTradeSide := order.NewTradeSide(int16(tradeLog.TakerOrderType))
	switch takerTradeSide {
	case order.TradeSideBuy:
		buyerMatchOrderId = tradeLog.TakerOrderId
		buyerUserId = tradeLog.TakerUserId
		sellerMatchOrderId = tradeLog.MakerOrderId
		sellerUserId = tradeLog.MakerUserId
	case order.TradeSideSell:
		sellerMatchOrderId = tradeLog.TakerOrderId
		sellerUserId = tradeLog.TakerUserId
		buyerMatchOrderId = tradeLog.MakerOrderId
		buyerUserId = tradeLog.MakerUserId
	default:
		return fmt.Errorf("未知的交易操作类型: %d", tradeLog.TakerOrderType)
	}

	// grpc获取买卖双方的matchOrder
	mo, err := a.matchServiceClient.BatchGetMatchOrderRPC(ctx, &matchpb.BatchGetMatchOrderRPCReq{
		OrderId: []string{sellerMatchOrderId, buyerMatchOrderId},
	})
	if err != nil {
		zap.L().Error("get buyer order error", zap.Error(err), zap.String("seller_order_id", sellerMatchOrderId), zap.String("buyer_order_id", buyerMatchOrderId))
		return err
	}
	matchOrders := make(map[string]*matchpb.BatchGetMatchOrderRPCResp_MatchOrder)
	for _, o := range mo.Orders {
		matchOrders[o.OrderId] = o
	}

	// 获取买家支付订单(卖家撤单时为nil)
	var paymentOrder *order.Order
	// 卖家撤单时，不需要获取支付订单
	if !(takerMarketType.Equal(order.MarketTypeCancelled) && takerTradeSide.Equal(order.TradeSideSell)) {
		// grpc获取撮合买单对应的订单id
		paymentOrderResp, err := a.matchServiceClient.GetPaymentOrderIDByBuyMatchOrderIDRPC(ctx, &matchpb.GetPaymentOrderIDByBuyMatchOrderIDRPCReq{
			BuyOrderId: buyerMatchOrderId,
		})
		if err != nil {
			zap.L().Error("get payment order id by buy match order id error", zap.Error(err), zap.String("buyer_order_id", buyerMatchOrderId))
			return err
		}
		// 获取订单信息
		paymentOrder, err = a.orderRepo.GetOrderByIDWithUserID(ctx, paymentOrderResp.PaymentOrderId, buyerUserId)
		if paymentOrder == nil {
			zap.L().Error("payment order not found", zap.String("buyer_order_id", buyerMatchOrderId), zap.String("buyer_user_id", buyerUserId))
			return status.Error(codes.NotFound, "payment order not found")
		}
		if err != nil {
			zap.L().Error("get payment order by id with user id error", zap.Error(err), zap.String("payment_order_id", paymentOrderResp.PaymentOrderId), zap.String("buyer_user_id", buyerUserId))
			return err
		}
	} else {
		zap.L().Info("seller cancel order, payment order is nil", zap.String("trade_id", tradeLog.TradeId))
	}

	// 回调兜底消息，准备预填入分账和退款请求号
	matchCallbackMessage, err := a.notifyRepo.GetMatchCallbackMessageByTradeID(ctx, tradeLog.TradeId)
	if err != nil {
		zap.L().Error("get match callback message by trade id error", zap.Error(err), zap.String("trade_id", tradeLog.TradeId))
		return err
	}
	if matchCallbackMessage == nil {
		zap.L().Error("match callback message not found", zap.String("trade_id", tradeLog.TradeId))
		return status.Error(codes.NotFound, "match callback message not found")
	}
	// 退款金额
	var refund decimal.Decimal
	// actualPrice 实际成交的价格
	actualPrice, _ := decimal.NewFromString(tradeLogResp.TradeLog.Price)
	// pendingPrice 买家挂单价格
	pendingPrice, _ := decimal.NewFromString(matchOrders[buyerMatchOrderId].Price)

	switch {
	// 正常交易
	case order.MarketTypeLimit.Equal(takerMarketType) || order.MarketTypeMarket.Equal(takerMarketType):
		switch takerTradeSide {
		// 买家吃卖单时，向买家退还差价，检查两边订单状态
		case order.TradeSideBuy:
			// (挂单价-成交价)*数量
			refund = pendingPrice.Sub(actualPrice).Mul(decimal.NewFromInt(tradeLogResp.TradeLog.Amount))

		// 卖家吃买单时，享受更高的成交价，此时不需要退款，修改extra表状态，转移nft，检查两边订单状态
		case order.TradeSideSell:
			refund = decimal.Zero
		}
	// 撤单
	case takerMarketType.Equal(order.MarketTypeCancelled):
		switch takerTradeSide {
		// 买家撤单
		case order.TradeSideBuy:
			// 挂单价*数量(tradeLog会返回剩余未成交的数量)
			refund = pendingPrice.Mul(decimal.NewFromInt(tradeLogResp.TradeLog.Amount))
		// 卖家撤单
		case order.TradeSideSell:
			refund = decimal.Zero
		}
	default:
		return fmt.Errorf("未知的交易订单类型: %d", tradeLog.TakerMarketType)
	}

	// 1. 先转移NFT/处理撤单，并检查订单状态
	transferRPCReq := &matchpb.FlashSaleTransferNftRPCReq{
		TradeId:  tradeLog.TradeId,
		BuyerId:  buyerUserId,
		SellerId: sellerUserId,
	}
	if paymentOrder != nil {
		transferRPCReq.PaymentOrderId = paymentOrder.ID
	}
	_, err = a.matchServiceClient.FlashSaleTransferNftRPC(ctx, transferRPCReq)
	if err != nil {
		zap.L().Error("flash sale transfer nft error", zap.Error(err), zap.String("trade_id", tradeLog.TradeId))
		return err
	}
	zap.L().Info("flash sale transfer nft success", zap.String("trade_id", tradeLog.TradeId))
	// 2. 分账
	if order.MarketTypeLimit.Equal(takerMarketType) || order.MarketTypeMarket.Equal(takerMarketType) {
		if matchCallbackMessage.DivideReqNo() == nil || *matchCallbackMessage.DivideReqNo() == "" {
			matchCallbackMessage.SetDivideReqNo(strings.ReplaceAll(uuid.NewString(), "-", "")[:30])
			rowAffected, err := a.notifyRepo.UpdateMatchCallbackMessage(ctx, matchCallbackMessage)
			if err != nil {
				zap.L().Error("update match callback message error", zap.Error(err), zap.String("trade_id", tradeLog.TradeId))
				return err
			}
			if rowAffected == 0 {
				zap.L().Error("match callback message update failed", zap.String("trade_id", tradeLog.TradeId))
				return status.Error(codes.NotFound, "match callback message update failed")
			}
		}
		err = a.divideService.MatchEngineProcessDivideAndPay(ctx, buyerUserId, sellerUserId, tradeLog.TradeId, paymentOrder,
			actualPrice.Mul(decimal.NewFromInt(tradeLogResp.TradeLog.Amount)),
			*matchCallbackMessage.DivideReqNo())
		if err != nil {
			zap.L().Error("match engine process divide and pay error", zap.Error(err), zap.String("trade_id", tradeLog.TradeId))
			return err
		}
		zap.L().Info("match engine process divide and pay success", zap.String("trade_id", tradeLog.TradeId))
	}

	// 3. 退款
	if !refund.LessThanOrEqual(decimal.Zero) {
		if matchCallbackMessage.RefundReqNo() == nil || *matchCallbackMessage.RefundReqNo() == "" {
			matchCallbackMessage.SetRefundReqNo(strings.ReplaceAll(uuid.NewString(), "-", "")[:30])
			rowAffected, err := a.notifyRepo.UpdateMatchCallbackMessage(ctx, matchCallbackMessage)
			if err != nil {
				zap.L().Error("update match callback message error", zap.Error(err), zap.String("trade_id", tradeLog.TradeId))
				return err
			}
			if rowAffected == 0 {
				zap.L().Error("match callback message update failed", zap.String("trade_id", tradeLog.TradeId))
				return status.Error(codes.NotFound, "match callback message update failed")
			}
		}
		err = a.refundService.MatchEngineProcessRefund(ctx, paymentOrder, takerMarketType, refund, *matchCallbackMessage.RefundReqNo())
		if err != nil {
			zap.L().Error("match engine process refund error", zap.Error(err), zap.String("trade_id", tradeLog.TradeId))
			return err
		}
		zap.L().Info("match engine process refund success", zap.String("trade_id", tradeLog.TradeId))
	}

	// 标记交易记录为已处理
	_, err = a.notifyRepo.DeleteMatchCallbackMessageByTradeID(ctx, tradeLog.TradeId)
	if err != nil {
		return err
	}
	zap.L().Info("match engine callback success", zap.String("trade_id", tradeLog.TradeId))
	return nil
}
