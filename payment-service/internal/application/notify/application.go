package notify

import (
	"cnb.cool/1024hub/kfpay-go-sdk"
	matchpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/match"
	nftpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/nft"
	purchasereqpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/purchasereq"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/divide"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/notify"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/refund"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"
)

type Application struct {
	yeepayClient       *yeepay.Client
	walletRepo         wallet.Repo
	orderRepo          order.Repo
	nftServiceClient   nftpb.NFTServiceClient
	matchServiceClient matchpb.MatchServiceClient
	payRepo            pay.Repo
	divideRepo         divide.Repo
	kfpayClient        *kfpay.Client
	purchaseReqClient  purchasereqpb.PurchaseReqServiceClient
	orderService       *order.OrderService
	divideService      *divide.Service
	notifyService      *notify.Service
	refundService      *refund.Service
	notifyRepo         notify.Repo
}

func NewApplication(
	yeepayClient *yeepay.Client,
	walletRepo wallet.Repo,
	orderRepo order.Repo,
	nftServiceClient nftpb.NFTServiceClient,
	payRepo pay.Repo,
	divideRepo divide.Repo,
	purchaseReqClient purchasereqpb.PurchaseReqServiceClient,
	orderService *order.OrderService,
	divideService *divide.Service,
	notifyService *notify.Service,
	kfpayClient *kfpay.Client,
	matchServiceClient matchpb.MatchServiceClient,
	refundService *refund.Service,
	notifyRepo notify.Repo,
) *Application {
	return &Application{
		yeepayClient:       yeepayClient,
		walletRepo:         walletRepo,
		orderRepo:          orderRepo,
		nftServiceClient:   nftServiceClient,
		payRepo:            payRepo,
		divideRepo:         divideRepo,
		purchaseReqClient:  purchaseReqClient,
		orderService:       orderService,
		divideService:      divideService,
		notifyService:      notifyService,
		kfpayClient:        kfpayClient,
		matchServiceClient: matchServiceClient,
		refundService:      refundService,
		notifyRepo:         notifyRepo,
	}
}
