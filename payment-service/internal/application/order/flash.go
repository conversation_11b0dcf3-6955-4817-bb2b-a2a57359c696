package order

import (
	"context"

	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
)

func (a *Application) CreateFlashBuyOrderRPC(ctx context.Context, req *orderpb.CreateFlashBuyOrderRPCReq) (*orderpb.CreateFlashBuyOrderRPCResp, error) {
	flashOrder, err := order.NewOrderFromFlashBuyOrderRPCReq(req)
	if err != nil {
		return nil, err
	}
	err = a.orderRepo.CreateOrder(ctx, flashOrder)
	if err != nil {
		return nil, err
	}
	return &orderpb.CreateFlashBuyOrderRPCResp{}, nil
}
