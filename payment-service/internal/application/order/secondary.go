package order

import (
	"cnb.cool/cymirror/ces-services/common/server"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"context"
	"github.com/shopspring/decimal"
	"time"

	kycpb "cnb.cool/cymirror/ces-services/account-service/gen/proto/ces/account/kyc"
	"cnb.cool/cymirror/ces-services/common/auth"
	"cnb.cool/cymirror/ces-services/common/db"
	marketpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/market"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/query"
	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/market/secondary"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func (a *Application) BuySecondaryMarketNFT(ctx context.Context, req *orderpb.BuySecondaryMarketNFTReq) (*orderpb.BuySecondaryMarketNFTResp, error) {
	// 检查用户是否登录
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 锁单限制
	isLimit, err := a.orderService.CheckOrderIsLimit(ctx, userID)
	if err != nil {
		return nil, err
	}
	if isLimit {
		return nil, status.Error(codes.ResourceExhausted, "order limit")
	}

	// 检查是否已经实名
	idCard, err := a.kycClient.GetIDCardByUserIDRPC(ctx, &kycpb.GetIDCardByUserIDRPCReq{UserID: userID})
	if err != nil {
		return nil, err
	}
	if !idCard.IsAuth {
		return nil, status.Error(codes.Aborted, "user not auth")
	}

	// 检查是否可以购买
	listingResp, err := a.secondaryMarketClient.CanBuyListingRPC(ctx, &marketpb.CanBuyListingRPCReq{
		ListingID: req.ListingID,
	})
	if err != nil {
		return nil, err
	}
	listing := secondary.NewListingFromCanBuyListingRPC(listingResp)
	if !listing.CanBuy(userID) {
		return nil, status.Error(codes.InvalidArgument, "listing can not buy")
	}

	// 检查买家和卖家是否开通对应的钱包
	walletList, err := a.walletService.BatchGetUserWalletStatus(ctx, []string{userID, listing.SellerID()})
	if err != nil {
		return nil, server.InternalStatus
	}
	if walletList == nil {
		return nil, status.Error(codes.Unavailable, "buyer or seller not open wallet")
	}

	buyerWalletStatus := &wallet.UserWalletStatus{}
	sellerWalletStatus := &wallet.UserWalletStatus{}
	for _, w := range walletList {
		if w.UserID() == userID {
			buyerWalletStatus = w
		} else if w.UserID() == listing.SellerID() {
			sellerWalletStatus = w
		}
	}

	switch req.PayType {
	case orderpb.PayType_YEEPAY_QUICK: // 快捷不需要验证买家钱包
		if !sellerWalletStatus.YeepayWalletIsOpen() {
			return nil, status.Error(codes.InvalidArgument, "seller not open yeepay wallet")
		}
	case orderpb.PayType_YEEPAY_WALLET:
		if !buyerWalletStatus.YeepayWalletIsOpen() || !sellerWalletStatus.YeepayWalletIsOpen() {
			return nil, status.Error(codes.InvalidArgument, "buyer or seller not open yeepay wallet")
		}
	case orderpb.PayType_KFPAY_BALANCE, orderpb.PayType_KFPAY_BANK_CARD:
		if !buyerWalletStatus.KfpayWalletIsOpen() || !sellerWalletStatus.KfpayWalletIsOpen() {
			return nil, status.Error(codes.InvalidArgument, "buyer or seller not open kfpay wallet")
		}
	}

	// 创建订单
	o := order.NewSecondaryMarket(userID, listing)
	err = a.orderRepo.CreateOrder(ctx, o.Order)
	if err != nil {
		return nil, err
	}

	// 远程 RPC 锁定库存
	resp, err := a.secondaryMarketClient.LockSecondaryMarketNFTRPC(ctx, &marketpb.LockSecondaryMarketNFTRPCReq{
		ListingIDs:     []string{listing.ID},
		OrderID:        o.ID,
		ExpireDuration: time.Minute.Milliseconds() * 3,
		UserID:         o.UserID(),
	})
	if err != nil {
		return nil, err
	}
	if len(resp.LockedNFTIDs) == 0 {
		return nil, status.Error(codes.InvalidArgument, "nft status is change")
	}

	// 填充二级市场订单额外信息
	o.SetSecondaryExtra(listing)
	o.SetStatus(order.StatusWaitPay)
	o.SetPayType(order.NewPayTypeFromOrderPB(req.PayType))

	// 插入更新订单额外信息
	err = db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
		err = a.orderRepo.CreateSecondaryMarketOrderExtra(ctx, o)
		if err != nil {
			return err
		}
		err = a.orderRepo.UpdateOrder(ctx, o.Order)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	// TODO: 数据一致性

	return &orderpb.BuySecondaryMarketNFTResp{
		OrderID: o.ID,
	}, nil
}

func (a *Application) GetSecondaryOrderTradingRPC(ctx context.Context, req *orderpb.GetSecondaryOrderTradingRPCReq) (*orderpb.GetSecondaryOrderTradingRPCResp, error) {
	startTime := req.StartTime
	endTime := req.EndTime

	if startTime > endTime {
		return nil, status.Error(codes.InvalidArgument, "invalid start time and end time")
	}

	rankMap, err := a.orderRepo.GetOrderRank(ctx, startTime, endTime, int(req.Limit))
	if err != nil {
		return nil, err
	}

	itemList := make([]*orderpb.GetSecondaryOrderTradingRPCResp_OrderItem, 0, len(rankMap))
	for k, v := range rankMap {
		itemList = append(itemList, &orderpb.GetSecondaryOrderTradingRPCResp_OrderItem{
			ProjectID: k,
			Volume:    v,
		})
	}

	return &orderpb.GetSecondaryOrderTradingRPCResp{
		Items: itemList,
	}, nil
}

func (a *Application) GetProjectSecondaryOrderListRPC(ctx context.Context, req *orderpb.GetProjectSecondaryOrderListRPCReq) (*orderpb.GetProjectSecondaryOrderListRPCResp, error) {
	sort := ""
	switch req.Sort {
	case orderpb.GetProjectSecondaryOrderListRPCReq_TRADE_TIME:
		sort = "created_at"
	case orderpb.GetProjectSecondaryOrderListRPCReq_PRICE:
		sort = "price"
	default:
		return nil, status.Error(codes.InvalidArgument, "invalid sort field")
	}

	list, total, err := a.orderRepo.GetSecondaryOrderByProjectIDWithStatus(ctx, req.Page, req.PageSize, sort, req.IsAsc, req.ProjectID, order.StatusSuccess)
	if err != nil {
		return nil, err
	}

	items := make([]*orderpb.GetProjectSecondaryOrderListRPCResp_OrderItem, 0, len(list))
	for _, l := range list {
		items = append(items, &orderpb.GetProjectSecondaryOrderListRPCResp_OrderItem{
			NftID:     l.NftID(),
			SellTime:  l.CreatedAt().Unix(),
			Price:     l.Price().String(),
			ListingID: l.ListingID(),
		})
	}

	return &orderpb.GetProjectSecondaryOrderListRPCResp{
		Items: items,
		Total: total,
	}, nil
}

func (a *Application) GetSecondaryOrderByNFTIDRPC(ctx context.Context, req *orderpb.GetSecondaryOrderByNFTIDRPCReq) (*orderpb.GetSecondaryOrderByNFTIDRPCResp, error) {
	sort := ""
	switch req.Sort {
	case orderpb.GetSecondaryOrderByNFTIDRPCReq_TRADE_TIME:
		sort = "created_at"
	case orderpb.GetSecondaryOrderByNFTIDRPCReq_PRICE:
		sort = "price"
	default:
		return nil, status.Error(codes.InvalidArgument, "invalid sort field")
	}

	// 根据 NFT ID 获取交易列表，同时过滤订单状态
	list, total, err := a.orderRepo.GetSecondaryOrderByNFTIDWithStatus(ctx, req.Page, req.PageSize, sort, req.IsAsc, req.NftID, order.StatusSuccess)
	if err != nil {
		return nil, err
	}

	items := make([]*orderpb.GetSecondaryOrderByNFTIDRPCResp_OrderItem, 0, len(list))
	for _, l := range list {
		items = append(items, &orderpb.GetSecondaryOrderByNFTIDRPCResp_OrderItem{
			NftID:    l.NftID(),
			SellTime: l.CreatedAt().Unix(),
			Price:    l.Price().String(),
			OrderID:  l.OrderID(),
		})
	}

	return &orderpb.GetSecondaryOrderByNFTIDRPCResp{
		Items: items,
		Total: total,
	}, nil
}

func (a *Application) GetAllTradeAmountsRPC(ctx context.Context) (*orderpb.GetAllTradeAmountsRPCResp, error) {
	totalAmount, err := a.orderRepo.GetAllTradeAmounts(ctx)
	if err != nil {
		return nil, err
	}
	if totalAmount.IsZero() {
		return &orderpb.GetAllTradeAmountsRPCResp{}, nil
	}
	return &orderpb.GetAllTradeAmountsRPCResp{TotalAmount: totalAmount.String()}, nil
}

func (a *Application) GetTradeAmountsRangeByTimeRPC(ctx context.Context, req *orderpb.GetTradeAmountsRangeByTimeRPCReq) (*orderpb.GetTradeAmountsRangeByTimeRPCResp, error) {
	start := time.Unix(req.StartTime, 0)
	end := time.Unix(req.EndTime, 0)
	totalAmount, err := a.orderRepo.GetTradeAmountsRangeByTime(ctx, start, end)
	if err != nil {
		return nil, err
	}
	if totalAmount.IsZero() {
		return &orderpb.GetTradeAmountsRangeByTimeRPCResp{}, nil
	}
	return &orderpb.GetTradeAmountsRangeByTimeRPCResp{TotalAmount: totalAmount.String()}, nil
}

func (a *Application) GetProjectTradeAmountsRPC(ctx context.Context, req *orderpb.GetProjectTradeAmountsRPCReq) (*orderpb.GetProjectTradeAmountsRPCResp, error) {
	amount, err := a.orderRepo.GetProjectTradeAmounts(ctx, req.ProjectID)
	if err != nil {
		return nil, err
	}
	if amount.IsZero() {
		return &orderpb.GetProjectTradeAmountsRPCResp{}, nil
	}
	return &orderpb.GetProjectTradeAmountsRPCResp{Amount: amount.String()}, nil
}

func (a *Application) GetProjectTradeAmountsRangeByTimeRPC(ctx context.Context, req *orderpb.GetProjectTradeAmountsRangeByTimeRPCReq) (*orderpb.GetProjectTradeAmountsRangeByTimeRPCResp, error) {
	start := time.Unix(req.StartTime, 0)
	end := time.Unix(req.EndTime, 0)
	amount, err := a.orderRepo.GetProjectTradeAmountsRangeByTime(ctx, req.ProjectID, start, end)
	if err != nil {
		return nil, err
	}
	if amount.IsZero() {
		return &orderpb.GetProjectTradeAmountsRangeByTimeRPCResp{}, nil
	}
	return &orderpb.GetProjectTradeAmountsRangeByTimeRPCResp{Amount: amount.String()}, nil
}

func (a *Application) GetProjectsTradeAmountsRangeByTimeRPC(ctx context.Context, req *orderpb.GetProjectsTradeAmountsRangeByTimeRPCReq) (*orderpb.GetProjectsTradeAmountsRangeByTimeRPCResp, error) {
	start := time.Unix(req.StartTime, 0)
	end := time.Unix(req.EndTime, 0)

	projectWithAmounts, err := a.orderRepo.GetProjectsTradeAmountsRangeByTime(ctx, req.ProjectIDs, start, end, req.IsAsc)
	if err != nil {
		return nil, err
	}

	// 将 projectWithAmounts 转为 map
	projectMap := make(map[string]decimal.Decimal)
	for _, p := range projectWithAmounts {
		if p.Amount.Valid {
			projectMap[p.ProjectID] = p.Amount.Decimal
		} else {
			projectMap[p.ProjectID] = decimal.Zero
		}
	}

	// 构建结果列表
	items := make([]*orderpb.GetProjectsTradeAmountsRangeByTimeRPCResp_ProjectItem, len(projectWithAmounts))
	for i, p := range projectWithAmounts {
		items[i] = &orderpb.GetProjectsTradeAmountsRangeByTimeRPCResp_ProjectItem{
			ProjectID: p.ProjectID,
		}
		if p.Amount.Valid {
			items[i].Amount = p.Amount.Decimal.String()
		}
	}
	for _, projectID := range req.ProjectIDs {
		_, exists := projectMap[projectID]
		if !exists {
			item := &orderpb.GetProjectsTradeAmountsRangeByTimeRPCResp_ProjectItem{
				ProjectID: projectID,
				//Amount:    decimal.Zero.String(),
			}
			if req.IsAsc {
				items = append([]*orderpb.GetProjectsTradeAmountsRangeByTimeRPCResp_ProjectItem{item}, items...)
			} else {
				items = append(items, item)
			}
		}

	}

	return &orderpb.GetProjectsTradeAmountsRangeByTimeRPCResp{
		Items: items,
	}, nil
}

func (a *Application) SearchSecondaryOrderRPC(ctx context.Context, req *orderpb.SearchSecondaryOrderRPCReq) (*orderpb.SearchSecondaryOrderRPCResp, error) {
	sort := ""
	switch req.SortField {
	case orderpb.SearchSecondaryOrderRPCReq_CREATE_TIME:
		sort = "created_at"
	case orderpb.SearchSecondaryOrderRPCReq_PRICE:
		sort = "price"
	default:
		return nil, status.Error(codes.InvalidArgument, "invalid sort field")
	}

	switch {
	// 通过orderID精确搜索
	case req.OrderID != nil:
		o, err := a.orderRepo.GetOrderByID(ctx, *req.OrderID)
		if err != nil {
			return nil, err
		}
		oExtras, err := a.orderRepo.GetSecondaryExtraByOrderID(ctx, *req.OrderID)
		if err != nil {
			return nil, err
		}
		return &orderpb.SearchSecondaryOrderRPCResp{
			Orders: []*orderpb.SearchSecondaryOrderRPCResp_SecondaryOrder{buildSearchSecondaryOrderRPCResp(o, oExtras)},
			Total:  1,
		}, nil
	// 通过OrderNo精确搜索
	case req.OrderNo != nil:
		o, err := a.orderRepo.GetOrderByOrderNo(ctx, *req.OrderNo)
		if err != nil {
			return nil, err
		}
		oExtras, err := a.orderRepo.GetSecondaryExtraByOrderID(ctx, o.ID)
		if err != nil {
			return nil, err
		}
		return &orderpb.SearchSecondaryOrderRPCResp{
			Orders: []*orderpb.SearchSecondaryOrderRPCResp_SecondaryOrder{buildSearchSecondaryOrderRPCResp(o, oExtras)},
			Total:  1,
		}, nil
	// 当根据nftID查询时，需要先查询extra表中对应的orderIDs，再去主表查询orders并回到extra表查到对应extra
	case req.NftID != nil:
		cris := convertSearchSecondaryOrderReqToSecondaryOrderCriteria(req, sort)
		orderIDs, total, err := a.orderRepo.GetUniqueOrderIDsByNftIDWithCriteria(ctx, *req.NftID, cris)
		if err != nil {
			return nil, err
		}
		ordersWithSecondaryExtra, err := a.orderService.GetOrdersWithSecondaryExtra(ctx, orderIDs)
		if err != nil {
			return nil, err
		}
		if total == 0 {
			return &orderpb.SearchSecondaryOrderRPCResp{
				Orders: nil,
				Total:  0,
			}, nil
		}
		res := make([]*orderpb.SearchSecondaryOrderRPCResp_SecondaryOrder, len(ordersWithSecondaryExtra))
		for i, orderWithSecondaryExtra := range ordersWithSecondaryExtra {
			res[i] = buildSearchSecondaryOrderRPCResp(orderWithSecondaryExtra.Order, orderWithSecondaryExtra.Extras)
		}
		return &orderpb.SearchSecondaryOrderRPCResp{
			Orders: res,
			Total:  total,
		}, nil
	// 通过UserID搜索且高级筛选
	default:
		cris := convertSearchSecondaryOrderReqToOrderCriteria(req, sort)
		orders, cnt, err := a.orderRepo.GetOrdersByCriteria(ctx, cris)
		if err != nil {
			return nil, err
		}
		if cnt == 0 {
			return &orderpb.SearchSecondaryOrderRPCResp{
				Orders: nil,
				Total:  0,
			}, nil
		}
		orderIDs := make([]string, len(orders))
		for i, o := range orders {
			orderIDs[i] = o.ID
		}
		// 获得 map[orderID][]*order.SecondaryExtra
		extraMap, err := a.orderService.GetSecondaryExtraMap(ctx, orderIDs)

		res := make([]*orderpb.SearchSecondaryOrderRPCResp_SecondaryOrder, len(orders))
		for i, o := range orders {
			extra, ok := extraMap[o.ID]
			if !ok {
				res[i] = buildSearchSecondaryOrderRPCResp(o, nil)
			}
			res[i] = buildSearchSecondaryOrderRPCResp(o, extra)
		}
		return &orderpb.SearchSecondaryOrderRPCResp{
			Orders: res,
			Total:  cnt,
		}, nil
	}
}

func (a *Application) GetOrdersByNftListingIDsRPC(ctx context.Context, req *orderpb.GetOrdersByNftListingIDsRPCReq) (*orderpb.GetOrdersByNftListingIDsRPCResp, error) {
	orderMap, err := a.orderRepo.GetOrdersByListingIDs(ctx, req.ListingIDs)
	if err != nil {
		return nil, err
	}
	res := make([]*orderpb.GetOrdersByNftListingIDsRPCResp_OrderItem, len(req.ListingIDs))
	for i, listingID := range req.ListingIDs {
		o, ok := orderMap[listingID]
		if !ok {
			res[i] = &orderpb.GetOrdersByNftListingIDsRPCResp_OrderItem{
				ListingID: listingID,
			}
			continue
		}
		orderNo := o.OrderNo()
		res[i] = &orderpb.GetOrdersByNftListingIDsRPCResp_OrderItem{
			ListingID: listingID,
			OrderID:   &o.ID,
			OrderNo:   &orderNo,
		}
	}
	return &orderpb.GetOrdersByNftListingIDsRPCResp{
		Items: res,
	}, nil
}
