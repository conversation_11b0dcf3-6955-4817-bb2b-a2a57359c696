package order

import (
	"context"
	"errors"
	"fmt"
	"time"

	kycpb "cnb.cool/cymirror/ces-services/account-service/gen/proto/ces/account/kyc"
	"cnb.cool/cymirror/ces-services/common/auth"
	"cnb.cool/cymirror/ces-services/common/db"
	marketpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/market"
	projectpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/project"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/query"
	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/project"
	whitelistpb "cnb.cool/cymirror/ces-services/whitelist-service/gen/proto/ces/whitelist/whitelist"
	"github.com/bsm/redislock"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var createPrimaryMarketOrderLockKey = "order:create:%s:%s"

func (a *Application) CreatePrimaryMarketOrder(ctx context.Context, req *orderpb.CreatePrimaryMarketOrderReq) (*orderpb.CreatePrimaryMarketOrderResp, error) {
	// if req.Count >= 100 || req.Count <= 0 {
	// 	return nil, status.Error(codes.InvalidArgument, "count invalid")
	// }

	// 检查用户是否登录
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}
	// 获取分布式锁，同时锁住用户ID和项目ID
	lockKey := fmt.Sprintf(createPrimaryMarketOrderLockKey, userID, req.ProjectID)
	lock, err := a.lock.Obtain(ctx, lockKey, time.Second*30, &redislock.Options{})
	if err != nil {
		if errors.Is(err, redislock.ErrNotObtained) {
			zap.L().Warn("failed to obtain lock",
				zap.String("userID", userID),
				zap.String("projectID", req.ProjectID))
			return nil, order.ErrLockNotObtained
		}
		zap.L().Error("obtain lock failed",
			zap.Error(err),
			zap.String("userID", userID),
			zap.String("projectID", req.ProjectID))
		return nil, status.Error(codes.ResourceExhausted, "lock not obtained")
	}
	defer func() {
		if err := lock.Release(ctx); err != nil {
			zap.L().Error("release lock failed",
				zap.Error(err),
				zap.String("userID", userID),
				zap.String("projectID", req.ProjectID))
		}
	}()
	// 锁单限制
	isLimit, err := a.orderService.CheckOrderIsLimit(ctx, userID)
	if err != nil {
		return nil, err
	}
	if isLimit {
		return nil, status.Error(codes.ResourceExhausted, "order limit")
	}

	// 检查是否已经实名
	idCard, err := a.kycClient.GetIDCardByUserIDRPC(ctx, &kycpb.GetIDCardByUserIDRPCReq{UserID: userID})
	if err != nil {
		return nil, err
	}
	if !idCard.IsAuth {
		return nil, status.Error(codes.Aborted, "user not auth")
	}

	// 检查 project 是否可以购买
	projectResp, err := a.projectClient.CanBuyProjectRPC(ctx, &projectpb.CanBuyProjectReq{
		Id: req.ProjectID,
	})
	if err != nil {
		return nil, err
	}
	p := project.NewProjectFromCanBuyProjectRPCResp(projectResp)
	if !p.CanBuy() {
		return nil, status.Error(codes.InvalidArgument, "project can not buy")
	}

	// 校验白名单资格
	whitelistResp, err := a.whitelistClient.CheckWhitelistQualificationRPC(ctx, &whitelistpb.CheckWhitelistQualificationRPCReq{
		UserID:     userID,
		RuleTypeID: req.ProjectID,
		Count:      int64(req.Count),
	})
	if err != nil {
		return nil, err
	}
	if !whitelistResp.HasQualification {
		return nil, status.Error(codes.Aborted, "user not whitelist or whitelist count not enough")
	}

	// 创建订单
	o := order.NewPrimaryMarket(userID, p, req.Count)
	err = a.orderRepo.CreateOrder(ctx, o.Order)
	if err != nil {
		return nil, err
	}

	// 远程 RPC 获取 NFT ids
	stock, err := a.primaryMarketClient.ReduceStockRPC(ctx, &marketpb.ReduceStockRPCReq{
		ProjectID:      req.ProjectID,
		Count:          req.Count,
		UserID:         userID,
		OrderID:        o.ID,
		ExpireDuration: 3 * time.Minute.Milliseconds(),
	})
	if err != nil {
		zap.L().Error("reduce stock rpc failed", zap.Error(err))
		return nil, err
	}
	o.SetPrimaryExtra(stock.NftIDs, p)
	o.SetStatus(order.StatusWaitPay)

	// 更新订单额外信息
	err = db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
		err = a.orderRepo.CreatePrimaryMarketOrderExtra(ctx, o)
		if err != nil {
			return err
		}
		err = a.orderRepo.UpdateOrder(ctx, o.Order)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	// TODO: 数据一致性

	return &orderpb.CreatePrimaryMarketOrderResp{
		OrderID: o.Order.ID,
	}, nil
}
