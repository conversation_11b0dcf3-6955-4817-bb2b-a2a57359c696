package order

import (
	"cnb.cool/1024hub/kfpay-go-sdk"
	"cnb.cool/cymirror/ces-services/common/auth"
	nftpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/nft"
	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/divide"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/refund"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"
	"context"
	"github.com/golang/protobuf/ptypes/empty"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"
	"strings"
)

func (a *Application) CancelOrder(ctx context.Context, req *orderpb.CancelOrderReq) (*emptypb.Empty, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	o, err := a.orderRepo.GetOrderByIDWithUserID(ctx, req.OrderID, userID)
	if err != nil {
		return nil, err
	}
	orderOldStatus := o.Status()

	// 取消订单
	err = o.Cancel()
	if err != nil {
		return nil, err
	}

	err = a.orderRepo.UpdateOrder(ctx, o)
	if err != nil {
		return nil, err
	}

	rpcReq := &nftpb.UnlockNFTRPCReq{
		MarketType: o.OrderType().ToNFTPB(),
		OrderID:    o.ID,
	}

	// 订单类型不同操作
	switch o.OrderType() {
	case order.TypePrimaryMarket, order.TypeSecondaryMarket:
		// 解锁nft
		_, err = a.nftClient.UnlockNFTRPC(ctx, rpcReq)
		if err != nil {
			zap.L().Error("unlock nft rpc failed", zap.Error(err), zap.String("order_id", o.ID))
		}
	}

	// 订单不是支付中 不处理
	if orderOldStatus != order.StatusPaying {
		return &empty.Empty{}, nil
	}

	// 反查退款
	var isNeedRefund bool
	switch o.PayType() {
	case order.PayTypeYeePayQuick, order.PayTypeYeePayWallet:
		info, err := a.yeepayClient.GetOrderInfo(&yeepay.QueryOrderCmd{
			OrderID: o.ID,
		})
		if err != nil {
			zap.L().Error("cancel order reverse lookup failed", zap.Error(err), zap.String("order_id", o.ID))
			return &emptypb.Empty{}, err
		}
		zap.L().Info("cancel order reverse lookup yeepay", zap.String("order_id", o.ID), zap.Any("info", info))
		if info.Status == yeepay.QueryOrderStatusSuccess {
			if o.OrderTypeNeedDivide() {
				// 先分账到自己项目方手上
				d, err := divide.NewProjectSideDivide(o.ID, o.ID, o.Price())
				if err != nil {
					return nil, err
				}
				_, err = a.divideService.YeepayDivide(ctx, []*divide.Divide{d})
				if err != nil {
					return nil, err
				}
			}
			isNeedRefund = true
		}
	case order.PayTypeKfpayBalance:
		kfpayOrder, err := a.payRepo.GetKfpayOrderByOrderID(ctx, o.ID)
		if err != nil {
			return nil, err
		}
		if kfpayOrder == nil {
			break
		}
		info, err := a.kfpayClient.QueryTradeRecord(ctx, &kfpay.QueryTradeRecordCmd{
			OriginalOrderNo: kfpayOrder.RequestNo(),
		})
		if err != nil {
			zap.L().Error("cancel order reverse lookup failed", zap.Error(err), zap.String("order_id", o.ID))
			return &emptypb.Empty{}, err
		}
		zap.L().Info("cancel order reverse lookup kfpay", zap.String("order_id", o.ID), zap.Any("info", info))
		if info.Status == kfpay.StatusSuccess {
			isNeedRefund = true
		}
	}
	if isNeedRefund {
		_, err = a.refundService.Refund(ctx, &refund.RefundCmd{
			OrderID:         o.ID,
			RefundAmount:    o.Price(),
			Remark:          "取消订单退款",
			RefundRequestNo: strings.ReplaceAll(o.ID, "-", "")[:30],
		})
		if err != nil {
			zap.L().Error("refund rpc failed", zap.Error(err), zap.String("order_id", o.ID))
		}
	}

	return &emptypb.Empty{}, nil
}
