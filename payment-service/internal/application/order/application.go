package order

import (
	"cnb.cool/1024hub/kfpay-go-sdk"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/refund"
	"context"
	"errors"
	"fmt"
	"time"

	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/divide"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/notify"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	kycpb "cnb.cool/cymirror/ces-services/account-service/gen/proto/ces/account/kyc"
	"cnb.cool/cymirror/ces-services/common/auth"
	marketpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/market"
	nftpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/nft"
	projectpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/project"
	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	profilepb "cnb.cool/cymirror/ces-services/user-service/gen/proto/ces/user/profile"
	whitelistpb "cnb.cool/cymirror/ces-services/whitelist-service/gen/proto/ces/whitelist/whitelist"
	"github.com/bsm/redislock"
)

var (
	// ReverseLookupOrderStatusAndNotifySuccessLockKey 反查订单状态并通知成功锁定 key: payment:order:reverseLookupOrderStatusAndNotifySuccess:{order_id}
	ReverseLookupOrderStatusAndNotifySuccessLockKey = "payment:order:reverseLookupOrderStatusAndNotifySuccess:%s"
)

type Application struct {
	projectClient         projectpb.ProjectServiceClient
	nftClient             nftpb.NFTServiceClient
	primaryMarketClient   marketpb.PrimaryMarketServiceClient
	secondaryMarketClient marketpb.SecondaryMarketServiceClient
	orderRepo             order.Repo
	kycClient             kycpb.KYCServiceClient
	orderService          *order.OrderService
	profileClient         profilepb.ProfileServiceClient
	whitelistClient       whitelistpb.WhitelistServiceClient
	lock                  *redislock.Client
	yeepayClient          *yeepay.Client
	payRepo               pay.Repo
	divideService         *divide.Service
	payService            *pay.Service
	notifyService         *notify.Service
	walletService         *wallet.Service
	kfpayClient           *kfpay.Client
	refundService         *refund.Service
}

func NewApplication(projectClient projectpb.ProjectServiceClient, nftClient nftpb.NFTServiceClient, primaryMarketClient marketpb.PrimaryMarketServiceClient, secondaryMarketClient marketpb.SecondaryMarketServiceClient, orderRepo order.Repo, kycClient kycpb.KYCServiceClient, orderService *order.OrderService, profileClient profilepb.ProfileServiceClient, whitelistClient whitelistpb.WhitelistServiceClient, lock *redislock.Client, yeepayClient *yeepay.Client, payRepo pay.Repo, divideService *divide.Service, payService *pay.Service, notifyService *notify.Service, walletService *wallet.Service, kfpayClient *kfpay.Client, refundService *refund.Service) *Application {
	return &Application{projectClient: projectClient, nftClient: nftClient, primaryMarketClient: primaryMarketClient, secondaryMarketClient: secondaryMarketClient, orderRepo: orderRepo, kycClient: kycClient, orderService: orderService, profileClient: profileClient, whitelistClient: whitelistClient, lock: lock, yeepayClient: yeepayClient, payRepo: payRepo, divideService: divideService, payService: payService, notifyService: notifyService, walletService: walletService, kfpayClient: kfpayClient, refundService: refundService}
}

func (a *Application) GetMyOrders(ctx context.Context, req *orderpb.GetMyOrdersReq) (*orderpb.GetMyOrdersResp, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	var page int64 = 1
	if req.Page != nil {
		page = *req.Page
	}
	var pageSize int64 = 10
	if req.PageSize != nil {
		pageSize = *req.PageSize
	}
	var orderType int64 = 0
	if req.OrderType != nil {
		orderType = *req.OrderType
	}
	switch orderType {
	case 0:
		return a.orderService.GetMyBoughtOrders(ctx, userID, page, pageSize)
	case 1:
		return a.orderService.GetMySoldOrders(ctx, userID, page, pageSize)
	}
	return nil, errors.New("invalid order type")
}

func (a *Application) GetOrderDetail(ctx context.Context, req *orderpb.GetOrderDetailReq) (*orderpb.GetOrderDetailResp, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}
	orderDetail, err := a.orderService.GetOrderDetail(ctx, req.OrderID, userID)
	if err != nil {
		return nil, err
	}
	return orderDetail, nil
}

func (a *Application) GetYeepayPayURL(ctx context.Context, req *orderpb.GetYeepayPayURLReq) (*orderpb.GetYeepayPayURLResp, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}
	payURL, err := a.orderRepo.GetYeepayPayURL(ctx, req.OrderID, userID)
	if err != nil {
		return nil, err
	}
	return &orderpb.GetYeepayPayURLResp{
		PayURL: payURL,
	}, nil
}

func (a *Application) GetOrderAmountByUserIDsRPC(ctx context.Context, req *orderpb.GetOrderAmountByUserIDsRPCReq) (*orderpb.GetOrderAmountByUserIDsRPCResp, error) {
	if req.Token != "$2a$10$lCp5Ty9B1nByHSKJLD8L1OQF68jjofaUZkhikAqUKSjDXF0tkoqGK" {
		return nil, errors.New("invalid token")
	}
	// 如果没有提供时间范围，设置默认值从2025年开始到现在
	startTime := req.StartTime
	endTime := req.EndTime
	if startTime == 0 {
		startTime = time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local).UnixMilli()
	}
	if endTime == 0 {
		endTime = time.Now().UnixMilli()
	}

	orderAmount, err := a.orderRepo.BatchGetOrderAmountByUserID(ctx, req.UserIDs, startTime, endTime)
	if err != nil {
		return nil, err
	}

	items := make([]*orderpb.GetOrderAmountByUserIDsRPCResp_UserItem, 0, len(orderAmount))
	for userID, amount := range orderAmount {
		items = append(items, &orderpb.GetOrderAmountByUserIDsRPCResp_UserItem{
			UserID: userID,
			Amount: amount.String(),
		})
	}
	return &orderpb.GetOrderAmountByUserIDsRPCResp{
		Items: items,
	}, nil
}

// 获取同一项目中用户已完成的订单数量
func (a *Application) GetUserCompletedOrderCountRPC(ctx context.Context, req *orderpb.GetUserCompletedOrderCountRPCReq) (*orderpb.GetUserCompletedOrderCountRPCResp, error) {
	count, err := a.orderService.GetUserCompletedOrderCount(ctx, req.UserID, req.ProjectID)
	if err != nil {
		return nil, err
	}

	return &orderpb.GetUserCompletedOrderCountRPCResp{
		Count: count,
	}, nil
}

func (a *Application) ReverseLookupOrderStatus(ctx context.Context, req *orderpb.ReverseLookupOrderStatusRPCReq) (*orderpb.ReverseLookupOrderStatusRPCResp, error) {
	// 获取订单
	o, err := a.orderRepo.GetOrderByID(ctx, req.OrderID)
	if err != nil {
		return nil, err
	}
	if o == nil {
		return nil, status.Error(codes.NotFound, "order not found")
	}

	// 反查并更新订单
	oldStatus := o.Status()
	err = a.notifyService.ReverseLookupAndUpdateOrderStatus(ctx, o)
	if err != nil {
		return nil, err
	}

	resp := &orderpb.ReverseLookupOrderStatusRPCResp{
		Finished:  o.Status().Equal(order.StatusSuccess),
		UserID:    o.UserID(),
		PayTime:   0,
		Amount:    o.Price().String(),
		OrderType: o.OrderType().ToOrderPB(),
		Extra:     nil,
	}
	if o.Status().Equal(order.StatusSuccess) && oldStatus != o.Status() {
		resp.PayTime = o.PayTime().UnixMilli()
		switch o.OrderType() {
		case order.TypePrimaryMarket:
			nftID2PrimaryExtID, err := a.orderService.GetMarketOrderNFTID2ExtraIDMap(ctx, o)
			if err != nil {
				return nil, err
			}
			resp.Extra = &orderpb.ReverseLookupOrderStatusRPCResp_PrimaryMarketExtra{
				PrimaryMarketExtra: &orderpb.ReverseLookupOrderStatusRPCResp_PrimaryMarket{
					NftID2PrimaryExtID: nftID2PrimaryExtID,
				},
			}
		case order.TypeSecondaryMarket:
			nftID2SecondaryExtIDMap, err := a.orderService.GetMarketOrderNFTID2ExtraIDMap(ctx, o)
			if err != nil {
				return nil, err
			}
			resp.Extra = &orderpb.ReverseLookupOrderStatusRPCResp_SecondaryMarketExtra{
				SecondaryMarketExtra: &orderpb.ReverseLookupOrderStatusRPCResp_SecondaryMarket{
					NftID2SecondaryExtID: nftID2SecondaryExtIDMap,
				},
			}
		}
	}

	return resp, nil
}

func (a *Application) GetOrderDetailByOrderIDsRPC(ctx context.Context, req *orderpb.GetOrderDetailByOrderIDsRPCReq) (*orderpb.GetOrderDetailByOrderIDsRPCResp, error) {
	orders, err := a.orderRepo.GetOrdersByIDs(ctx, req.OrderIDs)
	if err != nil {
		return nil, err
	}
	respOrders := make([]*orderpb.GetOrderDetailByOrderIDsRPCResp_Order, 0, len(orders))
	for _, order := range orders {
		respOrders = append(respOrders, &orderpb.GetOrderDetailByOrderIDsRPCResp_Order{
			ID:        order.ID,
			Status:    int32(order.Status().Int16()),
			OrderNo:   order.OrderNo(),
			UserID:    order.UserID(),
			Type:      order.OrderType().ToOrderPB(),
			Title:     order.Title(),
			CreatedAt: order.CreatedAt().UnixMilli(),
			UpdatedAt: order.UpdatedAt().UnixMilli(),
			PayType:   order.PayType().ToOrderPB(),
			ExpireAt:  order.ExpiredTime().UnixMilli(),
		})
	}
	return &orderpb.GetOrderDetailByOrderIDsRPCResp{
		Orders: respOrders,
	}, nil
}
func (a *Application) GetSecondaryOrdersByTimeRPC(ctx context.Context, req *orderpb.GetSecondaryOrdersByTimeRPCReq) (*orderpb.GetSecondaryOrdersByTimeRPCResp, error) {
	orders, err := a.orderRepo.GetOrdersByTime(ctx, req.StartTime, req.EndTime)
	if err != nil {
		return nil, err
	}
	var orderIDs []string
	for _, o := range orders {
		orderIDs = append(orderIDs, o.ID)
	}
	secondaryOrders, err := a.orderRepo.GetSecondaryOrderByOrderID(ctx, orderIDs)
	if err != nil {
		return nil, err
	}
	respOrders := make([]*orderpb.GetSecondaryOrdersByTimeRPCResp_SecondaryOrder, 0, len(secondaryOrders))
	for _, secondaryOrder := range secondaryOrders {
		respOrders = append(respOrders, &orderpb.GetSecondaryOrdersByTimeRPCResp_SecondaryOrder{
			OrderID:   secondaryOrder.OrderID(),
			ProjectID: secondaryOrder.ProjectID(),
			NftID:     secondaryOrder.NftID(),
			UserID:    secondaryOrder.UserID(),
			Price:     secondaryOrder.Price().String(),
			ListingID: secondaryOrder.ListingID(),
			ID:        secondaryOrder.ID,
			SellerID:  secondaryOrder.SellerID(),
		})
	}
	return &orderpb.GetSecondaryOrdersByTimeRPCResp{SecondaryOrders: respOrders}, nil
}

func (a *Application) ReverseLookupOrderStatusAndNotifySuccess(ctx context.Context, req *orderpb.ReverseLookupOrderStatusAndNotifySuccessRPCReq) (
	*orderpb.ReverseLookupOrderStatusAndNotifySuccessRPCResp, error) {
	// 加个锁减少并发
	lock, err := a.lock.Obtain(ctx, fmt.Sprintf(ReverseLookupOrderStatusAndNotifySuccessLockKey, req.OrderId), time.Minute, &redislock.Options{})
	if err != nil && !errors.Is(err, redislock.ErrNotObtained) {
		zap.L().Error("obtain lock failed", zap.Error(err))
		return nil, status.Error(codes.FailedPrecondition, "obtain lock failed")
	} else if errors.Is(err, redislock.ErrNotObtained) {
		return nil, status.Error(codes.Aborted, "obtain lock failed")
	}
	defer func() {
		err := lock.Release(context.Background())
		if err != nil {
			zap.L().Error("release lock failed", zap.Error(err))
		}
	}()

	// 获取订单
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 获取订单
	o, err := a.orderRepo.GetOrderByIDWithUserID(ctx, req.OrderId, userID)
	if err != nil {
		return nil, err
	}
	if o == nil {
		return nil, status.Error(codes.NotFound, "order not found")
	}
	if !o.Status().Equal(order.StatusPaying) {
		return &orderpb.ReverseLookupOrderStatusAndNotifySuccessRPCResp{Status: int32(o.Status().Int16())}, nil
	}

	// 反查并更新订单
	err = a.notifyService.ReverseLookupAndUpdateOrderStatus(ctx, o)
	if err != nil {
		return nil, err
	}

	if o.Status().Equal(order.StatusSuccess) {
		err = a.notifyService.PaySuccessPostProcess(ctx, o, &notify.PayNotify{
			PaySuccessDate: *o.PayTime(),
			OrderId:        o.ID,
			UniqueOrderNo:  "", // 没啥用
		})
		if err != nil {
			return &orderpb.ReverseLookupOrderStatusAndNotifySuccessRPCResp{Status: int32(o.Status().Int16())}, nil
		}
	}

	return &orderpb.ReverseLookupOrderStatusAndNotifySuccessRPCResp{Status: int32(o.Status().Int16())}, nil
}

func (a *Application) GetKfpayRequest(ctx context.Context, req *orderpb.GetKfpayRequestReq) (*orderpb.GetKfpayRequestResp, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 检查订单是否属于当前用户
	userOrder, err := a.orderRepo.GetOrderByID(ctx, req.GetOrderId())
	if err != nil {
		return nil, err
	}
	if userID != userOrder.UserID() {
		return nil, nil
	}

	// 根据订单ID获取快付通请求订单
	kfOrder, err := a.payRepo.GetKfpayOrderByOrderID(ctx, userOrder.ID)
	if err != nil {
		return nil, err
	}

	return &orderpb.GetKfpayRequestResp{
		RequestNo: kfOrder.RequestNo(),
	}, nil
}

func (a *Application) GetOrderDetailByUserIdRPC(ctx context.Context, req *orderpb.GetOrderDetailByUserIdRPCReq) (*orderpb.GetOrderDetailByUserIdRPCResp, error) {

	cris := convertOrderDetailByUserIdRPCReqToOrderCriteria(req)
	switch *cris.OrderType {
	case order.TypePrimaryMarket:
		return a.orderService.GetPrimaryByOrderCriteria(ctx, cris)
	case order.TypeSecondaryMarket:
		return a.orderService.GetSecondByOrderCriteria(ctx, cris)
	default:
		return nil, status.Error(codes.InvalidArgument, "invalid order type")
	}
}

func (a *Application) GetSecondOrderDetailByOrderIDsRPC(ctx context.Context, req *orderpb.GetSecondOrderDetailByOrderIDsRPCReq) (*orderpb.GetSecondOrderDetailByOrderIDsRPCResp, error) {
	orders, err := a.orderRepo.GetSecondaryExtraByOrderIDs(ctx, req.OrderIDs)
	if err != nil {
		return nil, err
	}
	respOrders := make([]*orderpb.GetSecondOrderDetailByOrderIDsRPCResp_SecondOrder, 0, len(orders))
	for _, order := range orders {
		respOrders = append(respOrders, &orderpb.GetSecondOrderDetailByOrderIDsRPCResp_SecondOrder{
			ID:        order.ID,
			OrderId:   order.OrderID(),
			ProjectId: order.ProjectID(),
			NftId:     order.NftID(),
			UserId:    order.UserID(),
			Price:     order.Price().String(),
			ListingId: order.ListingID(),
			CreatedAt: order.CreatedAt().UnixMilli(),
			UpdatedAt: order.UpdatedAt().UnixMilli(),
			Version:   order.Version(),
			SellerId:  order.SellerID(),
		})
	}

	return &orderpb.GetSecondOrderDetailByOrderIDsRPCResp{
		Orders: respOrders,
	}, nil
}
