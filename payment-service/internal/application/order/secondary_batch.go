package order

import (
	"context"
	"time"

	kycpb "cnb.cool/cymirror/ces-services/account-service/gen/proto/ces/account/kyc"
	"cnb.cool/cymirror/ces-services/common/auth"
	"cnb.cool/cymirror/ces-services/common/db"
	nftpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/nft"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/query"
	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/market/secondary"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func (a *Application) BatchBuySecondaryMarketNFT(ctx context.Context, req *orderpb.BatchBuySecondaryMarketNFTReq) (*orderpb.BatchBuySecondaryMarketNFTResp, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 锁单限制
	isLimit, err := a.orderService.CheckOrderIsLimit(ctx, userID)
	if err != nil {
		return nil, err
	}
	if isLimit {
		return nil, status.Error(codes.ResourceExhausted, "order limit")
	}

	// 检查是否已经实名
	idCard, err := a.kycClient.GetIDCardByUserIDRPC(ctx, &kycpb.GetIDCardByUserIDRPCReq{UserID: userID})
	if err != nil {
		return nil, err
	}
	if !idCard.IsAuth {
		return nil, status.Error(codes.Aborted, "user not auth")
	}

	// 创建订单
	o := order.NewBaseSecondaryMarket(userID)

	// 按照价格排序获取可以购买的二级市场在售NFT并锁定
	rpcReq := &nftpb.GetListingsOrderByPriceAndLockRPCReq{
		Count:          req.Count,
		ProjectID:      req.ProjectID,
		BuyerID:        userID,
		OrderID:        o.ID,
		ExpireDuration: 3 * time.Minute.Milliseconds(),
		PayType:        nftpb.PayType(req.PayType),
	}

	// 插入订单
	err = a.orderRepo.CreateOrder(ctx, o.Order)
	if err != nil {
		return nil, err
	}

	rpcResp, err := a.nftClient.GetListingsOrderByPriceAndLockRPC(ctx, rpcReq)
	if err != nil {
		return nil, err
	}
	if len(rpcResp.LockedListings) == 0 {
		return nil, status.Error(codes.InvalidArgument, "no listings can be bought")
	}
	listings := make([]*secondary.Listing, 0, len(rpcResp.LockedListings))
	for _, l := range rpcResp.LockedListings {
		listings = append(listings, secondary.NewListingFromGetListingsOrderByPriceAndLockRPC(l))
	}

	// 填充二级市场订单额外信息
	totalPrice := decimal.Zero
	for _, l := range listings {
		totalPrice = totalPrice.Add(l.Price())
	}
	o.SetOrderInfo(listings[0].Name(), listings[0].ImgURL())
	o.SetPrice(totalPrice)
	o.SetSecondaryExtras(listings)
	o.SetStatus(order.StatusWaitPay)
	o.SetPayType(order.NewPayTypeFromOrderPB(req.PayType))

	// 插入更新订单额外信息
	err = db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
		err = a.orderRepo.CreateSecondaryMarketOrderExtra(ctx, o)
		if err != nil {
			return err
		}
		err = a.orderRepo.UpdateOrder(ctx, o.Order)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	// TODO: 数据一致性

	nftIDs := make([]string, 0)
	for _, l := range listings {
		nftIDs = append(nftIDs, l.NftID())
	}
	// 8. 批量获取 Chain ID
	var chainIDMap map[string]string
	if len(nftIDs) > 0 {
		resp, err := a.nftClient.BatchGetChainIDByNFTIDRPC(ctx, &nftpb.BatchGetChainIDByNFTIDRPCReq{
			NftIDs: nftIDs,
		})
		if err != nil {
			zap.L().Error("failed to get chain ids", zap.Error(err), zap.Strings("nft_ids", nftIDs))
		} else if resp != nil {
			chainIDMap = resp.NftIDToChainID
		}
	}
	listingsResp := make([]*orderpb.BatchBuySecondaryMarketNFTResp_Listing, 0, len(listings))
	for _, l := range listings {
		chainID := ""
		if chainIDMap != nil {
			if id, ok := chainIDMap[l.NftID()]; ok {
				chainID = id
			}
		}
		listingsResp = append(listingsResp, &orderpb.BatchBuySecondaryMarketNFTResp_Listing{
			Price:   l.Price().String(),
			ImgURL:  l.ImgURL(),
			Name:    l.Name(),
			ChainID: chainID,
		})
	}

	return &orderpb.BatchBuySecondaryMarketNFTResp{
		OrderID:  o.ID,
		Listings: listingsResp,
	}, nil
}
