package order

import (
	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"github.com/shopspring/decimal"
	"time"
)

func buildSearchSecondaryOrderRPCResp(o *order.Order, oExtras []*order.SecondaryExtra) *orderpb.SearchSecondaryOrderRPCResp_SecondaryOrder {
	buildExtra := func(oExtras []*order.SecondaryExtra) []*orderpb.SearchSecondaryOrderRPCResp_SecondaryExtra {
		if oExtras == nil {
			return nil
		}
		extras := make([]*orderpb.SearchSecondaryOrderRPCResp_SecondaryExtra, len(oExtras))
		for i, oExtra := range oExtras {
			extras[i] = &orderpb.SearchSecondaryOrderRPCResp_SecondaryExtra{
				NftID:     oExtra.NftID(),
				ProjectID: oExtra.ProjectID(),
				ListingID: oExtra.ListingID(),
				SellerID:  oExtra.SellerID(),
			}
			if !oExtra.Price().IsZero() {
				extras[i].UnitPrice = oExtra.Price().String()
			}
		}
		return extras
	}
	respOrder := &orderpb.SearchSecondaryOrderRPCResp_SecondaryOrder{
		OrderID:     o.ID,
		UserID:      o.UserID(),
		Num:         int64(len(oExtras)),
		Nfts:        buildExtra(oExtras),
		Status:      int64(o.Status().Int16()),
		OrderNo:     o.OrderNo(),
		ImgURL:      o.ImgURL(),
		PayType:     int64(o.PayType().Int16()),
		CreateTime:  o.CreatedAt().Unix(),
		ExpiredTime: o.ExpiredTime().Unix(),
	}
	if o.PayTime() != nil {
		respOrder.PayTime = o.PayTime().Unix()
	}
	if !o.Price().IsZero() {
		respOrder.Price = o.Price().String()
	}
	return respOrder
}

func convertSearchSecondaryOrderReqToOrderCriteria(req *orderpb.SearchSecondaryOrderRPCReq, sort string) *order.OrderCriteria {
	cris := &order.OrderCriteria{
		UserID:    req.BuyerID,
		Page:      req.Page,
		PageSize:  req.PageSize,
		SortField: sort,
		IsAsc:     req.IsAsc,
	}
	if req.StartTime != nil {
		st := time.Unix(*req.StartTime, 0)
		cris.StartTime = &st
	}
	if req.EndTime != nil {
		et := time.Unix(*req.EndTime, 0)
		cris.EndTime = &et
	}
	if req.MinPrice != nil {
		mp, _ := decimal.NewFromString(*req.MinPrice)
		cris.MinPrice = &mp
	}
	if req.MaxPrice != nil {
		mp, _ := decimal.NewFromString(*req.MaxPrice)
		cris.MaxPrice = &mp
	}
	if req.Status != nil {
		st := order.NewStatus(int16(*req.Status))
		cris.Status = []order.Status{st}
	}
	if req.SortPayTypeField != nil {
		pt := order.NewPayType(int16(*req.SortPayTypeField))
		cris.PayType = &pt
	}
	if req.SortOrderTypeField != nil {
		ot := order.NewType(int16(*req.SortOrderTypeField))
		cris.OrderType = &ot
	}
	return cris
}

func convertSearchSecondaryOrderReqToSecondaryOrderCriteria(req *orderpb.SearchSecondaryOrderRPCReq, sort string) *order.SecondaryOrderCriteria {
	cris := &order.SecondaryOrderCriteria{
		UserID:    req.BuyerID,
		Page:      req.Page,
		PageSize:  req.PageSize,
		SortField: sort,
		IsAsc:     req.IsAsc,
	}
	if req.StartTime != nil {
		st := time.Unix(*req.StartTime, 0)
		cris.StartTime = &st
	}
	if req.EndTime != nil {
		et := time.Unix(*req.EndTime, 0)
		cris.EndTime = &et
	}
	if req.MinPrice != nil {
		mp, _ := decimal.NewFromString(*req.MinPrice)
		cris.MinPrice = &mp
	}
	if req.MaxPrice != nil {
		mp, _ := decimal.NewFromString(*req.MaxPrice)
		cris.MaxPrice = &mp
	}
	return cris
}

func convertOrderDetailByUserIdRPCReqToOrderCriteria(req *orderpb.GetOrderDetailByUserIdRPCReq) *order.OrderCriteria {
	cris := &order.OrderCriteria{

		UserID:     &req.UserId,
		Page:       req.Page,
		PageSize:   req.PageSize,
		Keyword:    req.Keyword,
		ProjectID:  req.ProjectId,
		RangeField: req.RangeField,
	}

	orderType := order.PBToType(req.OrderType)
	cris.OrderType = &orderType

	if req.StartTime != nil {
		st := time.Unix(*req.StartTime, 0)
		cris.StartTime = &st
	}

	if req.EndTime != nil {
		et := time.Unix(*req.EndTime, 0)
		cris.EndTime = &et
	}

	var status []order.Status
	for _, v := range req.Status {
		status = append(status, order.NewStatus(int16(v)))
	}
	cris.Status = status

	if req.SortField != nil {
		cris.SortField = *req.SortField
	}

	if req.IsAsc != nil {
		cris.IsAsc = *req.IsAsc
	}

	return cris

}
