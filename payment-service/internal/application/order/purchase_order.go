package order

import (
	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"context"
)

func (a *Application) CreatePurchaseRequestOrderRPC(ctx context.Context, req *orderpb.CreatePurchaseRequestOrderRPCReq) (*orderpb.CreatePurchaseRequestOrderRPCResp, error) {
	purchaseOrder, err := order.NewPurchaseRequest(req)
	if err != nil {
		return nil, err
	}
	err = a.orderRepo.CreateOrder(ctx, purchaseOrder.Order)
	if err != nil {
		return nil, err
	}
	return &orderpb.CreatePurchaseRequestOrderRPCResp{
		ExpiredTime: purchaseOrder.ExpiredTime().UnixMilli(),
	}, nil
}
