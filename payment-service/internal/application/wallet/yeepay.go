package wallet

import (
	"context"
	"github.com/samber/lo"
	"time"

	kycpb "cnb.cool/cymirror/ces-services/account-service/gen/proto/ces/account/kyc"
	"cnb.cool/cymirror/ces-services/common/auth"
	"cnb.cool/cymirror/ces-services/common/server"
	walletpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/wallet"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func (a *Application) GetYeePayWalletStatus(ctx context.Context) (*walletpb.YeePayWalletStatusResp, error) {
	// 验证用户是否登录
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 从数据库获取
	yeepayWallet, err := a.walletRepo.GetYeePayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if yeepayWallet == nil || yeepayWallet.MemberID() == nil {
		return &walletpb.YeePayWalletStatusResp{IsOpen: false}, nil
	}

	return &walletpb.YeePayWalletStatusResp{IsOpen: true}, nil
}

func (a *Application) GetYeePayWalletOpenUrl(ctx context.Context, req *walletpb.YeePayWalletOpenUrlReq) (*walletpb.YeePayWalletOpenUrlResp, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	//获取用户钱包,如果钱包存在且memberID存在,返回错误,否则返回openUrl
	yeepayWallet, err := a.walletRepo.GetYeePayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if yeepayWallet != nil && yeepayWallet.MemberID() != nil {
		return nil, status.Error(codes.AlreadyExists, "wallet is exists")
	}

	// 调用远程rpc获取用户实名信息
	userIDCard, err := a.kycClient.GetIDCardByUserIDRPC(ctx, &kycpb.GetIDCardByUserIDRPCReq{UserID: userID})
	if err != nil {
		return nil, err
	}

	if !userIDCard.IsAuth {
		return nil, status.Error(codes.InvalidArgument, "user not auth")
	}

	// 没有映射关系
	if yeepayWallet == nil {
		yeepayWallet = wallet.NewEmptyYeePayWallet(
			userID,
			userIDCard.Name,
			userIDCard.IdCard,
		)
		//插入数据库
		err = a.walletRepo.InsertYeePayWallet(ctx, yeepayWallet)
		if err != nil {
			return nil, err
		}

	}

	yeePayWallet := yeepay.WalletRegisterLoginCmd{
		UserID:        yeepayWallet.ExternalUserID(),
		Name:          yeepayWallet.Name(),
		CertificateNo: yeepayWallet.IDCard(),
		RedirectURL:   req.RedirectUrl,
		RequestNo:     uuid.New().String(),
	}

	login, err := a.yeepayClient.WalletRegisterLogin(ctx, &yeePayWallet)
	if err != nil {
		return nil, err
	}

	if login.Url == "" {
		zap.L().Error("yeepay login url is empty", zap.String("userID", yeepayWallet.UserID()))
		return nil, server.InternalStatus
	}

	return &walletpb.YeePayWalletOpenUrlResp{OpenUrl: login.Url}, nil
}

func (a *Application) GetUserWalletInfo(ctx context.Context) (*walletpb.YeePayWalletInfoResp, error) {
	// 验证用户是否登录
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 判断用户是否有绑定钱包
	yeepayWallet, err := a.walletRepo.GetYeePayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if yeepayWallet == nil {
		return nil, status.Error(codes.NotFound, "user not open yeepay wallet")
	}

	// 钱包登录注册
	walletRegisterLoginCmd := yeepay.WalletRegisterLoginCmd{
		UserID:        yeepayWallet.ExternalUserID(),
		Name:          yeepayWallet.Name(),
		CertificateNo: yeepayWallet.IDCard(),
		RedirectURL:   "0.o",
		RequestNo:     uuid.New().String(),
	}

	login, err := a.yeepayClient.WalletRegisterLogin(ctx, &walletRegisterLoginCmd)
	if err != nil {
		return nil, err
	}

	if login.Url == "" {
		zap.L().Error("yeepay login url is empty", zap.String("userID", yeepayWallet.UserID()))
		return nil, server.InternalStatus
	}

	if yeepayWallet.MemberID() == nil {
		return &walletpb.YeePayWalletInfoResp{IsOpen: true, Url: login.Url, Balance: decimal.NewFromFloat(0.00).String()}, nil
	}

	// 获取用户易宝钱包余额
	getUserWalletInfoCmd := yeepay.GetUserWalletInfoCmd{
		MerchantUserNo: yeepayWallet.ExternalUserID(),
	}
	info, err := a.yeepayClient.GetUserWalletInfo(&getUserWalletInfoCmd)
	if err != nil {
		return &walletpb.YeePayWalletInfoResp{IsOpen: false}, err
	}

	return &walletpb.YeePayWalletInfoResp{Balance: info.Balance, IsOpen: true, Url: login.Url}, nil
}

func (a *Application) BatchGetUsersWalletStatusRPC(ctx context.Context, req *walletpb.BatchGetUsersWalletStatusRPCReq) (*walletpb.BatchGetUsersWalletStatusRPCResp, error) {
	if len(req.UserIds) == 0 {
		return &walletpb.BatchGetUsersWalletStatusRPCResp{
			UserWalletStatus: make([]*walletpb.BatchGetUsersWalletStatusRPCResp_UserWalletStatus, 0),
		}, nil
	}

	walletStatuses, err := a.walletService.BatchGetUserWalletStatus(ctx, req.UserIds)
	if err != nil {
		return nil, err
	}

	// 组装返回数据
	userWalletStatuses := make([]*walletpb.BatchGetUsersWalletStatusRPCResp_UserWalletStatus, 0, len(walletStatuses))
	for _, s := range walletStatuses {
		userWalletStatuses = append(userWalletStatuses, &walletpb.BatchGetUsersWalletStatusRPCResp_UserWalletStatus{
			UserId:             s.UserID(),
			YeepayWalletIsOpen: s.YeepayWalletIsOpen(),
			KfpayWalletIsOpen:  s.KfpayWalletIsOpen(),
			IsOpen:             s.IsOpen(),
		})
	}

	return &walletpb.BatchGetUsersWalletStatusRPCResp{
		UserWalletStatus: userWalletStatuses,
	}, nil
}

func (a *Application) GetYeepayWalletInfoByConditionRPC(ctx context.Context, req *walletpb.GetYeepayWalletInfoByConditionRPCReq) (*walletpb.GetYeepayWalletInfoByConditionRPCResp, error) {
	cmd := &wallet.GetYeepayWalletByConditionCmd{
		Page:         req.Page,
		PageSize:     req.PageSize,
		Keyword:      req.Keyword,
		UserID:       req.UserId,
		IsOpenYeepay: req.IsOpenYeepay,
	}
	if req.StartTime != nil {
		startTime := time.UnixMilli(*req.StartTime)
		cmd.StartTime = &startTime
	}
	if req.EndTime != nil {
		endTime := time.UnixMilli(*req.EndTime)
		cmd.EndTime = &endTime
	}
	condition, total, err := a.walletRepo.GetYeepayWalletByCondition(ctx, cmd)
	if err != nil {
		return nil, err
	}
	userInfos := make([]*walletpb.GetYeepayWalletInfoByConditionRPCResp_UserInfo, 0, len(condition))
	for _, w := range condition {
		userInfos = append(userInfos, &walletpb.GetYeepayWalletInfoByConditionRPCResp_UserInfo{
			UserId:             w.UserID(),
			UserName:           w.Name(),
			IdCard:             w.IDCard(),
			YeepayWalletIsOpen: w.IsOpen(),
			YeepayMemberId:     lo.FromPtr(w.MemberID()),
		})
	}

	return &walletpb.GetYeepayWalletInfoByConditionRPCResp{
		UserInfo: userInfos,
		Total:    total,
	}, nil
}
