package wallet

import (
	"cnb.cool/1024hub/kfpay-go-sdk"
	kycpb "cnb.cool/cymirror/ces-services/account-service/gen/proto/ces/account/kyc"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"
	smspb "cnb.cool/cymirror/ces-services/sms-infra/gen/proto/ces/sms/sms"
	profilepb "cnb.cool/cymirror/ces-services/user-service/gen/proto/ces/user/profile"
)

type Application struct {
	walletRepo    wallet.Repo
	yeepayClient  *yeepay.Client
	kycClient     kycpb.KYCServiceClient
	profileClient profilepb.ProfileServiceClient
	kfpayClient   *kfpay.Client
	walletService *wallet.Service
	smsClient     smspb.SmsServiceClient
	kfpayWalletPasswordPepper *wallet.KfpayWalletPasswordPepper
}

func NewApplication(
	walletRepo wallet.Repo,
	yeepayClient *yeepay.Client,
	kycClient kycpb.KYCServiceClient,
	profileClient profilepb.ProfileServiceClient,
	kfpayClient *kfpay.Client,
	walletService *wallet.Service,
	smsClient smspb.SmsServiceClient,
	kfpayWalletPasswordPepper *wallet.KfpayWalletPasswordPepper,
) *Application {
	return &Application{
		walletRepo:    walletRepo,
		yeepayClient:  yeepayClient,
		kycClient:     kycClient,
		profileClient: profileClient,
		kfpayClient:   kfpayClient,
		walletService: walletService,
		smsClient:     smsClient,
		kfpayWalletPasswordPepper: kfpayWalletPasswordPepper,
	}
}
