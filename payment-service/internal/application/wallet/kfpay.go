package wallet

import (
	smspb "cnb.cool/cymirror/ces-services/sms-infra/gen/proto/ces/sms/sms"
	"context"
	"fmt"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
	"time"

	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/query"
	"github.com/samber/lo"

	"cnb.cool/1024hub/kfpay-go-sdk"
	kycpb "cnb.cool/cymirror/ces-services/account-service/gen/proto/ces/account/kyc"
	"cnb.cool/cymirror/ces-services/common/auth"
	"cnb.cool/cymirror/ces-services/common/server"
	walletpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/wallet"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
)

// OTP 有效期和最小重发间隔
const (
	phoneForgetPasswordOTPTTL            = 5 * time.Minute
	phoneForgetPasswordMinResendInterval = 1 * time.Minute
)

var (
	// [^\p{Han}A-Za-z0-9]+ 匹配所有 **非**（汉字、英文字母、数字）的字符
	shortNameRegexp = regexp.MustCompile(`[^\p{Han}A-Za-z0-9]+`)
)

func (a *Application) KfpayBindBankCard(ctx context.Context, req *walletpb.KfpayBindBankCardReq) (*walletpb.KfpayBindBankCardResp, error) {
	// 1. 校验是否登陆
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 不支持除借记卡之外的绑定
	if req.CardType != walletpb.BankCardType_Debit {
		return nil, status.Error(codes.InvalidArgument, "card type is not supported")
	}

	// 忘记密码需要验证token
	if req.ReqType == 1 {
		// 校验token
		token, err := a.walletRepo.GetKfpayWalletForgetPayPasswordToken(ctx, userID, wallet.ForgetPayPasswordStepBankCardVerified)
		if err != nil {
			return nil, server.InternalStatus
		}
		if token != req.Token {
			return nil, status.Error(codes.InvalidArgument, "invalid token")
		}
	}

	// 2. 获取实名信息
	userIDCard, err := a.kycClient.GetIDCardByUserIDRPC(ctx, &kycpb.GetIDCardByUserIDRPCReq{UserID: userID})
	if err != nil {
		return nil, err
	}
	if !userIDCard.IsAuth {
		return nil, status.Error(codes.InvalidArgument, "user not auth")
	}

	// 3. 校验钱包是否已开通
	kfWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if !kfWallet.IsOpen() {
		return nil, status.Error(codes.InvalidArgument, "wallet not open")
	}

	// 4. 检查卡是否已绑定
	card, err := a.walletRepo.GetKfpayUserBandCardByCardNoWithUserID(ctx, userID, req.CardNo)
	if err != nil {
		return nil, err
	}

	resp := &walletpb.KfpayBindBankCardResp{}

	if card != nil {
		return nil, status.Error(codes.InvalidArgument, "card already bind")
	}

	// 5. 调用「卡bin校验」获取bank code
	// 6. 「单笔银行卡带手机号验证」 验证（储蓄卡，信用卡）信息是否正确 => 四要素（身份证，姓名，手机号，卡号）是否正确
	vcbResp, bankCode, err := a.walletService.KfpayValidateBankCard(ctx, userID, userIDCard, req.CardNo, req.PhoneNumber, req.CardType)
	if err != nil {
		return nil, err
	}

	cardType := wallet.NewCardTypeFromInt16(int16(req.CardType.Number()))

	cb := wallet.NewKfpayUserBandCardBuilder().
		SetBasic(req.CardNo, bankCode.BankName(), req.PhoneNumber, cardType, userID, vcbResp.BankType, wallet.WithdrawStatusNone)

	if req.CardType == walletpb.BankCardType_Credit {
		expireAt := time.UnixMilli(req.ExpireTime)
		cb.SetCredit(req.Cvv, expireAt)
	}

	bankCard, err := cb.Build()
	if err != nil {
		zap.L().Error("kfpay validate bank card failed", zap.Error(err))
		return nil, server.InternalStatus
	}
	if err = a.walletRepo.CreateKfpayUserBandCard(ctx, bankCard); err != nil {
		return nil, server.InternalStatus
	}

	if req.ReqType == 1 {
		// 将卡号和对应的手机号放入redis
		if err := a.walletRepo.SetKfpayCardAndPhone(ctx, userID, req.CardNo, req.PhoneNumber); err != nil {
			return nil, server.InternalStatus
		}

		// 忘记密码新增绑定银行卡，需要返回 token
		token := uuid.NewString()
		if err := a.walletRepo.SetKfpayWalletForgetPayPasswordToken(ctx, userID, token, wallet.ForgetPayPasswordStepSmsSent); err != nil {
			return nil, server.InternalStatus
		}
		resp.Token = token

		//删除旧token
		_ = a.walletRepo.DeleteKfpayWalletForgetPayPasswordToken(ctx, userID, wallet.ForgetPayPasswordStepBankCardVerified)
	}
	return resp, nil
}

func (a *Application) KfpayUnBindBankCard(ctx context.Context, req *walletpb.KfpayUnBindBankCardReq) (*walletpb.KfpayUnBindBankCardResp, error) {
	// 校验是否登陆
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 获取钱包信息
	kfWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	if !kfWallet.IsOpen() {
		return nil, status.Error(codes.InvalidArgument, "wallet is not open")
	}

	// 提现卡不允许解绑
	withdrawCard, err := a.walletRepo.GetKfpayUserWithdrawBandCardWithUserID(ctx, userID)
	if err != nil || withdrawCard == nil {
		return nil, server.InternalStatus
	}
	if withdrawCard.ID == req.Id {
		return nil, status.Error(codes.InvalidArgument, "cannot unbind withdraw bank card")
	}

	// 审核中的卡不能解绑
	cardWithdrawStatusInReviewing, err := a.walletRepo.GetKfpayUserBankCardWithdrawStatusInReviewing(ctx, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	for _, card := range cardWithdrawStatusInReviewing {
		if card.ID == req.Id {
			return nil, status.Error(codes.InvalidArgument, "cannot unbind reviewing bank card")
		}
	}

	// 校验密码
	err = a.walletService.ValidateKfpayPassword(ctx, kfWallet, req.PayPassword)
	if err != nil {
		return nil, err
	}

	// 删除记录
	err = a.walletRepo.DeleteKfpayUserBankCardWithUserID(ctx, req.Id, userID)
	if err != nil {
		return nil, err
	}

	return &walletpb.KfpayUnBindBankCardResp{}, nil
}

func (a *Application) KfpayOpenWallet(ctx context.Context, req *walletpb.KfpayOpenWalletReq) (*emptypb.Empty, error) {
	// 1. 校验是否登陆
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 2. 校验钱包信息
	kfpayWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if kfpayWallet != nil {
		// 检查钱包是否开通
		if kfpayWallet.IsOpen() {
			return nil, status.Error(codes.AlreadyExists, "wallet is open")
		}

		// 校验钱包是否审核中
		if kfpayWallet.IsAuditing() {
			return nil, status.Error(codes.FailedPrecondition, "wallet is auditing")
		}
	}

	// 3. 获取用户信息
	userIDCard, err := a.kycClient.GetIDCardByUserIDRPC(ctx, &kycpb.GetIDCardByUserIDRPCReq{UserID: userID})
	if err != nil {
		return nil, server.InternalStatus
	}
	if !userIDCard.IsAuth {
		return nil, status.Error(codes.InvalidArgument, "user not auth")
	}

	// 4. 调用「卡bin校验」获取bank code & 校验银行卡信息
	// 只支持借记卡
	vcbResp, bankCode, err := a.walletService.KfpayValidateBankCard(ctx, userID, userIDCard, req.CardNo, req.PhoneNumber, walletpb.BankCardType_Debit)
	if err != nil {
		return nil, err
	}

	// 写入数据库 钱包信息 和 银行卡信息
	err = db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
		if kfpayWallet == nil {
			kfpayWallet, err = wallet.NewKfpay(userID, userIDCard.Name, userIDCard.IdCard)
			if err != nil {
				return err
			}
			err = a.walletRepo.CreateKfpayWallet(ctx, kfpayWallet)
			if err != nil {
				return err
			}
		} else {
			// 二次创建直接更新钱包
			kfpayWallet.SetStatus(wallet.KfpayStatusAuditing)
			err = a.walletRepo.UpdateKfpayWallet(ctx, kfpayWallet)
			if err != nil {
				return err
			}

			// 移除之前所有银行卡记录
			err = a.walletRepo.DeleteAllKfpayBankCardsByUserID(ctx, userID)
			if err != nil {
				return err
			}
		}

		// 创建银行卡
		cb := wallet.NewKfpayUserBandCardBuilder().
			SetBasic(req.CardNo, bankCode.BankName(), req.PhoneNumber, wallet.CardTypeDebit, userID, vcbResp.BankType, wallet.WithdrawStatusActive)
		bankCard, err := cb.Build()
		if err != nil {
			return err
		}

		err = a.walletRepo.CreateKfpayUserBandCard(ctx, bankCard)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, server.InternalStatus
	}

	idCardNameReplaced := shortNameRegexp.ReplaceAllString(userIDCard.Name, "")
	asmResp, err := a.kfpayClient.AddSecondMerchant(ctx, &kfpay.AddSecondMerchantCmd{
		OrderNo:           kfpayWallet.OrderNo(),
		SecMerchantName:   userIDCard.Name,
		ShortName:         idCardNameReplaced,
		District:          "0728", // 上海市-嘉定区-嘉定区
		Address:           wallet.GenStreetName(),
		LegalName:         userIDCard.Name,
		ContactName:       userIDCard.Name,
		ContactPhone:      req.PhoneNumber,
		ContactEmail:      "<EMAIL>", // 随便填固定值
		MerchantProperty:  kfpay.MerchantPropertyPerson,
		Category:          "**********", // 个人-线下
		MerchantAttribute: kfpay.MerchantAttributeHybrid,
		PersonCertInfo: []kfpay.CertInfo{
			{
				CertType:      kfpay.CertificationTypeIDCard,
				CertNo:        userIDCard.IdCard,
				CertValidDate: "********",
			},
		},
		SettleBankAccount: &kfpay.SettleBankAccount{
			SettleBankNo:               vcbResp.BankType,
			SettleBankAccountNo:        req.CardNo,
			SettleName:                 userIDCard.Name,
			SettleBankAcctType:         kfpay.BankAcctTypePersonal,
			SettleAccountCreditOrDebit: kfpay.AccountCardTypeDebit,
		},
		BusinessScene:     "支付",
		BusinessMode:      "个人",
		RegisteredFundStr: "100000",
		CertPath:          "****************.zip",     // 固定值
		CertDigest:        "rW/rzgbSW7LmI4exv8gnCg==", // 固定值
		BusinessFunctions: []kfpay.BusinessFunction{
			kfpay.BusinessFunctionSmsExpress,
			kfpay.BusinessFunctionActivePaymentT0,
			kfpay.BusinessFunctionUserConsumption,
		},
		MerchantOperateName:    fmt.Sprintf("%s(数交宝)", idCardNameReplaced),
		MerchantBankBranchNo:   "************",
		MerchantMCC:            "0742",
		MerchantEnglishName:    "JDYS",
		MerchantBankBranchName: "中国建设银行股份有限公司温州信河支行",
	})
	if err != nil {
		zap.L().Error("kfpay add second merchant failed", zap.Error(err), zap.String("card no", req.CardNo))
		return nil, server.InternalStatus
	}
	if asmResp.Status == kfpay.AddSecondMerchantStatusRejected {
		zap.L().Error("kfpay add second merchant failed",
			zap.String("status", asmResp.Status.String()),
			zap.String("card no", req.CardNo),
			zap.String("failure details", asmResp.FailureDetails),
		)
		return nil, status.Error(codes.InvalidArgument, "validate information failed")
	}

	return nil, nil
}

func (a *Application) KfpayWithdraw(ctx context.Context, req *walletpb.KfpayWithdrawReq) (*emptypb.Empty, error) {
	// 校验是否登陆
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 提现金额
	amount, err := decimal.NewFromString(req.Amount)
	if err != nil {
		return nil, err
	}
	if !amount.IsPositive() {
		return nil, status.Error(codes.InvalidArgument, "amount must be greater than 0")
	}

	// 获取用户钱包
	userWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	// 校验钱包是否开通
	if !userWallet.IsOpen() {
		return nil, status.Error(codes.FailedPrecondition, "user wallet is not open")
	}
	// 校验钱包密码
	if !userWallet.ValidatePassword(req.PayPassword, a.kfpayWalletPasswordPepper.Pepper()) {
		return nil, status.Error(codes.InvalidArgument, "password is not match")
	}

	// 查询余额
	balanceResp, err := a.kfpayClient.QueryBalance(ctx, &kfpay.QueryBalanceCmd{
		SecMerchantID: *userWallet.SecMerchantID(),
		OrderNo:       strings.ReplaceAll(uuid.NewString(), "-", "")[:30],
	})
	if err != nil {
		return nil, err
	}
	if balanceResp.Balance.LessThan(amount) {
		return nil, status.Error(codes.FailedPrecondition, "balance is not enough")
	}

	// 获取银行卡
	card, err := a.walletRepo.GetKfpayUserWithdrawBandCardWithUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if card == nil {
		return nil, status.Error(codes.InvalidArgument, "card not found")
	}

	// 插入交易记录
	tradeLog := wallet.NewKfpayTradeLog(wallet.KfpayTradeLogTradeTypeWithdraw, amount,
		userID, "", "")
	err = a.walletRepo.CreateKfpayTradeLog(ctx, tradeLog)
	if err != nil {
		return nil, err
	}

	// 使用defer确保交易状态会被更新
	defer func() {
		if tradeLog.Status() == wallet.KfpayTradeLogStatusProcessing {
			return
		}
		if updateErr := a.walletRepo.UpdateKfpayTradeLog(ctx, tradeLog); updateErr != nil {
			zap.L().Error("update withdraw trade log failed", zap.Error(updateErr))
		}
	}()

	// 快付通提现
	cmd := &kfpay.WithdrawCmd{
		SecMerchantId:        *userWallet.SecMerchantID(),
		OrderNo:              tradeLog.RequestNo(),
		PayerBankCode:        card.BankCardNumber(),
		PayerBankAccountName: userWallet.RealName(),
		TradeName:            "提现",
		TradeTime:            tradeLog.CreatedAt(),
		Amount:               amount,
		Currency:             kfpay.CurrencyTypeCNY,
	}
	withdrawResult, err := a.kfpayClient.Withdraw(ctx, cmd)
	if err != nil {
		tradeLog.SetStatus(wallet.KfpayTradeLogStatusFail)
		zap.L().Error("withdraw failed", zap.Error(err), zap.Any("cmd", cmd), zap.Any("result", withdrawResult))
		return nil, err
	}
	zap.L().Info("withdraw result", zap.Any("cmd", cmd), zap.Any("result", withdrawResult))
	if !(withdrawResult.Status == kfpay.StatusSuccess || withdrawResult.Status == kfpay.StatusProcessing) {
		tradeLog.SetStatus(wallet.KfpayTradeLogStatusFail)
		tradeLog.SetRemark(fmt.Sprintf("%s: %s", withdrawResult.ErrorCode, withdrawResult.FailureDetails))
		return nil, status.Error(codes.FailedPrecondition, withdrawResult.FailureDetails)
	}

	// 设置状态
	tradeLog.SetStatus(wallet.NewKfpayTradeLogStatusFromKfpayStatus(withdrawResult.Status))

	return &emptypb.Empty{}, nil
}

func (a *Application) KfpayRechargeRequest(ctx context.Context, req *walletpb.KfpayRechargeRequestReq) (*walletpb.KfpayRechargeRequestResp, error) {
	// 前置校验
	amount, err := decimal.NewFromString(req.Amount)
	if err != nil {
		return nil, err
	}
	if amount.LessThanOrEqual(decimal.Zero) {
		return nil, status.Error(codes.InvalidArgument, "amount must be greater than 0")
	}

	// 校验是否登陆
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 获取用户钱包
	w, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if w == nil || !w.IsOpen() {
		return nil, status.Error(codes.NotFound, "user not open kfpay wallet")
	}

	// 获取用户银行卡
	bandCard, err := a.walletRepo.GetKfpayUserBandCardWithUserID(ctx, req.CardId, userID)
	if err != nil {
		return nil, err
	}
	if bandCard == nil {
		return nil, status.Error(codes.InvalidArgument, "card not found")
	}

	// 插入记录表
	tradeLog := wallet.NewKfpayTradeLog(wallet.KfpayTradeLogTradeTypeRecharge,
		amount, userID, "", "")
	err = a.walletRepo.CreateKfpayTradeLog(ctx, tradeLog)
	if err != nil {
		return nil, err
	}

	// 调用短信快捷申请下单
	cmd := &kfpay.SmsCollectCmd{
		SecMerchantId:            *w.SecMerchantID(),
		OrderNo:                  tradeLog.RequestNo(),
		TerminalIp:               "*************",
		TradeName:                "用户充值",
		TradeTime:                time.Now(),
		Amount:                   amount,
		Currency:                 kfpay.CurrencyTypeCNY,
		CustBankNo:               bandCard.BankCode(),
		CustBankAccountNo:        bandCard.BankCardNumber(),
		CustBindPhoneNo:          bandCard.RegisteredMobileNumber(),
		CustName:                 w.RealName(),
		CustAccountCreditOrDebit: lo.ToPtr(bandCard.CardType().ToSDKBankCardType()),
		CustCardValidDate:        bandCard.CreditCardExpirationDate(),
		CustCardCvv2:             bandCard.CreditCardCvv(),
		CustCertificationType:    kfpay.CertificationTypeIDCard,
		CustID:                   w.Card(),
		CustPhone:                bandCard.RegisteredMobileNumber(),
		PayerMerchantId:          *w.SecMerchantID(),
	}
	result, err := a.kfpayClient.SmsCollect(ctx, cmd)
	if err != nil {
		zap.L().Error("recharge request failed", zap.Error(err), zap.Any("cmd", cmd), zap.Any("result", result))
		return nil, err
	}

	if result.Status != kfpay.StatusSuccess && result.Status != kfpay.StatusUserPaying {
		zap.L().Error("recharge request failed", zap.Any("cmd", cmd), zap.Any("result", result))
		return nil, status.Error(codes.FailedPrecondition, result.FailureDetails)
	}

	return &walletpb.KfpayRechargeRequestResp{
		OrderNo: tradeLog.RequestNo(),
	}, nil
}

func (a *Application) KfpayRechargeConfirm(ctx context.Context, req *walletpb.KfpayRechargeConfirmReq) (*walletpb.KfpayRechargeConfirmResp, error) {
	// 校验是否登陆
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 校验数据库数据
	log, err := a.walletRepo.GetKfpayTradeLogByReqNo(ctx, req.OrderNo)
	if err != nil {
		return nil, err
	}
	if log == nil {
		return nil, status.Error(codes.InvalidArgument, "order not found")
	}
	if log.Status() != wallet.KfpayTradeLogStatusProcessing {
		return nil, status.Error(codes.InvalidArgument, "order status is not processing")
	}

	kfpayWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	if kfpayWallet == nil || !kfpayWallet.IsOpen() {
		return nil, status.Error(codes.FailedPrecondition, "wallet is not open")
	}

	bankCard, err := a.walletRepo.GetKfpayUserBandCard(ctx, req.BankCardId)
	if err != nil {
		return nil, server.InternalStatus
	}
	if bankCard == nil {
		return nil, status.Error(codes.FailedPrecondition, "bank card not found")
	}

	// 短信快捷确认确认支付
	result, err := a.kfpayClient.SmsPay(ctx, &kfpay.SmsPayCmd{
		SecMerchantId:   *kfpayWallet.SecMerchantID(),
		OrderNo:         req.OrderNo,
		SmsCode:         req.SmsCode,
		ConfirmFlag:     kfpay.ConfirmFlagConfirm,
		CustBindPhoneNo: bankCard.RegisteredMobileNumber(),
	})
	if err != nil {
		zap.L().Error("kfpay confirm failed", zap.Error(err),
			zap.String("order no", req.OrderNo),
		)
		return nil, err
	}

	if result.Status == kfpay.StatusSuccess {
		log.SetStatus(wallet.KfpayTradeLogStatusSuccess)
	} else if result.Status == kfpay.StatusFailure {
		log.SetStatus(wallet.KfpayTradeLogStatusFail)
		log.SetRemark(fmt.Sprintf("%s: %s", result.ErrorCode, result.FailureDetails))
		err = a.walletRepo.UpdateKfpayTradeLog(ctx, log)
		if err != nil {
			return nil, server.InternalStatus
		}
		return nil, status.Error(codes.Internal, "pay failed")
	} else {
		zap.L().Error("kfpay confirm failed", zap.Error(err),
			zap.String("order no", req.OrderNo),
			zap.Any("result", result),
		)
		return nil, status.Error(codes.InvalidArgument, result.FailureDetails)
	}

	err = a.walletRepo.UpdateKfpayTradeLog(ctx, log)
	if err != nil {
		return nil, err
	}

	return &walletpb.KfpayRechargeConfirmResp{}, nil
}

func (a *Application) GetKfpayWalletInfo(ctx context.Context, req *walletpb.KfpayGetWalletInfoReq) (*walletpb.KfpayGetWalletInfoResp, error) {
	// 1. 校验是否登陆
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}
	// 2. 校验钱包是否开通
	kfpayWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if kfpayWallet == nil {
		return &walletpb.KfpayGetWalletInfoResp{}, nil
	}
	if !kfpayWallet.IsOpen() {
		return &walletpb.KfpayGetWalletInfoResp{
			Status: kfpayWallet.Status().Int32(),
		}, nil
	}
	//3. 获取余额
	queryBalanceCmd := &kfpay.QueryBalanceCmd{
		OrderNo: kfpayWallet.OrderNo(),
	}
	if kfpayWallet.SecMerchantID() != nil {
		queryBalanceCmd.SecMerchantID = *kfpayWallet.SecMerchantID()
	}
	queryBalanceResult, err := a.kfpayClient.QueryBalance(ctx, queryBalanceCmd)
	if err != nil {
		zap.L().Error("kfpay query balance failed", zap.Error(err),
			zap.String("SecMerchantID", *kfpayWallet.SecMerchantID()),
			zap.String("OrderNo", kfpayWallet.OrderNo()),
			zap.String("FirstMerchantID", a.kfpayClient.CommonBaseParams.MerchantID))
	}
	if queryBalanceResult.Status != kfpay.StatusSuccess {
		zap.L().Error("kfpay query balance failed failed",
			zap.String("status", queryBalanceResult.Status.String()),
			zap.String("order no", queryBalanceResult.OrderNo),
			zap.String("failure details", queryBalanceResult.FailureDetails),
			zap.String("error code", queryBalanceResult.ErrorCode),
		)
		return nil, server.InternalStatus
	}
	w := &walletpb.KfpayGetWalletInfoResp{
		Balance:     queryBalanceResult.Balance.String(),
		Status:      kfpayWallet.Status().Int32(),
		HasPassword: len(kfpayWallet.PayPassword()) > 0,
	}
	return w, nil
}

func (a *Application) GetKfpayBankCard(ctx context.Context) (*walletpb.GetKfpayBankCardResp, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	cards, err := a.walletRepo.GetKfpayUserBandCardsByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if len(cards) == 0 {
		return &walletpb.GetKfpayBankCardResp{}, nil
	}

	cardNumbers := make([]string, 0, len(cards))
	for _, card := range cards {
		cardNumbers = append(cardNumbers, card.BankCardNumber())
	}

	kfpayBankCards := make([]*walletpb.KfpayBankCard, 0, len(cards))
	for _, card := range cards {
		cardNo := card.BankCardNumber()
		if len(cardNo) > 4 {
			cardNo = cardNo[len(cardNo)-4:]
		}

		mobileNumber := card.RegisteredMobileNumber()
		mobileNumberLen := len(card.RegisteredMobileNumber())
		if mobileNumberLen > 7 {
			var build strings.Builder
			build.WriteString(card.RegisteredMobileNumber()[:3])
			build.WriteString("****")
			build.WriteString(card.RegisteredMobileNumber()[mobileNumberLen-4:])
			mobileNumber = build.String()
		}

		kfpayBankCards = append(kfpayBankCards, &walletpb.KfpayBankCard{
			Id:             card.ID,
			BankName:       card.BankName(),
			CardNo:         cardNo,
			CardPhone:      mobileNumber,
			CardType:       card.CardType().ToPbBankCardType(),
			ReviewMessage:  card.ReviewMessage(),
			WithdrawStatus: card.WithdrawStatus().ToPbWithdrawStatus(),
		})
	}

	return &walletpb.GetKfpayBankCardResp{
		KfpayBankCards: kfpayBankCards,
	}, nil
}

func (a *Application) SetKfpayWalletPasswordApply(ctx context.Context, req *walletpb.SetKfpayWalletPasswordApplyReq) (*walletpb.SetKfpayWalletPasswordApplyResp, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 校验钱包是否开通
	kfWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	if !kfWallet.IsOpen() {
		return nil, status.Error(codes.FailedPrecondition, "wallet is not open")
	}

	// 校验密码
	err = a.walletService.ValidateKfpayPassword(ctx, kfWallet, req.OldPayPassword)
	if err != nil {
		return nil, err
	}

	token := uuid.New().String()
	// 生成token
	err = a.walletRepo.SetKfpayWalletResetPasswordToken(ctx, userID, token)
	if err != nil {
		return nil, server.InternalStatus
	}

	return &walletpb.SetKfpayWalletPasswordApplyResp{
		Token: token,
	}, nil
}

func (a *Application) SetKfpayWalletPassword(ctx context.Context, req *walletpb.SetKfpayWalletPasswordReq) (*emptypb.Empty, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 校验钱包是否开通
	kfWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	if !kfWallet.IsOpen() {
		return nil, status.Error(codes.FailedPrecondition, "wallet is not open")
	}

	switch req.Type {
	case walletpb.KfpaySetPayPasswordType_SET_PAY_PASSWORD_TYPE_INITIAL: //首次设置密码
		// 首次设置密码逻辑（不需要校验token）
		if len(kfWallet.PayPassword()) != 0 {
			return nil, status.Error(codes.FailedPrecondition, "password already set")
		}
	case walletpb.KfpaySetPayPasswordType_SET_PAY_PASSWORD_TYPE_MODIFY: // 修改密码
		// 校验token
		token, err := a.walletRepo.GetKfpayWalletResetPasswordToken(ctx, userID)
		if err != nil {
			return nil, server.InternalStatus
		}
		if token != req.Token {
			return nil, status.Error(codes.InvalidArgument, "invalid token")
		}
	case walletpb.KfpaySetPayPasswordType_SET_PAY_PASSWORD_TYPE_RESET: // 忘记密码重置密码
		// 校验 token
		token, err := a.walletRepo.GetKfpayWalletForgetPayPasswordToken(ctx, userID, wallet.ForgetPayPasswordStepPasswordReset)
		if err != nil {
			return nil, server.InternalStatus
		}
		if token != req.Token {
			return nil, status.Error(codes.InvalidArgument, "invalid token")
		}
	default:
		return nil, status.Error(codes.InvalidArgument, "invalid type")
	}

	// 设置新密码
	err = kfWallet.SetPayPassword(req.PayPassword, a.kfpayWalletPasswordPepper.Pepper())
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, "invalid password")
	}

	err = a.walletRepo.UpdateKfpayWallet(ctx, kfWallet)
	if err != nil {
		return nil, server.InternalStatus
	}

	if req.Type == walletpb.KfpaySetPayPasswordType_SET_PAY_PASSWORD_TYPE_RESET {
		_ = a.walletRepo.DeleteKfpayWalletForgetPayPasswordToken(ctx, userID, wallet.ForgetPayPasswordStepPasswordReset)
		_ = a.walletRepo.ResetKfpayWalletSetPasswordErrorTimes(ctx, kfWallet.UserID())
	} else if req.Type == walletpb.KfpaySetPayPasswordType_SET_PAY_PASSWORD_TYPE_MODIFY {
		_ = a.walletRepo.DeleteKfpayWalletResetPasswordToken(ctx, userID)
	}

	return &emptypb.Empty{}, nil
}

func (a *Application) SetKfpayWithdrawBankCard(ctx context.Context, req *walletpb.SetKfpayWithdrawBankCardReq) (*emptypb.Empty, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 校验钱包是否开通
	kfWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	if !kfWallet.IsOpen() {
		return nil, status.Error(codes.FailedPrecondition, "wallet is not open")
	}

	//同一时期只能有一张处于审核中
	reviewingCards, err := a.walletRepo.GetKfpayUserBankCardWithdrawStatusInReviewing(ctx, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	if len(reviewingCards) != 0 {
		return nil, status.Error(codes.FailedPrecondition, "withdrawal bank card is already under review")
	}

	// 校验银行卡是否属于该用户且已绑定
	newBandCard, err := a.walletRepo.GetKfpayUserBandCardWithUserID(ctx, req.NewCardId, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	if newBandCard == nil {
		return nil, status.Error(codes.InvalidArgument, "card not found")
	}
	if newBandCard.WithdrawStatus().Value() == wallet.WithdrawStatusReviewing.Value() || newBandCard.WithdrawStatus().Value() == wallet.WithdrawStatusActive.Value() {
		return &emptypb.Empty{}, nil
	}

	// 获取实名信息
	userIDCard, err := a.kycClient.GetIDCardByUserIDRPC(ctx, &kycpb.GetIDCardByUserIDRPCReq{UserID: userID})
	if err != nil {
		return nil, server.InternalStatus
	}
	if !userIDCard.IsAuth {
		return nil, status.Error(codes.InvalidArgument, "user not auth")
	}

	newOrderNo := strings.ReplaceAll(uuid.New().String(), "-", "")[:30]

	// 事务处理：更新银行卡和钱包
	err = db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
		// 设置新卡为审核中的状态
		newBandCard.SetWithdrawStatus(wallet.WithdrawStatusReviewing)
		if err := a.walletRepo.UpdateKfpayBankCard(ctx, newBandCard); err != nil {
			return err
		}
		// 更新钱包OrderNo
		kfWallet.SetOrderNo(newOrderNo)
		if err := a.walletRepo.UpdateKfpayWallet(ctx, kfWallet); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, server.InternalStatus
	}

	cmd := &kfpay.UpdateSecondMerchantCmd{
		SettleBankAccount: &kfpay.SettleBankAccount{
			SettleBankNo:               newBandCard.BankCode(),
			SettleBankAccountNo:        newBandCard.BankCardNumber(),
			SettleName:                 userIDCard.Name,
			SettleBankAcctType:         kfpay.BankAcctTypePersonal,
			SettleAccountCreditOrDebit: kfpay.AccountCardTypeDebit,
		},
		SecMerchantID: kfWallet.SecondaryMerchantID(),
		OrderNo:       newOrderNo,
	}

	resp, err := a.kfpayClient.UpdateSecondMerchant(ctx, cmd)
	if err != nil {
		zap.L().Error("kfpay update second merchant failed", zap.Error(err), zap.String("card no", req.NewCardId))
		return nil, server.InternalStatus
	}
	if resp.Status.String() == kfpay.AddSecondMerchantStatusRejected.String() {
		zap.L().Error("kfpay update second merchant failed",
			zap.String("status", resp.Status.String()),
			zap.String("card no", req.NewCardId),
			zap.String("failure details", resp.FailureDetails),
		)
		return nil, status.Error(codes.InvalidArgument, "validate information failed")
	}

	return &emptypb.Empty{}, nil
}

func (a *Application) ResetKfpayFailWithdrawBankCard(ctx context.Context) (*emptypb.Empty, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 校验钱包是否开通
	kfWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	if !kfWallet.IsOpen() {
		return nil, status.Error(codes.FailedPrecondition, "wallet is not open")
	}

	// 获取所有失败的提现银行卡
	failCards, err := a.walletRepo.GetKfpayUserBandCardsByUserIDWithWithdrawStatus(ctx, userID, wallet.WithdrawStatusFail.Value())
	if err != nil {
		return nil, server.InternalStatus
	}

	// 遍历所有失败的银行卡，将其状态重置为非提现卡
	for _, failCard := range failCards {
		// 重置为普通卡状态（非提现卡）
		failCard.SetWithdrawStatus(wallet.WithdrawStatusNone)
		// 清空审核消息
		failCard.SetReviewMessage(nil)
		// 更新数据库
		err = a.walletRepo.UpdateKfpayBankCard(ctx, failCard)
		if err != nil {
			zap.L().Error("reset kfpay fail withdraw bank card failed",
				zap.Error(err),
				zap.String("card_id", failCard.ID),
				zap.String("user_id", userID))
			return nil, server.InternalStatus
		}
	}
	return &emptypb.Empty{}, nil
}

func (a *Application) ForgetPasswordVerifyUserIdentity(ctx context.Context, req *walletpb.VerifyUserIdentityReq) (*walletpb.KfpForgetPayPasswordTokenResp, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 1. 校验钱包是否开通
	kfWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	if !kfWallet.IsOpen() {
		return nil, status.Error(codes.FailedPrecondition, "wallet is not open")
	}

	//2. 获取实名信息
	userIDCard, err := a.kycClient.GetIDCardByUserIDRPC(ctx, &kycpb.GetIDCardByUserIDRPCReq{UserID: userID})
	if err != nil {
		return nil, server.InternalStatus
	}
	if !userIDCard.IsAuth {
		return nil, status.Error(codes.InvalidArgument, "user not authenticated")
	}

	// 3. 校验身份证是否一致
	isMatch := (req.IdCard == userIDCard.IdCard)

	// 4. 一致时生成token写入redis
	if !isMatch {
		return nil, status.Error(codes.InvalidArgument, "user profile error")
	}
	token := uuid.NewString()
	if err := a.walletRepo.SetKfpayWalletForgetPayPasswordToken(ctx, userID, token, wallet.ForgetPayPasswordStepBankCardVerified); err != nil {
		return nil, server.InternalStatus
	}

	return &walletpb.KfpForgetPayPasswordTokenResp{
		Token: token,
	}, nil
}

func (a *Application) ForgetPasswordPhoneOtp(ctx context.Context, req *walletpb.KfpForgetPasswordPhoneOtpReq) (*walletpb.KfpForgetPayPasswordTokenResp, error) {
	// 1. 校验登录态
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 2. 校验 token 是否有效
	token, err := a.walletRepo.GetKfpayWalletForgetPayPasswordToken(ctx, userID, wallet.ForgetPayPasswordStepSmsSent)
	if err != nil {
		return nil, server.InternalStatus
	}
	if token != req.Token {
		return nil, status.Error(codes.InvalidArgument, "invalid token")
	}

	// 3. 拼接完整国际手机号
	phone, cardNo, err := a.walletRepo.GetKfpayCardAndPhone(ctx, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	if phone == "" || cardNo == "" {
		return nil, server.InternalStatus
	}
	// 只支持中国手机号
	fullPhone := fmt.Sprintf("+86%s", phone)

	// 4. 限流：获取上次发送时间的 TTL，判断是否已满最小间隔
	ttl, err := a.walletRepo.GetPhoneForgetPayPasswordOTPTTL(ctx, fullPhone)
	if err != nil {
		return nil, server.InternalStatus
	}
	if ttl > 0 && ttl+phoneForgetPasswordMinResendInterval > phoneForgetPasswordOTPTTL {
		return nil, status.Error(codes.ResourceExhausted, "验证码发送过于频繁，请稍后再试")
	}

	// 5. 生成 6 位数字 OTP
	otp := strconv.Itoa(100000 + rand.Intn(900000))

	// 6. 发送 SMS OTP
	_, err = a.smsClient.SendOTP(ctx, &smspb.SendOTPReq{
		Phone:  fullPhone,
		Expire: int32(phoneForgetPasswordOTPTTL.Minutes()),
		Code:   otp,
	})
	if err != nil {
		return nil, server.InternalStatus
	}

	// 7. 存储 OTP，并设置过期
	if err := a.walletRepo.SetPhoneForgetPayPasswordOTP(ctx, fullPhone, otp, phoneForgetPasswordOTPTTL); err != nil {
		return nil, server.InternalStatus
	}

	// 8. 创建验证otp token
	token = uuid.NewString()
	if err := a.walletRepo.SetKfpayWalletForgetPayPasswordToken(ctx, userID, token, wallet.ForgetPayPasswordStepSmsVerified); err != nil {
		return nil, server.InternalStatus
	}
	return &walletpb.KfpForgetPayPasswordTokenResp{
		Token: token,
	}, nil
}

func (a *Application) KfpForgetPayPasswordOTPVerify(ctx context.Context, req *walletpb.ForgetPayPasswordOTPVerifyReq) (*walletpb.KfpForgetPayPasswordTokenResp, error) {
	// 1. 校验登录态
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}
	// 2. 校验 token 是否有效
	token, err := a.walletRepo.GetKfpayWalletForgetPayPasswordToken(ctx, userID, wallet.ForgetPayPasswordStepSmsVerified)
	if err != nil {
		return nil, server.InternalStatus
	}
	if token != req.Token {
		return nil, status.Error(codes.InvalidArgument, "invalid token")
	}
	// 3.获取手机号
	phone, cardNo, err := a.walletRepo.GetKfpayCardAndPhone(ctx, userID)
	if err != nil {
		return nil, server.InternalStatus
	}
	if phone == "" || cardNo == "" {
		return nil, server.InternalStatus
	}
	fullPhone := fmt.Sprintf("+86%s", phone)

	// 4.验证验证码
	otp, err := a.walletRepo.GetPhoneForgetPayPasswordOTP(ctx, fullPhone)
	if err != nil {
		return nil, server.InternalStatus
	}
	if otp == "" || otp != req.SmsCode {
		return nil, status.Error(codes.InvalidArgument, "invalid otp")
	}

	// 5.删除验证码
	err = a.walletRepo.DeletePhoneForgetPayPasswordOTP(ctx, phone)
	if err != nil {
		return nil, server.InternalStatus
	}
	// 6.设置新 token
	token = uuid.NewString()
	if err := a.walletRepo.SetKfpayWalletForgetPayPasswordToken(ctx, userID, token, wallet.ForgetPayPasswordStepPasswordReset); err != nil {
		return nil, server.InternalStatus
	}
	// 7.删除旧token
	for _, tokenType := range []wallet.ForgetPasswordResetStep{wallet.ForgetPayPasswordStepSmsSent, wallet.ForgetPayPasswordStepSmsVerified} {
		_ = a.walletRepo.DeleteKfpayWalletForgetPayPasswordToken(ctx, userID, tokenType)
	}
	return &walletpb.KfpForgetPayPasswordTokenResp{
		Token: token,
	}, nil
}

func (a *Application) GetBankNameByCardNo(ctx context.Context, req *walletpb.GetBankNameByCardNoReq) (*walletpb.GetBankNameByCardNoResp, error) {
	user := auth.UserIDFromContext(ctx)
	if user == "" {
		return nil, auth.ErrUnAuthStatus
	}

	result, err := a.kfpayClient.ValidateCardBin(ctx, &kfpay.ValidateCardBinCmd{
		CardNo: req.CardNo,
	})
	if err != nil {
		return nil, err
	}
	bankCode, err := a.walletRepo.GetKfpayBankCodeByTypeCode(ctx, result.BankType)
	if err != nil {
		return nil, server.InternalStatus
	}
	if bankCode == nil {
		return nil, status.Error(codes.InvalidArgument, "bank code invalid or bank not support")
	}
	if !bankCode.IsSupport() {
		return nil, status.Error(codes.InvalidArgument, "bank not support")
	}

	return &walletpb.GetBankNameByCardNoResp{
		BankName: bankCode.BankName(),
	}, nil
}

func (a *Application) KfpForgetPayPasswordBankCardVerify(ctx context.Context, req *walletpb.ForgetPayPasswordBankCardVerifyReq) (*walletpb.KfpForgetPayPasswordTokenResp, error) {
	// 1. 校验是否登陆
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 2. 校验上一步的 token（即身份验证阶段是否已通过）
	token, err := a.walletRepo.GetKfpayWalletForgetPayPasswordToken(ctx, userID, wallet.ForgetPayPasswordStepBankCardVerified)
	if err != nil {
		return nil, server.InternalStatus
	}
	if token != req.Token {
		return nil, status.Error(codes.InvalidArgument, "invalid token")
	}

	// 3. 校验钱包是否已开通
	kfWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if !kfWallet.IsOpen() {
		return nil, status.Error(codes.InvalidArgument, "wallet not open")
	}

	// 4. 校验卡是否绑定（查找卡信息）
	card, err := a.walletRepo.GetKfpayUserBandCardByCardNoWithUserID(ctx, userID, req.CardNo)
	if err != nil {
		return nil, err
	}
	if card == nil {
		return nil, status.Error(codes.InvalidArgument, "card not found")
	}

	// 5. 校验卡片详细信息是否匹配
	if card.ID != req.Id ||
		card.BankCardNumber() != req.CardNo ||
		card.RegisteredMobileNumber() != req.PhoneNumber {
		return nil, status.Error(codes.InvalidArgument, "card info not match")
	}

	// 6. 将卡号和手机号写入 Redis，供后续短信验证阶段使用
	if err := a.walletRepo.SetKfpayCardAndPhone(ctx, userID, req.CardNo, req.PhoneNumber); err != nil {
		return nil, server.InternalStatus
	}

	// 7. 生成新的 token（用于下一阶段：短信验证码发送）
	token = uuid.NewString()
	if err := a.walletRepo.SetKfpayWalletForgetPayPasswordToken(ctx, userID, token, wallet.ForgetPayPasswordStepSmsSent); err != nil {
		return nil, server.InternalStatus
	}

	// 8. 删除当前阶段的 token（保持一次性使用）
	_ = a.walletRepo.DeleteKfpayWalletForgetPayPasswordToken(ctx, userID, wallet.ForgetPayPasswordStepBankCardVerified)

	// 9. 返回新的 token
	return &walletpb.KfpForgetPayPasswordTokenResp{
		Token: token,
	}, nil
}

func (a *Application) GetKfpayWalletOpenFailedTimes(ctx context.Context) (*walletpb.GetKfpayWalletOpenFailedTimesResp, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}
	times, err := a.walletRepo.GetKfpayVerifyBankFailedTimes(ctx, userID)
	if err != nil {
		return nil, err
	}
	return &walletpb.GetKfpayWalletOpenFailedTimesResp{
		Times: times,
	}, nil
}
