package wallet

import (
	"cnb.cool/cymirror/ces-services/common/auth"
	"context"

	walletpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/wallet"
)

func (a *Application) GetUserOpenedWallet(ctx context.Context) (*walletpb.GetUserOpenedWalletResp, error) {
	// 1. 校验登录状态
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, auth.ErrUnAuthStatus
	}

	// 2. 分别查询快付通和易宝钱包
	kfpWallet, err := a.walletRepo.GetKfpayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	ypWallet, err := a.walletRepo.GetYeePayWalletByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 3. 组装返回结构（仅返回开通状态）
	info := &walletpb.GetUserOpenedWalletResp_UserWalletInfo{
		UserId: userID,
	}
	if kfpWallet != nil && kfpWallet.IsOpen() {
		info.KfpayWalletIsOpen = true
	}
	if ypWallet != nil && ypWallet.IsOpen() {
		info.YeepayWalletIsOpen = true
	}

	return &walletpb.GetUserOpenedWalletResp{
		UserWalletInfo: []*walletpb.GetUserOpenedWalletResp_UserWalletInfo{info},
	}, nil
}

func (a *Application) GetUserOpenedWalletRPC(ctx context.Context, req *walletpb.GetUserOpenedWalletRPCReq) (*walletpb.GetUserOpenedWalletRPCResp, error) {
	if req == nil || len(req.UserIds) == 0 {
		return &walletpb.GetUserOpenedWalletRPCResp{
			UserWalletInfos: make([]*walletpb.GetUserOpenedWalletRPCResp_UserWalletInfo, 0),
		}, nil
	}

	// 查询快付通钱包
	kfpWallets, err := a.walletRepo.BatchGetKfpayWalletsByUserIDs(ctx, req.UserIds)
	if err != nil {
		return nil, err
	}

	// 查询易宝钱包
	ypWallets, err := a.walletRepo.BatchGetYeePayWalletsByUserIDs(ctx, req.UserIds)
	if err != nil {
		return nil, err
	}

	// 初始化 map，方便后续填充数据
	infoMap := make(map[string]*walletpb.GetUserOpenedWalletRPCResp_UserWalletInfo, len(req.UserIds))
	for _, id := range req.UserIds {
		infoMap[id] = &walletpb.GetUserOpenedWalletRPCResp_UserWalletInfo{
			UserId: id,
		}
	}

	// 填充快付通钱包开通状态
	for _, w := range kfpWallets {
		if w == nil {
			continue
		}
		uid := w.UserID()
		info, ok := infoMap[uid]
		if !ok {
			continue
		}
		if w.IsOpen() {
			info.KfpayWalletIsOpen = true
		}
	}

	// 填充易宝钱包开通状态
	for _, y := range ypWallets {
		if y == nil {
			continue
		}
		uid := y.UserID()
		info, ok := infoMap[uid]
		if !ok {
			continue
		}
		if y.IsOpen() {
			info.YeepayWalletIsOpen = true
		}
	}

	// 按照请求中的 user_ids 顺序组装返回结果
	resp := &walletpb.GetUserOpenedWalletRPCResp{
		UserWalletInfos: make([]*walletpb.GetUserOpenedWalletRPCResp_UserWalletInfo, 0, len(infoMap)),
	}
	for _, id := range req.UserIds {
		if info, ok := infoMap[id]; ok {
			resp.UserWalletInfos = append(resp.UserWalletInfos, info)
		}
	}

	return resp, nil
}
