package cron

import (
	"cnb.cool/1024hub/kfpay-go-sdk"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"context"
	"errors"
	"fmt"
	"github.com/bsm/redislock"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"time"
)

var _ cron.Job = (*KfpayTradeLogStatusCheckCron)(nil)
var checkKfpayTradeLogStatusDistributionLockKey = "cron:payment:kfpayTradeLogStatusDist"

type KfpayTradeLogStatusCheckCron struct {
	parentContext context.Context
	walletRepo    wallet.Repo
	kfpayClient   *kfpay.Client
	lock          *redislock.Client
}

func NewCheckKfpayRechargeStatusCron(parentContext context.Context, walletRepo wallet.Repo, kfpayClient *kfpay.Client, lock *redislock.Client) *KfpayTradeLogStatusCheckCron {
	return &KfpayTradeLogStatusCheckCron{parentContext: parentContext, walletRepo: walletRepo, kfpayClient: kfpayClient, lock: lock}
}

func (p *KfpayTradeLogStatusCheckCron) Run() {
	ctx, cancel := context.WithTimeout(p.parentContext, time.Minute-time.Second)
	defer cancel()

	startTime := time.Now()
	zap.L().Info("start check kfpay recharge order status job", zap.Time("start_time", startTime))

	// 加分布式锁
	lock, err := p.lock.Obtain(ctx, checkKfpayTradeLogStatusDistributionLockKey, time.Minute, &redislock.Options{})
	if err != nil && !errors.Is(err, redislock.ErrNotObtained) {
		zap.L().Error("obtain lock failed", zap.Error(err))
		return
	} else if errors.Is(err, redislock.ErrNotObtained) {
		return
	}
	defer func() {
		err := lock.Release(ctx)
		if err != nil {
			zap.L().Error("release lock failed", zap.Error(err))
		}
	}()

	// ---------- 定时任务逻辑开始 ----------
	// 查询充值中 支付中 且 更新时间大于30min后的所有支付方式的订单
	list, err := p.walletRepo.BatchGetExpiredKfpayTradeLog(ctx, 10)
	if err != nil {
		return
	}

	// 反查订单状态
	for _, l := range list {
		// 如果反查结果不存在 结束订单
		result, err := p.kfpayClient.QueryTradeRecord(ctx, &kfpay.QueryTradeRecordCmd{
			OriginalOrderNo: l.RequestNo(),
		})
		if err != nil {
			zap.L().Error("QueryTradeRecord failed", zap.Error(err), zap.String("order_no", l.RequestNo()))
			continue
		}

		if result.Status == kfpay.StatusSuccess {
			// 如果订单存在且完成 完成订单
			l.SetStatus(wallet.KfpayTradeLogStatusSuccess)

		} else if result.Status == kfpay.StatusProcessing {
			if l.TradeType() != wallet.KfpayTradeLogTradeTypeWithdraw {
				// 超时时间过长设置为取消
				l.SetStatus(wallet.KfpayTradeLogStatusCancel)
			}

		} else if result.Status == kfpay.StatusFailure &&
			(result.ErrorCode == kfpay.ErrorCodeInvalidStateForQuickPayConfirm ||
				result.ErrorCode == kfpay.ErrorCodeSmsCodeExpired) {
			// 超时或超时支付设置为取消
			l.SetStatus(wallet.KfpayTradeLogStatusCancel)

		} else if result.Status == kfpay.StatusFailure {
			// 订单失败
			l.SetStatus(wallet.KfpayTradeLogStatusFail)
			l.SetRemark(fmt.Sprintf("%s: %s", result.ErrorCode, result.FailureDetails))
		}

		// 无论是否状态变化都更新订单, 避免订单阻塞
		err = p.walletRepo.UpdateKfpayTradeLog(ctx, l)
		if err != nil {
			zap.L().Error("update kfpay trade log failed", zap.Error(err), zap.String("original order no", l.RequestNo()))
			continue
		}
	}
	// ---------- 定时任务逻辑结束 ----------

	zap.L().Info("end check kfpay recharge order status job", zap.Duration("cost", time.Now().Sub(startTime)))
}
