package cron

import (
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/divide"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/refund"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"
	"context"
	"errors"
	"github.com/bsm/redislock"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"time"
)

var _ cron.Job = (*PayingOrderExpiredCron)(nil)
var paymentOrderExpiredDistributionLockKey = "cron:nft:orderExpiredDist"

type PayingOrderExpiredCron struct {
	parentContext context.Context
	orderRepo     order.Repo
	payRepo       pay.Repo
	yeepayClient  *yeepay.Client
	divideService *divide.Service
	lock          *redislock.Client
	refundService *refund.Service
}

func NewPayingOrderExpiredCron(parentContext context.Context, orderRepo order.Repo, payRepo pay.Repo, yeepayClient *yeepay.Client, divideService *divide.Service, lock *redislock.Client, refundService *refund.Service) *PayingOrderExpiredCron {
	return &PayingOrderExpiredCron{parentContext: parentContext, orderRepo: orderRepo, payRepo: payRepo, yeepayClient: yeepayClient, divideService: divideService, lock: lock, refundService: refundService}
}

func (p *PayingOrderExpiredCron) Run() {
	ctx, cancel := context.WithTimeout(p.parentContext, time.Minute-time.Second)
	defer cancel()

	startTime := time.Now()
	zap.L().Info("start order expired job", zap.Time("start_time", startTime))

	// 加分布式锁
	lock, err := p.lock.Obtain(ctx, paymentOrderExpiredDistributionLockKey, time.Minute, &redislock.Options{})
	if err != nil && !errors.Is(err, redislock.ErrNotObtained) {
		zap.L().Error("obtain lock failed", zap.Error(err))
		return
	} else if errors.Is(err, redislock.ErrNotObtained) {
		return
	}
	defer func() {
		err := lock.Release(ctx)
		if err != nil {
			zap.L().Error("release lock failed", zap.Error(err))
		}
	}()

	// ---------- 定时任务逻辑开始 ----------

	// 查询支付中订单是否存在已过期订单
	orderList, err := p.orderRepo.GetExpiredPayingOrders(ctx, time.Now().Add(-time.Second*90), 50)
	if err != nil {
		return
	}

	for _, o := range orderList {
		o.SetStatus(order.StatusTimeout)
		err = p.orderRepo.UpdateOrder(ctx, o)
		if err != nil {
			continue
		}
	}

	// ---------- 定时任务逻辑结束 ----------

	zap.L().Info("end order expired job", zap.Duration("cost", time.Now().Sub(startTime)))
}
