package cron

import (
	"cnb.cool/1024hub/kfpay-go-sdk"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"context"
	"errors"
	"github.com/bsm/redislock"
	"github.com/google/uuid"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"strings"
	"time"
)

var _ cron.Job = (*PayingOrderExpiredCron)(nil)
var kfpayWalletStatusCheckDistributionLockKey = "cron:payment:kfpayStatusDist"

type KfpayWalletStatusCheckCron struct {
	parentContext context.Context
	walletRepo    wallet.Repo
	kfpayClient   *kfpay.Client
	lock          *redislock.Client
}

func NewKfpayWalletStatusCheckCron(parentContext context.Context, walletRepo wallet.Repo, kfpayClient *kfpay.Client, lock *redislock.Client) *KfpayWalletStatusCheckCron {
	return &KfpayWalletStatusCheckCron{parentContext: parentContext, walletRepo: walletRepo, kfpayClient: kfpayClient, lock: lock}
}

func (p *KfpayWalletStatusCheckCron) Run() {
	ctx, cancel := context.WithTimeout(p.parentContext, time.Minute-time.Second)
	defer cancel()

	startTime := time.Now()
	zap.L().Info("start kfpay wallet check job", zap.Time("start_time", startTime))

	// 加分布式锁
	lock, err := p.lock.Obtain(ctx, kfpayWalletStatusCheckDistributionLockKey, time.Minute, &redislock.Options{})
	if err != nil && !errors.Is(err, redislock.ErrNotObtained) {
		zap.L().Error("obtain lock failed", zap.Error(err))
		return
	} else if errors.Is(err, redislock.ErrNotObtained) {
		return
	}
	defer func() {
		err := lock.Release(ctx)
		if err != nil {
			zap.L().Error("release lock failed", zap.Error(err))
		}
	}()

	// ---------- 定时任务逻辑开始 ----------

	// 获取未审核的钱包
	kfpayWallets, err := p.walletRepo.BatchGetAuditingKfpayWallets(ctx, 50)
	for _, w := range kfpayWallets {
		// 获取钱包状态
		result, err := p.kfpayClient.QuerySecondMerchant(ctx, &kfpay.QuerySecondMerchantCmd{
			MerchantProperty: kfpay.MerchantPropertyPerson,
			CertNo:           w.Card(),
			OrderNo:          w.OrderNo(),
		})
		if err != nil {
			zap.L().Error("query second merchant failed", zap.Error(err), zap.String("wallet_id", w.ID))
			continue
		}
		if result.Status != kfpay.StatusSuccess { // 调用接口是否成功
			zap.L().Error("query second merchant failed", zap.Error(err),
				zap.Any("result", result), zap.String("wallet_id", w.ID))
			continue
		}

		// 带乐观锁更新
		if result.OrderStatus == kfpay.AddSecondMerchantStatusApproved ||
			result.OrderStatus == kfpay.AddSecondMerchantStatusCompleted ||
			result.OrderErrorCode == kfpay.OrderErrorCodeMerchantAlreadyExists { // 审核是否成功或重复开通
			isAuditFail := false
			if len(result.MerchantDetail.SecMerchantId) == 0 {
				// 可能为注销账户重新开通钱包
				result, err = p.kfpayClient.QuerySecondMerchant(ctx, &kfpay.QuerySecondMerchantCmd{
					MerchantProperty: kfpay.MerchantPropertyPerson,
					CertNo:           w.Card(),
				})
				if err != nil {
					zap.L().Error("query second merchant failed", zap.Error(err),
						zap.String("wallet_id", w.ID),
						zap.Any("result", result))
					continue
				}
				// 检查两次都不存在 SecMerchantId
				if len(result.MerchantDetail.SecMerchantId) == 0 {
					zap.L().Error("query second merchant failed", zap.Error(err),
						zap.String("wallet_id", w.ID),
						zap.Any("result", result))
					isAuditFail = true
					goto fail
				}

				// --- 解决钱包可能曾经开通过但提现卡与之前存在不同的情况 ---
				// 1. 获取当前钱包的提现卡信息
				bankCard, err := p.walletRepo.GetKfpayUserWithdrawBandCardWithUserID(ctx, w.UserID())
				if err != nil {
					continue
				}
				if bankCard == nil {
					isAuditFail = true
					goto fail
				}

				// 2. 检查提现卡是否与之前不同
				if bankCard.BankCardNumber() != result.MerchantDetail.SettleBankAccount.SettleBankAccountNo {
					// 3. 钱包重新进入审核状态 更新order no
					newOrderNo := strings.ReplaceAll(uuid.NewString(), "-", "")[:30]
					w.SetOrderNo(newOrderNo)
					err = p.walletRepo.UpdateKfpayWallet(ctx, w)
					if err != nil {
						continue
					}

					// 4. 更新客户的二级商户信息
					usmResp, err := p.kfpayClient.UpdateSecondMerchant(ctx, &kfpay.UpdateSecondMerchantCmd{
						SecMerchantID: result.MerchantDetail.SecMerchantId,
						OrderNo:       newOrderNo,
						SettleBankAccount: &kfpay.SettleBankAccount{
							SettleBankNo:               bankCard.BankCode(),
							SettleBankAccountNo:        bankCard.BankCardNumber(),
							SettleName:                 w.RealName(),
							SettleBankAcctType:         kfpay.BankAcctTypePersonal,
							SettleAccountCreditOrDebit: kfpay.AccountCardTypeDebit,
						},
					})
					if err != nil {
						zap.L().Error("update second merchant failed", zap.Error(err))
						continue
					}
					if usmResp.Status != kfpay.StatusSuccess {
						zap.L().Error("update second merchant failed", zap.Any("cmd", usmResp))
						isAuditFail = true
						goto fail
					}
					continue
				}
			}

		fail:
			if isAuditFail {
				// 钱包审核失败
				w.SetStatus(wallet.KfpayStatusFail)
				err = p.walletRepo.UpdateKfpayWallet(ctx, w)
				if err != nil {
					zap.L().Error("update kfpay wallet failed", zap.Error(err), zap.String("wallet id", w.ID))
					continue
				}
				continue
			}

			// 开通计费套餐
			mResult, err := p.kfpayClient.MerchantPackageOpen(ctx, &kfpay.MerchantPackageOpenCmd{
				SubMerchantID: result.MerchantDetail.SecMerchantId,
				PackageCode:   "N110001255917", // TODO 确认是否修改为配置项
			})
			if err != nil {
				zap.L().Error("merchant package open failed", zap.Error(err), zap.String("wallet_id", w.ID))
				continue
			}

			if mResult.Status != kfpay.StatusSuccess && mResult.ErrorCode != kfpay.ErrorCodePlanAlreadyEnabled {
				zap.L().Error("merchant package open failed", zap.Error(err),
					zap.Any("package open result", mResult),
					zap.String("wallet_id", w.ID))
				continue
			}

			w.SetStatus(wallet.KfpayStatusOpen)
			w.SetSecMerchantID(&result.MerchantDetail.SecMerchantId)
			err = p.walletRepo.UpdateKfpayWallet(ctx, w)
			if err != nil {
				zap.L().Error("update kfpay wallet failed", zap.Error(err), zap.String("wallet id", w.ID))
				continue
			}
			// 重置失败次数
			_ = p.walletRepo.ResetKfpayVerifyBankFailedTimes(ctx, w.UserID())
		} else if result.OrderStatus == kfpay.AddSecondMerchantStatusRejected { // 审核失败
			w.SetStatus(wallet.KfpayStatusFail)
			err = p.walletRepo.UpdateKfpayWallet(ctx, w)
			if err != nil {
				zap.L().Error("query second merchant failed", zap.Error(err),
					zap.Any("result", result), zap.String("wallet_id", w.ID))
				continue
			}
		}
	}

	// ---------- 定时任务逻辑结束 ----------

	zap.L().Info("end kfpay wallet check job", zap.Duration("cost", time.Now().Sub(startTime)))
}
