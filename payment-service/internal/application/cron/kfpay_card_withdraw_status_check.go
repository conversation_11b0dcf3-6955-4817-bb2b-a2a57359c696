package cron

import (
	"cnb.cool/1024hub/kfpay-go-sdk"
	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/query"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"context"
	"errors"
	"github.com/bsm/redislock"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"time"
)

var _ cron.Job = (*KfpayCardWithdrawStatusCheckCron)(nil)
var kfpayCardWithdrawStatusCheckDistributionLockKey = "cron:payment:kfpayCardWithdrawStatusDist"

type KfpayCardWithdrawStatusCheckCron struct {
	parentContext context.Context
	walletRepo    wallet.Repo
	kfpayClient   *kfpay.Client
	lock          *redislock.Client
}

func NewKfpayCardWithdrawStatusCheckCron(parentContext context.Context, walletRepo wallet.Repo, kfpayClient *kfpay.Client, lock *redislock.Client) *KfpayCardWithdrawStatusCheckCron {
	return &KfpayCardWithdrawStatusCheckCron{parentContext: parentContext, walletRepo: walletRepo, kfpayClient: kfpayClient, lock: lock}
}

func (p *KfpayCardWithdrawStatusCheckCron) Run() {
	ctx, cancel := context.WithTimeout(p.parentContext, time.Minute-time.Second)
	defer cancel()

	startTime := time.Now()
	zap.L().Info("start kfpay wallet check job", zap.Time("start_time", startTime))

	// 加分布式锁
	lock, err := p.lock.Obtain(ctx, kfpayCardWithdrawStatusCheckDistributionLockKey, 1*time.Minute, &redislock.Options{})
	if err != nil && !errors.Is(err, redislock.ErrNotObtained) {
		zap.L().Error("obtain lock failed", zap.Error(err))
		return
	} else if errors.Is(err, redislock.ErrNotObtained) {
		return
	}
	defer func() {
		err := lock.Release(ctx)
		if err != nil {
			zap.L().Error("release lock failed", zap.Error(err))
		}
	}()

	// ---------- 定时任务逻辑开始 ----------

	// 获取未审核的提现卡
	kfpayCards, err := p.walletRepo.BatchGetKfpayReviewingBankCards(ctx, 50)
	for _, w := range kfpayCards {
		kfpayWallet, err := p.walletRepo.GetKfpayWalletByUserID(ctx, w.UserID())
		if err != nil {
			zap.L().Error("get kfpay wallet failed", zap.Error(err))
			continue
		}
		if kfpayWallet == nil {
			zap.L().Error("get kfpay wallet is nil")
			continue
		}
		// 获取钱包状态
		result, err := p.kfpayClient.QuerySecondMerchant(ctx, &kfpay.QuerySecondMerchantCmd{
			MerchantProperty: kfpay.MerchantPropertyPerson,
			CertNo:           kfpayWallet.Card(),
			OrderNo:          kfpayWallet.OrderNo(),
		})
		if err != nil {
			zap.L().Error("query second merchant failed", zap.Error(err), zap.String("wallet_id", w.ID))
			continue
		}
		if result.Status != kfpay.StatusSuccess { // 调用接口是否成功
			zap.L().Error("query second merchant failed", zap.Error(err),
				zap.Any("result", result), zap.String("wallet_id", w.ID))
			continue
		}
		// 乐观锁更新
		if result.OrderStatus == kfpay.AddSecondMerchantStatusApproved ||
			result.OrderStatus == kfpay.AddSecondMerchantStatusCompleted {

			// 处理数据库设置但未成功发送请求到快付通的情况
			if result.MerchantDetail.SettleBankAccount.SettleBankAccountNo == "" ||
				result.MerchantDetail.SecMerchantId == "" {
				// 响应为空或无效
				zap.L().Warn("empty second merchant detail from response", zap.Any("result", result))
				w.SetWithdrawStatusFail("银行卡处理失败，请稍后重试")
				err := p.walletRepo.UpdateKfpayBankCard(ctx, w)
				if err != nil {
					zap.L().Error("update kfpay wallet failed", zap.Error(err), zap.String("card id", w.ID), zap.String("user id", w.UserID()), zap.Any("withdraw status", wallet.WithdrawStatusActive))
				}
				continue
			}

			err = db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
				oldCard, err := p.walletRepo.GetKfpayUserWithdrawBandCardWithUserID(ctx, w.UserID())
				if err != nil {
					return err
				}
				if oldCard == nil {
					return errors.New("old card is nil")
				}

				oldCard.SetWithdrawStatus(wallet.WithdrawStatusNone)
				err = p.walletRepo.UpdateKfpayBankCard(ctx, oldCard)
				if err != nil {
					return err
				}

				w.SetWithdrawStatus(wallet.WithdrawStatusActive)
				err = p.walletRepo.UpdateKfpayBankCard(ctx, w)
				if err != nil {
					return err
				}
				return nil
			})
			if err != nil {
				zap.L().Error("set kfpay withdraw status failed", zap.Error(err), zap.String("wallet_id", w.ID))
				continue
			}
		} else if result.OrderStatus == kfpay.AddSecondMerchantStatusRejected { // 审核失败
			w.SetWithdrawStatusFail(result.FailureDetails)
			err := p.walletRepo.UpdateKfpayBankCard(ctx, w)
			if err != nil {
				zap.L().Error("update kfpay wallet failed", zap.Error(err), zap.String("card id", w.ID), zap.String("user id", w.UserID()), zap.Any("withdraw status", wallet.WithdrawStatusActive))
				continue
			}
		}
	}
	// ---------- 定时任务逻辑结束 ----------

	zap.L().Info("end kfpay wallet check job", zap.Duration("cost", time.Now().Sub(startTime)))
}
