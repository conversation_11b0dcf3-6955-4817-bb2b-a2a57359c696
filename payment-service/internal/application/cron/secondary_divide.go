package cron

import (
	"context"
	"time"

	"cnb.cool/cymirror/ces-services/common/db"
	nftpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/nft"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/query"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/divide"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

var _ cron.Job = (*SecondaryDivideCron)(nil)

type SecondaryDivideCron struct {
	parentContext context.Context
	divideRepo    divide.Repo
	divideService *divide.Service
	orderRepo     order.Repo
	nftClient     nftpb.NFTServiceClient
}

func NewSecondaryDivideCron(parentContext context.Context, divideRepo divide.Repo, divideService *divide.Service, orderRepo order.Repo, nftClient nftpb.NFTServiceClient) *SecondaryDivideCron {
	return &SecondaryDivideCron{parentContext: parentContext, divideRepo: divideRepo, divideService: divideService, orderRepo: orderRepo, nftClient: nftClient}
}

func (s *SecondaryDivideCron) Run() {
	ctx := s.parentContext

	startTime := time.Now()
	zap.L().Info("start secondary divide cron job", zap.Time("start_time", startTime))
	defer func() {
		zap.L().Info("secondary divide cron job finished", zap.Duration("cost", time.Now().Sub(startTime)))
	}()

	var divideMessages []*divide.SecondaryDivideMessage
	var err error
	err = db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
		divideMessages, err = s.divideRepo.GetSecondaryDivideMessageNeedToDivideForUpdate(ctx, time.Now(), 30)
		if err != nil {
			zap.L().Error("get secondary divide messages failed", zap.Error(err))
			return err
		}

		for _, message := range divideMessages {
			message.Process()
			err = s.divideRepo.UpdateSecondaryDivideMessage(ctx, message)
			if err != nil {
				zap.L().Error("update secondary divide message failed", zap.Error(err), zap.String("message_id", message.ID))
				return err
			}
		}

		return nil
	})
	if err != nil {
		zap.L().Error("failed to get secondary divide messages", zap.Error(err))
		return
	}

	for _, message := range divideMessages {
		// 获取订单信息
		o, err := s.orderRepo.GetOrderByID(ctx, message.OrderID())
		if err != nil {
			zap.L().Error("failed to get order by id", zap.Error(err), zap.String("order_id", message.OrderID()))
			return
		}

		transferResult, err := s.nftClient.CheckIsNftTransferByOrderIDRPC(ctx, &nftpb.CheckIsNftTransferByOrderIDRPCReq{
			OrderID: o.ID,
		})
		if err != nil {
			zap.L().Error("failed to check nft transfer", zap.Error(err), zap.String("order_id", message.OrderID()))
			continue
		}
		if !transferResult.IsLogExist {
			zap.L().Warn("nft transfer not exist", zap.String("order_id", message.OrderID()))
			continue
		}

		// 调用分账服务
		divideStatus, err := s.divideService.DivideSecondaryMarketOrder(ctx, o)
		if err != nil {
			zap.L().Error("failed to divide secondary market order", zap.Error(err), zap.String("order_id", message.OrderID()))
			continue
		}

		zap.L().Info("successfully processed secondary divide message", zap.String("message_id", message.ID), zap.String("order_id", message.OrderID()), zap.String("divide_status", divideStatus.String()))

	}
}
