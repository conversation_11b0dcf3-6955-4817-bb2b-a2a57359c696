package refund

import (
	"context"
	"errors"
	"fmt"
	"time"

	"cnb.cool/1024hub/kfpay-go-sdk"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"
	"github.com/bsm/redislock"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type Service struct {
	orderRepo    order.Repo
	refundRepo   Repo
	yeepayClient *yeepay.Client
	kfpayClient  *kfpay.Client
	walletRepo   wallet.Repo
	lock         *redislock.Client
}

func NewService(orderRepo order.Repo, refundRepo Repo, yeepayClient *yeepay.Client, kfpayClient *kfpay.Client, walletRepo wallet.Repo, lock *redislock.Client) *Service {
	return &Service{orderRepo: orderRepo, refundRepo: refundRepo, yeepayClient: yeepayClient, kfpayClient: kfpayClient, walletRepo: walletRepo, lock: lock}
}

type RefundCmd struct {
	OrderID         string
	RefundRequestNo string
	RefundAmount    decimal.Decimal
	Remark          string
}

func (r *RefundCmd) Check() error {
	if r.OrderID == "" {
		return status.Error(codes.InvalidArgument, "order id is invalid")
	}
	if r.RefundRequestNo == "" {
		return status.Error(codes.InvalidArgument, "refund request no is invalid")
	}
	if r.RefundAmount.LessThanOrEqual(decimal.Zero) {
		return status.Error(codes.InvalidArgument, "refund amount is invalid")
	}
	return nil
}

// paymentOrderExpiredDistributionLockKey 支付订单过期分账锁定 key: payment:order:expired:lock:{req_no}
var refundLockKey = "payment:refund:lock:%s"

func (s *Service) Refund(ctx context.Context, cmd *RefundCmd) (Status, error) {
	// 加分布式锁
	lock, err := s.lock.Obtain(ctx, fmt.Sprintf(refundLockKey, cmd.RefundRequestNo), time.Minute, &redislock.Options{})
	if err != nil && !errors.Is(err, redislock.ErrNotObtained) {
		zap.L().Error("refund obtain lock failed", zap.Error(err))
		return StatusFail, status.Error(codes.FailedPrecondition, "refund obtain lock failed")
	} else if errors.Is(err, redislock.ErrNotObtained) {
		return StatusFail, status.Error(codes.Aborted, "refund lock not obtained")
	}
	defer func() {
		err := lock.Release(context.Background())
		if err != nil {
			zap.L().Error("refund release lock failed", zap.Error(err))
		}
	}()

	if err := cmd.Check(); err != nil {
		return StatusFail, err
	}

	// 获取订单检查是否可以退款
	o, err := s.orderRepo.GetOrderByID(ctx, cmd.OrderID)
	if err != nil {
		return StatusFail, err
	}

	// 根据流水号ID检查参数是否匹配
	r, err := s.refundRepo.GetRefundByRefundReqNo(ctx, cmd.RefundRequestNo)
	if err != nil {
		return StatusFail, err
	}

	if r == nil {
		r = NewRefund(o.ID, cmd.RefundAmount, cmd.Remark, cmd.RefundRequestNo)
		err = s.refundRepo.CreateRefund(ctx, r)
		if err != nil {
			return StatusFail, err
		}
	}
	if r.Status() != StatusProcessing {
		if r.OrderID() != o.ID {
			return StatusFail, status.Error(codes.InvalidArgument, "refund order id is invalid")
		}
		return r.status, nil
	}

	// 获取已退款金额
	refunds, err := s.refundRepo.GetRefundsByOrderID(ctx, cmd.OrderID)
	if err != nil {
		return StatusFail, err
	}
	totalRefundAmount := decimal.Zero
	for _, r := range refunds {
		totalRefundAmount = totalRefundAmount.Add(r.RefundAmount())
	}
	if err = o.CanRefund(totalRefundAmount); err != nil {
		return StatusFail, err
	}

	switch o.PayType() {
	case order.PayTypeYeePayQuick, order.PayTypeYeePayWallet:
		err = s.yeepayRefund(ctx, r, o)
		if err != nil {
			return StatusFail, err
		}
	//case order.PayTypeKfpayBalance, order.PayTypeKfpayBankCard:
	case order.PayTypeKfpayBalance:
		err = s.kfpayTransferRefund(ctx, r, o) // TODO: 完善退款表，增加userid
		if err != nil {
			return StatusFail, err
		}
	default:
		return StatusFail, status.Error(codes.Unimplemented, "not implement")
	}

	if r.status == StatusSuccess {
		err = s.refundRepo.UpdateRefund(ctx, r)
		if err != nil {
			return StatusFail, err
		}
	}

	return r.status, nil
}

func (s *Service) yeepayRefund(_ context.Context, r *Refund, _ *order.Order) error {
	refundResult, err := s.yeepayClient.Refund(&yeepay.RefundCmd{
		OrderID:         r.orderID,
		RefundRequestID: r.refundReqNo,
		RefundAmount:    r.refundAmount,
	})
	if err != nil {
		return err
	}

	if refundResult.Status == yeepay.RefundStatusSuccess {
		r.SetStatus(StatusSuccess)
	} else if refundResult.Status == yeepay.RefundStatusProcessing {
		return nil // 处理中，不需要再次发起
	} else {
		zap.L().Warn("yeepay refund failed", zap.String("order id", r.orderID), zap.String("status", refundResult.Status.String()))
		r.SetStatus(StatusFail)
	}

	return nil
}

func (s *Service) kfpayTransferRefund(ctx context.Context, r *Refund, o *order.Order) error {
	w, err := s.walletRepo.GetKfpayWalletByUserID(ctx, o.UserID())
	if err != nil {
		return err
	}
	if !w.IsOpen() {
		return status.Error(codes.Unavailable, "kfpay wallet is not open")
	}
	// 创建退款日志
	tradeLog, err := wallet.NewKfpayTradeLogWithReqNo(wallet.KfpayTradeLogTradeTypeRefund,
		r.refundAmount, o.UserID(), "",
		fmt.Sprintf("退款订单：%s", r.orderID),
		r.refundReqNo)
	if err != nil {
		return err
	}
	err = s.walletRepo.CreateKfpayTradeLog(ctx, tradeLog)
	if err != nil {
		return err
	}

	cmd := kfpay.TransferCmd{
		SecMerchantId:     s.kfpayClient.SecMerchantID, // 付款方
		OrderNo:           r.refundReqNo,
		PayeeMerchantId:   *w.SecMerchantID(), // 收款方
		PayeeMerchantName: w.RealName(),
		TradeName:         "退款",
		TradeTime:         r.CreatedAt(),
		Amount:            r.refundAmount,
		Currency:          kfpay.CurrencyTypeCNY,
	}
	transferResult, err := s.kfpayClient.Transfer(ctx, &cmd)
	if err != nil {
		zap.L().Error("kfpay refund failed", zap.Error(err), zap.Any("cmd", cmd), zap.Any("result", transferResult))
		return err
	}
	zap.L().Info("kfpayTransferRefund", zap.Any("cmd", cmd), zap.Any("result", transferResult))
	if transferResult.Status == kfpay.StatusSuccess {
		r.SetStatus(StatusSuccess)
		tradeLog.SetStatus(wallet.KfpayTradeLogStatusSuccess)
	} else if transferResult.Status == kfpay.StatusFailure {
		zap.L().Warn("kfpay refund failed", zap.String("order id", r.orderID), zap.String("status", transferResult.Status.String()))
	}

	if r.status == StatusSuccess {
		err = s.walletRepo.UpdateKfpayTradeLog(ctx, tradeLog)
		if err != nil {
			return err
		}
	}

	return err
}

// MatchEngineProcessRefund 处理撮合引擎退款退差价
func (s *Service) MatchEngineProcessRefund(ctx context.Context, paymentOrder *order.Order, takerMarketType order.MarketType, refund decimal.Decimal, refundRequestNo string) error {
	var remark string
	switch takerMarketType {
	case order.MarketTypeCancelled:
		remark = "撮合引擎撤单退款"
	case order.MarketTypeLimit:
		remark = "撮合引擎吃单退款"
	case order.MarketTypeMarket:
		remark = "撮合引擎吃单退款"
	}
	_, err := s.Refund(ctx, &RefundCmd{
		OrderID:         paymentOrder.ID,
		RefundRequestNo: refundRequestNo,
		RefundAmount:    refund,
		Remark:          remark,
	})
	if err != nil {
		return err
	}
	return nil
}
