package refund

import (
	paypb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"
)

var (
	// StatusProcessing 处理中
	StatusProcessing = Status{value: 1}
	// StatusSuccess 成功
	StatusSuccess = Status{value: 2}
	// StatusFail 失败
	StatusFail = Status{value: 3}
)

type Status struct {
	value int16
}

func (s Status) Int16() int16 {
	return s.value
}

func (s Status) ToRefundPB() paypb.RefundRPCResp_RefundStatus {
	switch s.value {
	case StatusSuccess.value:
		return paypb.RefundRPCResp_SUCCESS
	case StatusFail.value:
		return paypb.RefundRPCResp_FAIL
	case StatusProcessing.value:
		return paypb.RefundRPCResp_PROCESSING
	default:
		return paypb.RefundRPCResp_PROCESSING
	}
}

func NewStatusFromYeepayResult(result *yeepay.RefundResult) Status {
	switch result.Status {
	case yeepay.RefundStatusSuccess:
		return StatusSuccess
	case yeepay.RefundStatusFail:
		return StatusFail
	default:
		return StatusProcessing
	}
}

func NewStatusFromInt16(status int16) Status {
	switch status {
	case 1:
		return StatusProcessing
	case 2:
		return StatusSuccess
	case 3:
		return StatusFail
	default:
		return StatusProcessing
	}
}
