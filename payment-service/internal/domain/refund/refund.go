package refund

import (
	"cnb.cool/cymirror/ces-services/common/db/optimisticlock"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/model"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"time"
)

var _ optimisticlock.Versioned = (*Refund)(nil)

type Refund struct {
	ID           string                 // id
	orderID      string                 // 订单id
	refundAmount decimal.Decimal        // 退款金额
	status       Status                 // 状态
	createdAt    time.Time              // 创建时间
	updatedAt    time.Time              // 更新时间
	version      optimisticlock.Version // 乐观锁
	remark       string                 // 备注
	refundReqNo  string                 // 退款请求号
}

func (r *Refund) OrderID() string {
	return r.orderID
}

func (r *Refund) RefundAmount() decimal.Decimal {
	return r.refundAmount
}

func (r *Refund) CreatedAt() time.Time {
	return r.createdAt
}

func (r *Refund) UpdatedAt() time.Time {
	return r.updatedAt
}

func (r *Refund) Remark() string {
	return r.remark
}

func (r *Refund) RefundReqNo() string {
	return r.refundReqNo
}

func (r *Refund) Status() Status {
	return r.status
}

func (r *Refund) Version() optimisticlock.Version {
	return r.version
}

func (r *Refund) SetStatus(status Status) {
	r.status = status
}

func (r *Refund) SetVersion(v int64) {
	r.version = optimisticlock.Version{
		Int64: v,
		Valid: true,
	}
}

func (r *Refund) ToModel() *model.PaymentPayRefund {
	return &model.PaymentPayRefund{
		ID:           r.ID,
		OrderID:      r.orderID,
		RefundAmount: r.refundAmount,
		Status:       r.status.Int16(),
		CreatedAt:    r.createdAt,
		UpdatedAt:    r.updatedAt,
		DeletedAt:    gorm.DeletedAt{},
		Version:      r.version,
		Remark:       r.remark,
		RefundReqNo:  r.refundReqNo,
	}
}

func NewRefundFromModel(m *model.PaymentPayRefund) *Refund {
	return &Refund{
		ID:           m.ID,
		orderID:      m.OrderID,
		refundAmount: m.RefundAmount,
		status:       NewStatusFromInt16(m.Status),
		createdAt:    m.CreatedAt,
		updatedAt:    m.UpdatedAt,
		version:      m.Version,
		remark:       m.Remark,
		refundReqNo:  m.RefundReqNo,
	}
}

func NewRefund(orderID string, refundAmount decimal.Decimal, remark string, refundReqNo string) *Refund {
	return &Refund{
		ID:           "",
		orderID:      orderID,
		refundAmount: refundAmount,
		status:       StatusProcessing,
		createdAt:    time.Time{},
		updatedAt:    time.Time{},
		version:      optimisticlock.Version{},
		remark:       remark,
		refundReqNo:  refundReqNo,
	}
}
