package refund

import (
	"context"
)

type Repo interface {
	// CreateRefund 创建退款记录
	CreateRefund(ctx context.Context, refund *Refund) error
	// UpdateRefund 更新退款记录
	UpdateRefund(ctx context.Context, refund *Refund) error
	// GetRefundByRefundReqNo 根据流水号ID获取退款记录
	GetRefundByRefundReqNo(ctx context.Context, refundReqNo string) (*Refund, error)
	// GetRefundsByOrderID 根据订单ID获取退款记录
	GetRefundsByOrderID(ctx context.Context, orderID string) ([]*Refund, error)
}
