package domain

import (
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/divide"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/notify"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/refund"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"github.com/google/wire"
)

// DomainProviderSet domain providers
var DomainProviderSet = wire.NewSet(
	order.NewOrderService,
	divide.NewService,
	wallet.NewService,
	notify.NewService,
	refund.NewService,
	pay.NewService,
)
