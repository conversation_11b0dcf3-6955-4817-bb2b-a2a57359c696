package divide

import (
	paypb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"
)

var (
	// StatusNoDivide 不分账
	StatusNoDivide = Status{value: 1}
	// StatusWaitDivide 待分账
	StatusWaitDivide = Status{value: 2}
	// StatusSuccess 分账成功
	StatusSuccess = Status{value: 3}
	// StatusFailed 分账失败
	StatusFailed = Status{value: 4}
	// StatusDividing 分账中
	StatusDividing = Status{value: 5}
)

type Status struct {
	value int16
}

func NewDivideStatusFromOrderType(orderType order.Type) Status {
	switch orderType {
	case order.TypePrimaryMarket:
		return StatusNoDivide
	case order.TypeSecondaryMarket:
		return StatusWaitDivide
	default:
		return StatusNoDivide
	}
}

func NewDivideStatus(value int16) Status {
	switch value {
	case StatusNoDivide.value:
		return StatusNoDivide
	case StatusWaitDivide.value:
		return StatusWaitDivide
	case StatusSuccess.value:
		return StatusSuccess
	case StatusFailed.value:
		return StatusFailed
	case StatusDividing.value:
		return StatusDividing
	default:
		return StatusNoDivide
	}
}

func (d Status) Int16() int16 {
	return d.value
}

func (d Status) String() string {
	switch d.value {
	case StatusNoDivide.value:
		return "no_divide"
	case StatusWaitDivide.value:
		return "wait_divide"
	case StatusSuccess.value:
		return "success"
	case StatusFailed.value:
		return "failed"
	case StatusDividing.value:
		return "dividing"
	default:
		return "no_divide"

	}
}

func (d Status) ToDividePB() paypb.DivideRPCResp_DivideStatus {
	switch d.value {
	case StatusSuccess.value:
		return paypb.DivideRPCResp_SUCCESS
	case StatusFailed.value:
		return paypb.DivideRPCResp_FAIL
	case StatusDividing.value:
		return paypb.DivideRPCResp_PROCESSING
	default:
		return paypb.DivideRPCResp_PROCESSING
	}
}

var (
	// MemberTypeProjectSide 项目方
	MemberTypeProjectSide = MemberType{value: 1}
	// MemberTypeSellerSide 卖家用户
	MemberTypeSellerSide = MemberType{value: 2}
)

type MemberType struct {
	value int16
}

func (m MemberType) Int16() int16 {
	return m.value
}

func NewMemberTypeFromInt16(value int16) MemberType {
	switch value {
	case 1:
		return MemberTypeProjectSide
	case 2:
		return MemberTypeSellerSide
	default:
		return MemberTypeProjectSide
	}
}

func (m MemberType) ToYeepayMemberType() yeepay.DivideType {
	switch m {
	case MemberTypeProjectSide:
		return yeepay.DivideTypeMerchant2Merchant
	case MemberTypeSellerSide:
		return yeepay.DivideTypeMerchant2Member
	default:
		return yeepay.DivideTypeMerchant2Merchant
	}
}

var (
	// TradeTypeSecondaryMarket 二级市场交易
	TradeTypeSecondaryMarket = TradeType{value: 1}
	// TradeTypeFlashSale 闪购交易
	TradeTypeFlashSale = TradeType{value: 2}
)

type TradeType struct {
	value int16
}

func NewTradeTypeFromInt16(value int16) TradeType {
	switch value {
	case 1:
		return TradeTypeSecondaryMarket
	case 2:
		return TradeTypeFlashSale
	default:
		return TradeTypeSecondaryMarket
	}
}
func (t TradeType) Int16() int16 {
	return t.value
}
