package divide

import (
	"cnb.cool/cymirror/ces-services/common/db/optimisticlock"
	"github.com/shopspring/decimal"
	"time"
)

// Secondary 二级市场交易额外分账
type Secondary struct {
	ID                   string                 // id
	createdAt            time.Time              // 创建时间
	updatedAt            time.Time              // 更新时间
	version              optimisticlock.Version // 乐观锁
	recipientDescription string                 // 分账接收方描述
	projectID            string                 // 项目id
	revenueShare         decimal.Decimal        // 分成比例如：0.88
}

func (p *Secondary) CreatedAt() time.Time {
	return p.createdAt
}

func (p *Secondary) UpdateAt() time.Time {
	return p.updatedAt
}

func (p *Secondary) Version() optimisticlock.Version {
	return p.version
}

func (p *Secondary) RecipientDescription() string {
	return p.recipientDescription
}

func (p *Secondary) ProjectID() string {
	return p.projectID
}

func (p *Secondary) RevenueShare() decimal.Decimal {
	return p.revenueShare
}
