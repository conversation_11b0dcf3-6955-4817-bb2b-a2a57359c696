package divide

import (
	"context"
	"fmt"
	"sync/atomic"
	"time"

	"cnb.cool/1024hub/kfpay-go-sdk"
	"cnb.cool/cymirror/ces-services/common/server"

	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/pay"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type Service struct {
	payRepo      pay.Repo
	orderRepo    order.Repo
	walletRepo   wallet.Repo
	yeepayClient *yeepay.Client
	divideRepo   Repo
	kfpayClient  *kfpay.Client
}

func NewService(payRepo pay.Repo, orderRepo order.Repo, walletRepo wallet.Repo, yeepayClient *yeepay.Client, divideRepo Repo, kfpayClient *kfpay.Client) *Service {
	return &Service{payRepo: payRepo, orderRepo: orderRepo, walletRepo: walletRepo, yeepayClient: yeepayClient, divideRepo: divideRepo, kfpayClient: kfpayClient}
}

func (s *Service) DivideSecondaryMarketOrder(ctx context.Context, o *order.Order) (Status, error) {
	// 获取订单
	o, err := s.orderRepo.GetOrderByID(ctx, o.ID)
	if err != nil {
		return StatusFailed, err
	}

	// 获取二级订单详情
	secondaryExtras, err := s.orderRepo.GetSecondaryExtraByOrderID(ctx, o.ID)
	if err != nil {
		return StatusFailed, err
	}

	// 获取二级市场分账消息
	secondaryDivideMessage, err := s.divideRepo.GetSecondaryDivideMessageByOrderID(ctx, o.ID)
	if err != nil {
		return StatusFailed, err
	}
	if secondaryDivideMessage == nil {
		zap.L().Warn("secondaryDivideMessage not found", zap.String("orderID", o.ID))
		return StatusFailed, status.Error(codes.NotFound, "secondaryDivideMessage not found")
	}

	// 获取这笔订单涉及到用户的钱包
	secondaryExtraSellerUserIDs := lo.Map(secondaryExtras, func(item *order.SecondaryExtra, _ int) string {
		return item.SellerID()
	})
	wallets, err := s.walletRepo.GetWalletsByUserIDsAndPayType(ctx, secondaryExtraSellerUserIDs, o.PayType())
	if err != nil {
		return StatusFailed, err
	}
	sellerUserID2WalletMap := lo.SliceToMap(wallets, func(item wallet.Wallet) (string, wallet.Wallet) {
		return item.UserID(), item
	})

	requestNo := secondaryDivideMessage.divideReqNo                              // 分账请求号
	allUserSideAmount := decimal.Zero                                            // 所有属于用户的分账金额
	userSideMemberID2DivideMap := make(map[string]*Divide, len(secondaryExtras)) // 申请分账信息 key: memberID
	// 多个用户分账
	for _, secondaryExtra := range secondaryExtras {
		wallet := sellerUserID2WalletMap[secondaryExtra.SellerID()]
		// 获取出售用户钱包商户id
		if wallet == nil || !wallet.IsOpen() {
			zap.L().Error("wallet is not open", zap.Any("wallet", wallet))
			return StatusFailed, server.InternalStatus
		}
		// 用户分账金额 = 藏品金额 * 0.95
		userSideAmount := secondaryExtra.Price().Mul(decimal.NewFromInt(95)).Div(decimal.NewFromInt(100))
		userSideAmount = userSideAmount.RoundDown(2).Round(2) // 向下取整到两位小数
		// 针对金刚经项目修改分账比例，百分之94
		if secondaryExtra.ProjectID() == "2f07d61d-b758-4a29-8418-1a1b06f22bf8" {
			userSideAmount = secondaryExtra.Price().Mul(decimal.NewFromInt(94)).Div(decimal.NewFromInt(100))
			userSideAmount = userSideAmount.RoundDown(2).Round(2) // 向下取整到两位小数
		}
		allUserSideAmount = allUserSideAmount.Add(userSideAmount) // 项目侧分账金额 = 可分账金额 - 所有用户分账金额

		// 用户侧分账
		if d, ok := userSideMemberID2DivideMap[wallet.SecondaryMerchantID()]; ok { // 开通钱包memberID绝对不为空
			d.AddAmount(userSideAmount) // 用户分账存在，直接增加他应该得到的金额
		} else {                         // 用户分账不存在，则创建
			if userSideAmount.IsZero() { // 如果分账金额为0，则跳过
				continue
			}
			userSideDivide, err := NewMemberSideDivide(requestNo, wallet, o, userSideAmount)
			if err != nil {
				return StatusFailed, err
			}
			userSideMemberID2DivideMap[wallet.SecondaryMerchantID()] = userSideDivide // 开通钱包memberID绝对不为空
		}
	}

	// 项目侧分账
	// 项目方分账金额 = 剩余可分账金额 - 用户分账金额
	unSplitAmount := o.Price()
	projectSideAmount := unSplitAmount.Sub(allUserSideAmount)
	projectSideDivide, err := NewProjectSideDivide(requestNo, o.ID, projectSideAmount)
	if err != nil {
		return StatusFailed, err
	}

	// 分账domain
	divides := make([]*Divide, 0, len(userSideMemberID2DivideMap)+1)
	divides = append(divides, projectSideDivide)
	for _, d := range userSideMemberID2DivideMap {
		divides = append(divides, d)
	}

	var divideStatus Status
	switch o.PayType() {
	case order.PayTypeYeePayQuick, order.PayTypeYeePayWallet:
		divideStatus, err = s.YeepayDivide(ctx, divides)
		if err != nil {
			return StatusFailed, err
		}
	//case order.PayTypeKfpayBalance, order.PayTypeKfpayBankCard:
	case order.PayTypeKfpayBalance:
		divideStatus, err = s.KfpayDivide(ctx, divides, sellerUserID2WalletMap)
		if err != nil {
			return StatusFailed, err
		}
	default:
		return StatusFailed, status.Error(codes.Unimplemented, "not implement")
	}

	// 更新分账记录状态 TODO:
	err = s.divideRepo.UpdateDivideStatus(ctx, requestNo, divideStatus)
	if err != nil {
		return divideStatus, err
	}

	if divideStatus == StatusSuccess {
		_, err = s.divideRepo.DeleteSecondaryDivideMessageByOrderID(ctx, o.ID)
		if err != nil {
			return divideStatus, err
		}
	}
	return divideStatus, nil
}

func (s *Service) MatchEngineProcessDivideAndPay(ctx context.Context, buyerUserId, sellerUserId, tradeID string, paymentOrder *order.Order, price decimal.Decimal, requestNo string) error {

	// 获取卖家钱包
	kfWallet, err := s.walletRepo.GetKfpayWalletByUserID(ctx, sellerUserId)
	if err != nil {
		return err
	}
	if kfWallet == nil || !kfWallet.IsOpen() {
		return status.Error(codes.Unavailable, "wallet is not open")
	}

	// 用户分账金额 = 藏品金额 * 0.99
	userSideAmount := price.Mul(decimal.NewFromInt(95)).Div(decimal.NewFromInt(100)).
		RoundDown(2).Round(2) // 向下取整到两位小数
	// 用户挂了超小金额的单时不进行用户侧分账
	userSideDivide := new(Divide)
	userSideDivide = nil
	// todo: 配置项目分账比例
	if userSideAmount.GreaterThan(decimal.Zero) && userSideAmount.Mod(decimal.RequireFromString("0.01")).Equal(decimal.Zero) {
		// 创建用户分账记录
		userSideDivide, err = NewMemberSideDivideFlashSale(requestNo, kfWallet, paymentOrder, userSideAmount)
		if err != nil {
			return err
		}
	}
	// 项目侧分账
	projectSideAmount := price.Sub(userSideAmount)
	projectSideDivide, err := NewProjectSideDivideFlashSale(requestNo, paymentOrder.ID, projectSideAmount)
	if err != nil {
		return err
	}

	// 进行分账(同时进行幂等校验)
	divides := []*Divide{projectSideDivide}
	if userSideDivide != nil {
		divides = append([]*Divide{userSideDivide}, divides...)
	}
	divideStatus, err := s.KfpayDivide(ctx,
		divides,
		map[string]wallet.Wallet{
			sellerUserId: kfWallet,
		})
	if err != nil {
		return err
	}

	// 更新分账记录状态(这里好像只会更新项目方的状态,而且分账失败也不会更新)
	err = s.divideRepo.UpdateDivideStatus(ctx, requestNo, divideStatus)
	if err != nil {
		return err
	}

	return nil
}

func (s *Service) YeepayDivide(ctx context.Context, divides []*Divide) (Status, error) {
	// 前置检查
	err := s.preCheckDivide(ctx, divides, wallet.TypeYeepay)
	if err != nil {
		return StatusNoDivide, err
	}
	orderID := divides[0].OrderID()
	requestNo := divides[0].RequestNo()

	// TODO:多个支付商
	yeepayDivides := make([]*yeepay.Divide, 0, len(divides))
	for _, d := range divides {
		yeepayDivides = append(yeepayDivides, d.ToYeepayDivideInfra(s.yeepayClient.MerchantNo()))
	}

	// 申请分账
	divideResult, err := s.yeepayClient.ApplyDivide(&yeepay.ApplyDivideCmd{
		OrderID:         orderID,
		DivideRequestID: requestNo,
		Divide:          yeepayDivides,
	})
	if err != nil {
		return StatusNoDivide, err
	}
	divideStatus := StatusDividing
	if divideResult.Status == yeepay.DivideStatusFail {
		divideStatus = StatusFailed
		zap.L().Error("apply divide failed",
			zap.String("status", divideResult.Status.String()),
			zap.String("order_id", orderID),
		)
	} else if divideResult.Status == yeepay.DivideStatusSuccess {
		divideStatus = StatusSuccess
	}

	return divideStatus, nil
}

func (s *Service) KfpayDivide(ctx context.Context, divides []*Divide,
	userID2WalletMap map[string]wallet.Wallet) (Status, error) {
	// 前置检查
	err := s.preCheckDivide(ctx, divides, wallet.TypeKfpay)
	if err != nil {
		return StatusNoDivide, err
	}

	errGroup, _ := errgroup.WithContext(ctx)
	var successCount atomic.Int32
	for _, d := range divides {
		if d.MemberType() == MemberTypeProjectSide {
			continue
		}
		zap.L().Info("start divide",
			zap.Any("d.userID", d.userID), zap.Any("map", userID2WalletMap),
			zap.String("d.requestNo", d.requestNo),
			zap.String("order id", d.orderID),
			zap.String("d.id", d.ID),
		)
		w := userID2WalletMap[d.userID].(*wallet.Kfpay)

		errGroup.Go(func() error {
			// 创建收款日志
			tradeLog, err := wallet.NewKfpayTradeLogWithReqNo(wallet.KfpayTradeLogTradeTypeCollection,
				d.amount, d.userID, "", fmt.Sprintf("分账订单：%s", d.orderID),
				d.requestNo)
			if err != nil {
				zap.L().Error("create kfpay trade log failed", zap.Error(err))
				return err
			}
			zap.L().Info("create kfpay trade log", zap.Any("tradeLog", tradeLog))
			err = s.walletRepo.CreateKfpayTradeLog(ctx, tradeLog)
			if err != nil {
				return err
			}
			defer func() {
				if tradeLog.Status() == wallet.KfpayTradeLogStatusProcessing {
					return
				}
				if err := s.walletRepo.UpdateKfpayTradeLog(ctx, tradeLog); err != nil {
					zap.L().Error("update wallet trade log failed", zap.Error(err), zap.String("order id", d.orderID))
				}
			}()

			cmd := &kfpay.TransferCmd{
				SecMerchantId:     s.kfpayClient.SecMerchantID,
				OrderNo:           d.requestNo,
				PayeeMerchantId:   w.SecondaryMerchantID(),
				PayeeMerchantName: w.RealName(),
				TradeName:         "分账",
				TradeTime:         time.Now(),
				Amount:            d.amount,
				Currency:          kfpay.CurrencyTypeCNY,
			}
			transfer, err := s.kfpayClient.Transfer(ctx, cmd)
			if err != nil {
				zap.L().Error("Transfer failed", zap.Error(err), zap.Any("cmd", cmd), zap.Any("result", transfer))
				return err
			}
			zap.L().Info("KfpayDivide transfer result ", zap.Any("cmd", cmd), zap.Any("result", transfer))
			// 如果状态不是成功或者处理中，而且不是重复处理，则视为失败
			if !(transfer.Status == kfpay.StatusSuccess || transfer.Status == kfpay.StatusProcessing) &&
				transfer.ErrorCode != kfpay.ErrorCodeDuplicateOrderId {
				zap.L().Error("Transfer failed", zap.Error(err), zap.Any("cmd", cmd), zap.Any("result", transfer))
				return server.InternalStatus
			}
			tradeLog.SetStatus(wallet.KfpayTradeLogStatusSuccess)
			successCount.Add(1)
			return nil
		})
	}

	err = errGroup.Wait()
	if err != nil {
		return StatusDividing, err
	}

	// 一般不会修改为失败，-1为减去项目方分账
	if successCount.Load() != int32(len(divides))-1 {
		return StatusDividing, nil
	}

	return StatusSuccess, nil
}

func (s *Service) preCheckDivide(ctx context.Context, divides []*Divide, walletType wallet.Type) error {
	// 前置检查
	// 1. 分账必须有效
	if len(divides) == 0 {
		return status.Error(codes.InvalidArgument, "divides is empty")
	}
	// 2. 请求号/订单id必须一致
	if walletType == wallet.TypeYeepay {
		requestNo := divides[0].RequestNo()
		orderID := divides[0].OrderID()
		for _, d := range divides {
			if d.requestNo != requestNo || d.requestNo == "" {
				zap.L().Error("invalid request no", zap.String("request_no", d.requestNo), zap.String("order_id", d.orderID))
				return server.InternalStatus
			}
			if d.orderID != orderID || d.orderID == "" {
				zap.L().Error("invalid order id", zap.String("request_no", d.requestNo), zap.String("order_id", d.orderID))
				return server.InternalStatus
			}
		}
	}
	// 3. member id 唯一性检查
	memberID2DivideMap := lo.SliceToMap(divides, func(item *Divide) (string, *Divide) {
		return item.MemberID(), item
	})
	if len(memberID2DivideMap) != len(divides) {
		zap.L().Error("member id not unique", zap.Any("divides", divides))
		return server.InternalStatus
	}

	requestNos := lo.Map(divides, func(item *Divide, _ int) string {
		return item.RequestNo()
	})
	// 检查是否存在过分账记录
	dList, err := s.divideRepo.GetDivideByRequestNos(ctx, requestNos)
	if err != nil {
		return err
	}
	if len(dList) == 0 {
		err = s.divideRepo.CreateDivideRecords(ctx, divides)
		if err != nil {
			return err
		}
	} else {
		// 校验之前的分账记录是否已经处理过
		if len(divides) != len(dList) {
			zap.L().Warn("divide record create before, req not the same len",
				zap.Any("before record", dList),
				zap.Any("divides", divides))
			return status.Error(codes.InvalidArgument, "not same divide request")
		}
		for _, d := range divides {
			v, ok := memberID2DivideMap[d.MemberID()]
			if !ok {
				zap.L().Warn("divide record create before, req not the same memberID",
					zap.Any("before record", dList),
					zap.Any("divides", divides))
				return status.Error(codes.InvalidArgument, "not same divide request")
			}
			if v.amount != d.Amount() {
				zap.L().Warn("divide record create before, req not the same amount",
					zap.Any("before record", dList),
					zap.Any("divides", divides))
				return status.Error(codes.InvalidArgument, "not same divide request")
			}
		}
	}

	return nil
}
