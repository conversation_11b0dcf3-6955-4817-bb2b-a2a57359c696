package divide

import (
	"context"
	"time"
)

type Repo interface {
	// ---------- divide ----------

	// CreateDivideRecords 创建分账记录
	CreateDivideRecords(ctx context.Context, divides []*Divide) error
	// GetDivideFeeByOrderIDs 根据时间范围获取分账手续费
	GetDivideFeeByOrderIDs(ctx context.Context, orderIDs []string) ([]*Divide, error)
	// GetDivideByRequestNos 根据请求号获取分账记录
	GetDivideByRequestNos(ctx context.Context, requestNos []string) ([]*Divide, error)

	// UpdateDivideStatus 更新分账记录状态
	UpdateDivideStatus(ctx context.Context, requestNo string, status Status) error

	// ---------- divide ----------

	// ---------- secondary divide message ----------

	// CreateSecondaryDivideMessage 创建二级市场分账消息
	CreateSecondaryDivideMessage(ctx context.Context, divide *SecondaryDivideMessage) error

	// GetSecondaryDivideMessageNeedToDivideForUpdate 获取需要尝试的锁定记录
	GetSecondaryDivideMessageNeedToDivideForUpdate(ctx context.Context, nextTryTime time.Time, batchSize int32) ([]*SecondaryDivideMessage, error)
	// GetSecondaryDivideMessageByOrderID 根据订单ID获取二级市场分账消息
	GetSecondaryDivideMessageByOrderID(ctx context.Context, orderID string) (*SecondaryDivideMessage, error)

	// UpdateSecondaryDivideMessage 更新二级市场分账消息
	UpdateSecondaryDivideMessage(ctx context.Context, divide *SecondaryDivideMessage) error

	// DeleteSecondaryDivideMessage 删除二级市场分账消息
	DeleteSecondaryDivideMessage(ctx context.Context, id string) (int64, error)
	// DeleteSecondaryDivideMessageByOrderID 通过orderID删除二级市场分账消息
	DeleteSecondaryDivideMessageByOrderID(ctx context.Context, orderID string) (int64, error)

	// ---------- secondary divide message ----------

}
