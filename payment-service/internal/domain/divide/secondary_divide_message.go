package divide

import (
	"cnb.cool/cymirror/ces-services/common/db/optimisticlock"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/model"
	"github.com/google/uuid"
	"strings"
	"time"
)

var _ optimisticlock.Versioned = (*SecondaryDivideMessage)(nil)

type SecondaryDivideMessage struct {
	ID          string                 // id
	createdAt   time.Time              // 创建时间
	updatedAt   time.Time              // 更新时间
	version     optimisticlock.Version // 乐观锁
	orderID     string                 // 订单id
	tryCount    int32                  // 尝试次数
	nextTryTime time.Time              // 下次尝试时间
	divideReqNo string                 // 分账请求号
}

func NewSecondaryDivideMessage(orderID string) *SecondaryDivideMessage {
	return &SecondaryDivideMessage{
		ID:          "",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		version:     optimisticlock.Version{Int64: 0, Valid: true},
		orderID:     orderID,
		tryCount:    0,
		nextTryTime: time.Now().Add(time.Second * 20),
		divideReqNo: strings.ReplaceAll(uuid.New().String(), "-", ""),
	}
}

func NewSecondaryDivideMessageFromModel(model *model.PaymentSecondaryDivideMessage) *SecondaryDivideMessage {
	if model == nil {
		return nil
	}
	return &SecondaryDivideMessage{
		ID:          model.ID,
		createdAt:   model.CreatedAt,
		updatedAt:   model.UpdatedAt,
		version:     model.Version,
		orderID:     model.OrderID,
		tryCount:    model.TryCount,
		nextTryTime: model.NextTryTime,
		divideReqNo: model.DivideReqNo,
	}
}

func (s *SecondaryDivideMessage) ToModel() *model.PaymentSecondaryDivideMessage {
	return &model.PaymentSecondaryDivideMessage{
		ID:          s.ID,
		CreatedAt:   s.createdAt,
		UpdatedAt:   s.updatedAt,
		Version:     s.version,
		OrderID:     s.orderID,
		TryCount:    s.tryCount,
		NextTryTime: s.nextTryTime,
		DivideReqNo: s.divideReqNo,
	}
}

func (s *SecondaryDivideMessage) Version() optimisticlock.Version {
	return s.version
}

func (s *SecondaryDivideMessage) SetVersion(v int64) {
	s.version = optimisticlock.Version{Int64: v, Valid: true}
}

// OrderID 获取订单ID
func (s *SecondaryDivideMessage) OrderID() string {
	return s.orderID
}

// Process 处理锁定记录
func (s *SecondaryDivideMessage) Process() {
	s.tryCount++
	s.nextTryTime = time.Now().Add(time.Second * 30)
}
