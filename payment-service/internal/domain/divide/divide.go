package divide

import (
	"strings"
	"time"

	"cnb.cool/cymirror/ces-services/common/db/optimisticlock"
	"cnb.cool/cymirror/ces-services/common/server"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/model"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/wallet"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

type Divide struct {
	ID           string                 // id
	memberID     string                 // 分账用户 Member对象 的 id；若是商户本身时，传入0；
	memberType   MemberType             // 商户类型[1:项目方, 2:卖家用户]；
	requestNo    string                 // 分账流水号,幂等；
	orderID      string                 // 订单id；
	amount       decimal.Decimal        // 分账金额；
	createdAt    time.Time              // 创建时间；
	updatedAt    time.Time              // 更新时间；
	version      optimisticlock.Version // 乐观锁；
	divideStatus Status                 // 分账状态
	//orderSecondaryExtraID string                 // 订单二级额外信息表id
	tradeType TradeType // 交易类型
	userID    string    // TODO 用户id
}

// NewProjectSideDivide 二级市场项目侧分账
func NewProjectSideDivide(requestNo, orderID string, amount decimal.Decimal) (*Divide, error) {
	if amount.Exponent() < -2 {
		zap.L().Error("amount is invalid", zap.String("amount", amount.String()), zap.Int32("exponent", amount.Exponent()))
		return nil, server.InternalStatus
	}
	return &Divide{
		memberID:     "0",
		memberType:   MemberTypeProjectSide,
		requestNo:    requestNo,
		orderID:      orderID,
		amount:       amount,
		divideStatus: StatusDividing,
		tradeType:    TradeTypeSecondaryMarket,
	}, nil
}

// NewMemberSideDivide 二级市场卖家侧分账
func NewMemberSideDivide(requestNo string, w wallet.Wallet, o *order.Order, amount decimal.Decimal) (*Divide, error) {
	if amount.LessThanOrEqual(decimal.Zero) || !amount.Mod(decimal.RequireFromString("0.01")).Equal(decimal.Zero) {
		zap.L().Error("amount is invalid", zap.String("amount", amount.String()))
		return nil, server.InternalStatus
	}
	d := &Divide{
		memberID:     w.SecondaryMerchantID(),
		memberType:   MemberTypeSellerSide,
		requestNo:    "",
		orderID:      o.ID,
		amount:       amount,
		divideStatus: StatusWaitDivide,
		tradeType:    TradeTypeSecondaryMarket,
		userID:       w.UserID(),
	}
	switch o.PayType() {
	case order.PayTypeKfpayBalance:
		// TODO: 逻辑优化，现在传进来一定是uuid
		requestNo = strings.ReplaceAll(requestNo, "-", "")[:30]
		userID := strings.ReplaceAll(w.UserID(), "-", "")[:30]
		// 前置条件，使用divide message请求分账，使用生成的请求号的一半拼上userID防止重复分账
		reqNoPart := ""
		if len(requestNo) >= 15 {
			reqNoPart = requestNo[:15]
		} else {
			reqNoPart = requestNo
		}
		// userID 是长度36的uuid， 所以取前15位
		userIDPart := userID[:15]
		d.requestNo = reqNoPart + userIDPart
	default:
		d.requestNo = requestNo
	}
	return d, nil
}

// NewProjectSideDivideFlashSale 闪购项目侧分账
func NewProjectSideDivideFlashSale(requestNo, orderID string, amount decimal.Decimal) (*Divide, error) {
	if amount.Exponent() < -2 {
		zap.L().Error("amount is invalid", zap.String("amount", amount.String()), zap.Int32("exponent", amount.Exponent()))
		return nil, server.InternalStatus
	}
	return &Divide{
		memberID:     "0",
		memberType:   MemberTypeProjectSide,
		requestNo:    requestNo,
		orderID:      orderID,
		amount:       amount,
		divideStatus: StatusDividing,
		tradeType:    TradeTypeFlashSale,
	}, nil
}

// NewMemberSideDivideFlashSale 闪购卖家侧分账
func NewMemberSideDivideFlashSale(requestNo string, w wallet.Wallet, o *order.Order, amount decimal.Decimal) (*Divide, error) {
	if amount.LessThanOrEqual(decimal.Zero) || !amount.Mod(decimal.RequireFromString("0.01")).Equal(decimal.Zero) {
		zap.L().Error("amount is invalid", zap.String("amount", amount.String()))
		return nil, server.InternalStatus
	}
	d := &Divide{
		memberID:     w.SecondaryMerchantID(),
		memberType:   MemberTypeSellerSide,
		requestNo:    "",
		orderID:      o.ID,
		amount:       amount,
		divideStatus: StatusWaitDivide,
		tradeType:    TradeTypeFlashSale,
		userID:       w.UserID(),
	}
	switch o.PayType() {
	//case order.PayTypeKfpayBalance, order.PayTypeKfpayBankCard:
	case order.PayTypeKfpayBalance:
		requestNo = strings.ReplaceAll(requestNo, "-", "")[:30] // 去掉-，取前30位
		userID := strings.ReplaceAll(w.UserID(), "-", "")[:30]
		// 前置条件，使用divide message请求分账，使用生成的请求号的一半拼上userID防止重复分账
		reqNoPart := ""
		if len(requestNo) >= 15 {
			reqNoPart = requestNo[:15] // 取请求号的前15位
		} else {
			reqNoPart = requestNo
		}
		// userID 是长度36的uuid， 所以取前15位
		userIDPart := userID[:15]
		d.requestNo = reqNoPart + userIDPart
	default:
		d.requestNo = requestNo
	}
	return d, nil
}

func (d *Divide) ToModel() *model.PaymentPayDivide {
	return &model.PaymentPayDivide{
		ID:           d.ID,
		MemberID:     d.memberID,
		MemberType:   d.memberType.Int16(),
		RequestNo:    d.requestNo,
		OrderID:      d.orderID,
		Amount:       d.amount,
		CreatedAt:    d.createdAt,
		UpdatedAt:    d.updatedAt,
		Version:      d.version,
		DivideStatus: d.divideStatus.Int16(),
		TradeType:    d.tradeType.Int16(),
		UserID:       d.userID,
	}
}

func (d *Divide) MemberID() string {
	return d.memberID
}

func (d *Divide) MemberType() MemberType {
	return d.memberType
}

func (d *Divide) RequestNo() string {
	return d.requestNo
}

func (d *Divide) OrderID() string {
	return d.orderID
}

func (d *Divide) Amount() decimal.Decimal {
	return d.amount
}

func (d *Divide) CreatedAt() time.Time {
	return d.createdAt
}

func (d *Divide) UpdatedAt() time.Time {
	return d.updatedAt
}

func (d *Divide) Version() optimisticlock.Version {
	return d.version
}

func (d *Divide) DivideStatus() Status {
	return d.divideStatus
}

func (d *Divide) TradeType() TradeType {
	return d.tradeType
}

func (d *Divide) SetMemberID(memberID string) {
	d.memberID = memberID
}

func (d *Divide) SetMemberType(memberType MemberType) {
	d.memberType = memberType
}

func (d *Divide) SetRequestNo(requestNo string) {
	d.requestNo = requestNo
}

func (d *Divide) SetOrderID(orderID string) {
	d.orderID = orderID
}

func (d *Divide) SetAmount(amount decimal.Decimal) {
	d.amount = amount
}

func (d *Divide) SetDivideStatus(status Status) {
	d.divideStatus = status
}

func (d *Divide) SetTradeType(tradeType TradeType) {
	d.tradeType = tradeType
}

func (d *Divide) ToYeepayDivideInfra(merchantNo string) *yeepay.Divide {
	divide := &yeepay.Divide{
		Amount:     d.amount.Copy(),
		LedgerNo:   "",
		LedgerType: yeepay.DivideType{},
	}
	if d.memberType == MemberTypeSellerSide {
		divide.LedgerType = yeepay.DivideTypeMerchant2Member
		divide.LedgerNo = d.memberID
	} else {
		divide.LedgerType = yeepay.DivideTypeMerchant2Merchant
		divide.LedgerNo = merchantNo
	}
	return divide
}

func (d *Divide) AddAmount(amount decimal.Decimal) {
	d.amount = d.amount.Add(amount)
}

func NewDivideFromModel(m *model.PaymentPayDivide) *Divide {
	return &Divide{
		ID:           m.ID,
		memberID:     m.MemberID,
		memberType:   NewMemberTypeFromInt16(m.MemberType),
		requestNo:    m.RequestNo,
		orderID:      m.OrderID,
		amount:       m.Amount,
		createdAt:    m.CreatedAt,
		updatedAt:    m.UpdatedAt,
		version:      m.Version,
		divideStatus: NewDivideStatus(m.DivideStatus),
		userID:       m.UserID,
		tradeType:    NewTradeTypeFromInt16(m.TradeType),
	}
}
