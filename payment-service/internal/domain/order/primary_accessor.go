package order

import (
	"time"

	"github.com/shopspring/decimal"
)

func (p *PrimaryExtra) PrimaryID() string {
	return p.ID
}

func (p *PrimaryExtra) OrderID() string {
	return p.orderID
}

func (p *PrimaryExtra) ProjectID() string {
	return p.projectID
}

func (p *PrimaryExtra) NftID() string {
	return p.nftID
}

func (p *PrimaryExtra) UserID() string {
	return p.userID
}

func (p *PrimaryExtra) Price() decimal.Decimal {
	return p.price
}

func (p *PrimaryExtra) CreatedAt() time.Time {
	return p.createdAt
}

func (p *PrimaryExtra) UpdatedAt() time.Time {
	return p.updatedAt
}

func (p *PrimaryExtra) Version() int64 {
	return p.version
}
