package order

import (
	"github.com/shopspring/decimal"
	"time"
)

type OrderCriteria struct {
	UserID    *string
	ProjectID []string

	// 枚举查询
	Status    []Status
	OrderType *Type
	PayType   *PayType

	// 范围查询
	RangeField *string // 范围查询字段
	StartTime  *time.Time
	EndTime    *time.Time
	MinPrice   *decimal.Decimal
	MaxPrice   *decimal.Decimal

	// 过滤条件
	Keyword *string

	// 分页、排序
	Page      int64
	PageSize  int64
	SortField string
	IsAsc     bool
}

type SecondaryOrderCriteria struct {
	UserID   *string
	SellerID *string

	// 范围查询
	StartTime *time.Time
	EndTime   *time.Time
	MinPrice  *decimal.Decimal
	MaxPrice  *decimal.Decimal

	// 分页、排序
	Page      int64
	PageSize  int64
	SortField string
	IsAsc     bool
}
