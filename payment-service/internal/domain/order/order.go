package order

import (
	"fmt"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"strings"
	"time"

	"cnb.cool/cymirror/ces-services/common/db/optimisticlock"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/model"
	"github.com/shopspring/decimal"
)

var _ optimisticlock.Versioned = (*Order)(nil)

type Order struct {
	ID          string                 // 订单id
	status      Status                 // 订单状态
	orderNo     string                 // 订单号
	userID      string                 // 用户id
	orderType   Type                   // 订单类型
	title       string                 // 订单标题
	createdAt   time.Time              // 创建时间
	updatedAt   time.Time              // 更新时间
	version     optimisticlock.Version // 版本
	payType     PayType                // 支付类型
	payTime     *time.Time             // 支付时间
	expiredTime time.Time              // 过期时间
	imgURL      string                 // 图片链接
	price       decimal.Decimal        // 价格
	payID       *string                // 对应支付表的id
}

func NewFromModel(orderModel *model.PaymentOrder) *Order {
	if orderModel == nil {
		return nil
	}
	return &Order{
		ID:          orderModel.ID,
		status:      NewStatus(orderModel.Status),
		orderNo:     orderModel.OrderNo,
		userID:      orderModel.UserID,
		orderType:   NewType(orderModel.Type),
		title:       orderModel.Title,
		createdAt:   orderModel.CreatedAt,
		updatedAt:   orderModel.UpdatedAt,
		version:     orderModel.Version,
		payType:     NewPayType(orderModel.PayType),
		payTime:     orderModel.PayTime,
		expiredTime: orderModel.ExpiredTime,
		imgURL:      orderModel.ImgURL,
		price:       orderModel.Price,
		payID:       orderModel.PayID,
	}
}

func (o *Order) ToModel() *model.PaymentOrder {
	if o == nil {
		return nil
	}
	return &model.PaymentOrder{
		ID:          o.ID,
		Status:      o.status.Int16(),
		OrderNo:     o.orderNo,
		UserID:      o.userID,
		Type:        o.orderType.Int16(),
		Title:       o.title,
		Version:     o.version,
		PayType:     o.payType.Int16(),
		PayTime:     o.payTime,
		ExpiredTime: o.expiredTime,
		ImgURL:      o.imgURL,
		Price:       o.price,
		PayID:       o.payID,
	}
}

func (o *Order) SetOrderInfo(
	title string,
	imgURL string,
) {
	o.title = title
	o.imgURL = imgURL
}

func (o *Order) IsExpired() bool {
	return o.expiredTime.Before(time.Now())
}

func (o *Order) Cancel() error {
	if o == nil {
		return status.Error(codes.InvalidArgument, "order not found")
	}
	if !o.Status().Equal(StatusPaying) && !o.Status().Equal(StatusWaitPay) {
		return status.Error(codes.InvalidArgument, "order status can not cancel")
	}
	o.SetStatus(StatusCancel)
	return nil
}

func (o *Order) CanRefund(totalRefundAmount decimal.Decimal) error {
	if o == nil {
		return status.Error(codes.NotFound, "order not found")
	}
	if !o.status.Equal(StatusSuccess) && !o.status.Equal(StatusCancel) {
		return status.Error(codes.InvalidArgument, "order status can not refund")
	}
	if totalRefundAmount.GreaterThan(o.price) {
		return status.Error(codes.InvalidArgument, "refund amount is greater than order price")
	}

	return nil
}

func (o *Order) MakePaySuccess(payTime time.Time) error {
	if o == nil {
		return status.Error(codes.InvalidArgument, "order not found")
	}
	if !o.Status().Equal(StatusPaying) {
		return status.Error(codes.InvalidArgument, "order status not paying")
	}
	o.SetStatus(StatusSuccess)
	o.SetPayTime(payTime)
	return nil
}

func (o *Order) CanPay() error {
	if o == nil {
		return status.Error(codes.NotFound, "order not found")
	}
	// 检查订单是否过期
	if o.IsExpired() {
		return status.Error(codes.InvalidArgument, "order is expired")
	}
	// 检查订单是否为等待支付
	if !o.Status().Equal(StatusWaitPay) {
		return status.Error(codes.InvalidArgument, "order is not wait pay")
	}

	return nil
}

func (o *Order) PayOrder(t PayType, payOrderID string) error {
	if o.payType != PayTypeUnknown && t != o.payType {
		zap.L().Warn("PayOrder failed", zap.String("order_id", o.ID), zap.Int16("pay type", t.Int16()), zap.Int16("current pay type", o.payType.Int16()))
		return status.Error(codes.InvalidArgument, "pay type not match")
	}
	o.SetStatus(StatusPaying)
	o.SetPayType(t)
	o.SetPayID(payOrderID)
	return nil
}

// OrderTypeNeedDivide 订单类型是否需要分账
func (o *Order) OrderTypeNeedDivide() bool {
	return o.orderType == TypePurchaseRequest || o.orderType == TypeSecondaryMarket
}

func generateOrderNo(userID, orderID string, orderType Type) string {
	return fmt.Sprintf("%s%s%s%d", orderType.OrderNoPrefix(),
		strings.ToUpper(userID[:8]),
		strings.ToUpper(orderID[:8]),
		time.Now().Unix())
}

type OrderWithSecondaryExtra struct {
	Order  *Order
	Extras []*SecondaryExtra
}
