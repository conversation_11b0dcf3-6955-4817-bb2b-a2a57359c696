package order

import (
	"context"
	"fmt"
	"time"

	marketpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/market"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"

	"cnb.cool/cymirror/ces-services/common/rate"

	nftpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/nft"
	projectpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/project"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/model"
	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
	profilepb "cnb.cool/cymirror/ces-services/user-service/gen/proto/ces/user/profile"
	"go.uber.org/zap"
)

// 下单限流key rate:order:limit:{userID}
const redisOrderLimitKey = "rate:order:limit:%s"

type OrderService struct {
	orderRepo       Repo
	projectClient   projectpb.ProjectServiceClient
	nftClient       nftpb.NFTServiceClient
	profileClient   profilepb.ProfileServiceClient
	limiter         rate.Limiter
	secondaryClient marketpb.SecondaryMarketServiceClient
	yeepayClient    *yeepay.Client
}

func NewOrderService(orderRepo Repo, projectClient projectpb.ProjectServiceClient, nftClient nftpb.NFTServiceClient, profileClient profilepb.ProfileServiceClient, limiter rate.Limiter, secondaryClient marketpb.SecondaryMarketServiceClient) *OrderService {
	return &OrderService{orderRepo: orderRepo, projectClient: projectClient, nftClient: nftClient, profileClient: profileClient, limiter: limiter, secondaryClient: secondaryClient}
}

// GetOrderDetail 获取订单详情
func (s *OrderService) GetOrderDetail(ctx context.Context, orderID string, userID string) (*orderpb.GetOrderDetailResp, error) {

	// 获取订单主表信息
	orderInfo, err := s.orderRepo.GetOrderByID(ctx, orderID)
	if err != nil {
		return nil, fmt.Errorf("failed to get order info: %w", err)
	}
	if orderInfo == nil {
		return nil, ErrOrderNotFound
	}

	// 根据订单类型获取详细信息
	switch orderInfo.OrderType() {
	case TypePrimaryMarket:
		primaryExtra, err := s.orderRepo.GetPrimaryOrdersByOrderIDs(ctx, []string{orderInfo.ID})
		if err != nil {
			return nil, err
		}
		if len(primaryExtra) == 0 {
			return nil, ErrOrderNotFound
		}
		// 处理可能为空的 payTime 和 payID
		var payTime int64
		var payID string
		if orderInfo.PayTime() != nil {
			payTime = orderInfo.PayTime().Unix()
		}
		if orderInfo.PayID() != nil {
			payID = *orderInfo.PayID()
		}

		// 获取 Chain ID
		nftIDs := make([]string, 0)
		for _, p := range primaryExtra {
			nftIDs = append(nftIDs, p.NftID)
		}

		var chainIDMap map[string]string
		if len(nftIDs) > 0 {
			resp, err := s.nftClient.BatchGetChainIDByNFTIDRPC(ctx, &nftpb.BatchGetChainIDByNFTIDRPCReq{
				NftIDs: nftIDs,
			})
			if err != nil {
				zap.L().Error("failed to get chain ids", zap.Error(err), zap.Strings("nft_ids", nftIDs))
			} else if resp != nil {
				chainIDMap = resp.NftIDToChainID
			}
		}
		// 构建extras字段
		extras := make([]*orderpb.GetOrderDetailResp_OrderExtra, 0, len(primaryExtra))
		for _, item := range primaryExtra {
			chainID := ""
			if chainIDMap != nil {
				if id, ok := chainIDMap[item.NftID]; ok {
					chainID = id
				}
			}
			extras = append(extras, &orderpb.GetOrderDetailResp_OrderExtra{
				NftID:     item.NftID,
				ChainID:   chainID,
				UnitPrice: item.Price.String(),
				ListingID: "", // 一级市场不存在 listingID
				SellerID:  "", // 一级市场不存在 sellerID
			})
		}

		return &orderpb.GetOrderDetailResp{
			OrderID:     orderInfo.ID,
			ProjectID:   primaryExtra[0].ProjectID,
			OrderType:   orderInfo.OrderType().OrderNoPrefix(),
			Title:       orderInfo.Title(),
			Price:       orderInfo.Price().String(),
			Num:         int64(len(primaryExtra)),
			Status:      int64(orderInfo.Status().Int16()),
			OrderNo:     orderInfo.OrderNo(),
			ImgURL:      orderInfo.ImgURL(),
			Role:        0,
			PayType:     int64(orderInfo.PayType().Int16()),
			PayID:       payID,
			CreateTime:  orderInfo.CreatedAt().Unix(),
			PayTime:     payTime,
			ExpiredTime: orderInfo.ExpiredTime().Unix(),
			Extras:      extras,
		}, nil

	case TypeSecondaryMarket:
		secondaryExtra, err := s.orderRepo.GetSecondaryOrdersByOrderIDs(ctx, []string{orderInfo.ID})
		if err != nil {
			return nil, err
		}
		if len(secondaryExtra) == 0 {
			return nil, ErrOrderNotFound
		}
		// 处理可能为空的 payTime 和 payID
		var payTime int64
		var payID string
		if orderInfo.PayTime() != nil {
			payTime = orderInfo.PayTime().Unix()
		}
		if orderInfo.PayID() != nil {
			payID = *orderInfo.PayID()
		}
		// 处理买家卖家
		role := 0
		if secondaryExtra[0].SellerID == userID {
			role = 1
		}
		// 获取 Chain ID
		nftIDs := make([]string, 0)
		for _, s := range secondaryExtra {
			nftIDs = append(nftIDs, s.NftID)
		}

		var chainIDMap map[string]string
		if len(nftIDs) > 0 {
			resp, err := s.nftClient.BatchGetChainIDByNFTIDRPC(ctx, &nftpb.BatchGetChainIDByNFTIDRPCReq{
				NftIDs: nftIDs,
			})
			if err != nil {
				zap.L().Error("failed to get chain ids", zap.Error(err), zap.Strings("nft_ids", nftIDs))
			} else if resp != nil {
				chainIDMap = resp.NftIDToChainID
			}
		}

		// 构建extras字段
		extras := make([]*orderpb.GetOrderDetailResp_OrderExtra, 0, len(secondaryExtra))
		for _, item := range secondaryExtra {
			chainID := ""
			if chainIDMap != nil {
				if id, ok := chainIDMap[item.NftID]; ok {
					chainID = id
				}
			}
			extras = append(extras, &orderpb.GetOrderDetailResp_OrderExtra{
				NftID:     item.NftID,
				ChainID:   chainID,
				UnitPrice: item.Price.String(),
				ListingID: item.ListingID,
				SellerID:  item.SellerID,
			})
		}

		return &orderpb.GetOrderDetailResp{
			OrderID:     orderInfo.ID,
			ProjectID:   secondaryExtra[0].ProjectID,
			OrderType:   orderInfo.OrderType().OrderNoPrefix(),
			Title:       orderInfo.Title(),
			Price:       orderInfo.Price().String(),
			Num:         int64(len(secondaryExtra)),
			Status:      int64(orderInfo.Status().Int16()),
			OrderNo:     orderInfo.OrderNo(),
			ImgURL:      orderInfo.ImgURL(),
			Role:        int64(role),
			PayType:     int64(orderInfo.PayType().Int16()),
			PayID:       payID,
			CreateTime:  orderInfo.CreatedAt().Unix(),
			PayTime:     payTime,
			ExpiredTime: orderInfo.ExpiredTime().Unix(),
			Extras:      extras,
		}, nil
	case TypePurchaseRequest:
		return &orderpb.GetOrderDetailResp{
			OrderID:     orderInfo.ID,
			Title:       orderInfo.Title(),
			Price:       orderInfo.Price().String(),
			Status:      int64(orderInfo.Status().Int16()),
			OrderNo:     orderInfo.OrderNo(),
			ImgURL:      orderInfo.ImgURL(),
			CreateTime:  orderInfo.CreatedAt().Unix(),
			ExpiredTime: orderInfo.ExpiredTime().Unix(),
		}, nil
	default:
		return nil, ErrInvalidOrderType
	}
}

// GetMyBoughtOrders 获取我购买的订单列表
func (s *OrderService) GetMyBoughtOrders(ctx context.Context, userID string, page int64, pageSize int64) (*orderpb.GetMyOrdersResp, error) {
	// 1. 获取主订单列表（分页）
	orders, total, err := s.orderRepo.GetOrdersByUserID(ctx, userID, page, pageSize)
	if err != nil {
		return nil, err
	}

	// 2. 按订单类型分组
	primaryOrderIDs := make([]string, 0)
	secondaryOrderIDs := make([]string, 0)
	for _, o := range orders {
		if o.OrderType().Equal(TypePrimaryMarket) {
			primaryOrderIDs = append(primaryOrderIDs, o.ID)
		} else if o.OrderType().Equal(TypeSecondaryMarket) {
			secondaryOrderIDs = append(secondaryOrderIDs, o.ID)
		}
	}

	// 3. 获取一级市场订单额外信息
	var primaryOrders []*model.PaymentOrderPrimary
	if len(primaryOrderIDs) > 0 {
		primaryOrders, err = s.orderRepo.GetPrimaryOrdersByOrderIDs(ctx, primaryOrderIDs)
		if err != nil {
			return nil, err
		}
	}

	// 4. 获取二级市场订单额外信息
	var secondaryOrders []*model.PaymentOrderSecondary
	if len(secondaryOrderIDs) > 0 {
		secondaryOrders, err = s.orderRepo.GetSecondaryOrdersByOrderIDs(ctx, secondaryOrderIDs)
		if err != nil {
			return nil, err
		}
	}

	// 5. 建立订单ID到额外信息的映射
	primaryMap := make(map[string][]*model.PaymentOrderPrimary)
	for _, p := range primaryOrders {
		if _, ok := primaryMap[p.OrderID]; !ok {
			primaryMap[p.OrderID] = make([]*model.PaymentOrderPrimary, 0)
		}
		primaryMap[p.OrderID] = append(primaryMap[p.OrderID], p)
	}

	secondaryMap := make(map[string][]*model.PaymentOrderSecondary)
	for _, s := range secondaryOrders {
		if _, ok := secondaryMap[s.OrderID]; !ok {
			secondaryMap[s.OrderID] = make([]*model.PaymentOrderSecondary, 0)
		}
		secondaryMap[s.OrderID] = append(secondaryMap[s.OrderID], s)
	}

	// 6. 收集所有 NFT ID
	nftIDs := make([]string, 0)
	for _, p := range primaryOrders {
		nftIDs = append(nftIDs, p.NftID)
	}
	for _, s := range secondaryOrders {
		nftIDs = append(nftIDs, s.NftID)
	}

	// 7. 批量获取 Chain ID
	var chainIDMap map[string]string
	if len(nftIDs) > 0 {
		resp, err := s.nftClient.BatchGetChainIDByNFTIDRPC(ctx, &nftpb.BatchGetChainIDByNFTIDRPCReq{
			NftIDs: nftIDs,
		})
		if err != nil {
			zap.L().Error("failed to get chain ids", zap.Error(err), zap.Strings("nft_ids", nftIDs))
		} else if resp != nil {
			chainIDMap = resp.NftIDToChainID
		}
	}

	// 8. 构建返回结果
	var allItems []*orderpb.GetMyOrdersResp_OrderItem

	// 按照主订单的顺序处理
	for _, o := range orders {
		var payTime int64
		if o.PayTime() != nil {
			payTime = o.PayTime().UTC().UnixMilli()
		}

		// 处理一级市场订单
		if o.OrderType().Equal(TypePrimaryMarket) {
			primaryList, ok := primaryMap[o.ID]
			if !ok || len(primaryList) == 0 {
				zap.L().Warn("primary market order not found", zap.String("order_id", o.ID))
				continue
			}

			firstPrimary := primaryList[0]
			chainID := ""
			if chainIDMap != nil {
				if id, ok := chainIDMap[firstPrimary.NftID]; ok {
					chainID = id
				}
			}

			item := &orderpb.GetMyOrdersResp_OrderItem{
				OrderID:     o.ID,
				ExtraID:     firstPrimary.ID,
				NftID:       firstPrimary.NftID,
				ChainID:     chainID,
				OrderType:   o.OrderType().OrderNoPrefix(),
				Title:       o.Title(),
				Price:       o.Price().String(),
				Num:         int64(len(primaryList)),
				Status:      int64(o.Status().Int16()),
				OrderNo:     o.OrderNo(),
				ImgURL:      o.ImgURL(),
				CreateTime:  o.CreatedAt().UTC().UnixMilli(),
				PayTime:     payTime,
				Role:        0,
				ExpiredTime: o.ExpiredTime().UTC().UnixMilli(),
				PayType:     o.PayType().ToOrderPB(),
			}
			allItems = append(allItems, item)
			continue
		}

		// 处理二级市场订单
		if o.OrderType().Equal(TypeSecondaryMarket) {
			secondaryList, ok := secondaryMap[o.ID]
			if !ok || len(secondaryList) == 0 {
				zap.L().Warn("secondary market order not found", zap.String("order_id", o.ID))
				continue
			}

			firstSecondary := secondaryList[0]
			chainID := ""
			if chainIDMap != nil {
				if id, ok := chainIDMap[firstSecondary.NftID]; ok {
					chainID = id
				}
			}

			item := &orderpb.GetMyOrdersResp_OrderItem{
				OrderID:     o.ID,
				ExtraID:     firstSecondary.ID,
				NftID:       firstSecondary.NftID,
				ChainID:     chainID,
				OrderType:   o.OrderType().OrderNoPrefix(),
				Title:       o.Title(),
				Price:       o.Price().String(),
				Num:         int64(len(secondaryList)),
				Status:      int64(o.Status().Int16()),
				OrderNo:     o.OrderNo(),
				ImgURL:      o.ImgURL(),
				CreateTime:  o.CreatedAt().UTC().UnixMilli(),
				PayTime:     payTime,
				Role:        0,
				ExpiredTime: o.ExpiredTime().UTC().UnixMilli(),
				PayType:     o.PayType().ToOrderPB(),
			}
			allItems = append(allItems, item)
		}
	}
	return &orderpb.GetMyOrdersResp{
		Items: allItems,
		Total: total,
	}, nil
}

func (s *OrderService) GetMySoldOrders(ctx context.Context, userID string, page int64, pageSize int64) (*orderpb.GetMyOrdersResp, error) {
	// 售出订单只存在二级市场
	// 1. 获取二级市场订单额外信息
	secondaryOrders, total, err := s.orderRepo.GetSecondaryOrderListAsSellerWithStatus(ctx, int32(page), int32(pageSize), userID, StatusSuccess)
	if err != nil {
		return nil, err
	}

	// 获取二级市场的 listing 信息
	var listingIDs []string
	for _, s := range secondaryOrders {
		listingIDs = append(listingIDs, s.ListingID())
	}
	listings, err := s.secondaryClient.GetListingLogsByListingIDsRPC(ctx, &marketpb.GetListingLogsByListingIDsRPCReq{
		ListingIDs: listingIDs,
	})
	if err != nil {
		return nil, err
	}
	var listingsMap = make(map[string]*marketpb.GetListingLogsByListingIDsRPCResp_ListingLog)
	for _, l := range listings.ListingLogs {
		listingsMap[l.ListingID] = l
	}

	orderIDs := make([]string, 0)
	// 2. 收集所有 NFT ID 和 orderID
	nftIDs := make([]string, 0)
	for _, s := range secondaryOrders {
		nftIDs = append(nftIDs, s.NftID())
		orderIDs = append(orderIDs, s.OrderID())
	}
	// 3. 批量获取 Chain ID
	var chainIDMap map[string]string
	if len(nftIDs) > 0 {
		resp, err := s.nftClient.BatchGetChainIDByNFTIDRPC(ctx, &nftpb.BatchGetChainIDByNFTIDRPCReq{
			NftIDs: nftIDs,
		})
		if err != nil {
			zap.L().Error("failed to get chain ids", zap.Error(err), zap.Strings("nft_ids", nftIDs))
		} else if resp != nil {
			chainIDMap = resp.NftIDToChainID
		}
	}
	// 4. 批量获取主订单信息
	// TODO: 省略这一步,直接联表一次查询所有需要的数据
	mainOrders, err := s.orderRepo.GetOrdersByIDs(ctx, orderIDs)
	if err != nil {
		return nil, err
	}
	mainOrderMap := make(map[string]*Order)
	for _, mo := range mainOrders {
		mainOrderMap[mo.ID] = mo
	}

	// 5. 构建返回结果
	var allItems []*orderpb.GetMyOrdersResp_OrderItem
	for _, s := range secondaryOrders {
		mainOrder, ok := mainOrderMap[s.OrderID()]
		if !ok {
			zap.L().Warn("main order not found", zap.String("order_id", s.OrderID()))
			continue
		}
		chainID := ""
		if chainIDMap != nil {
			if id, ok := chainIDMap[s.NftID()]; ok {
				chainID = id
			}
		}
		var payTime int64
		if mainOrder.PayTime() != nil {
			payTime = mainOrder.PayTime().UTC().UnixMilli()
		}
		var listingTime, completedTime int64
		if listingsMap != nil {
			if l, ok := listingsMap[s.ListingID()]; ok {
				listingTime = l.CreatedAt
				completedTime = l.CompletionTime
			}
		}

		item := &orderpb.GetMyOrdersResp_OrderItem{
			OrderID:       s.OrderID(),
			ExtraID:       s.ID,
			NftID:         s.NftID(),
			ChainID:       chainID,
			OrderType:     TypeSecondaryMarket.OrderNoPrefix(),
			Title:         mainOrder.Title(),
			Price:         s.Price().String(),
			Num:           1,
			Status:        int64(StatusSuccess.Int16()),
			OrderNo:       mainOrder.OrderNo(),
			ImgURL:        mainOrder.ImgURL(),
			CreateTime:    mainOrder.CreatedAt().UTC().UnixMilli(),
			PayTime:       payTime,
			Role:          1,
			ExpiredTime:   mainOrder.ExpiredTime().UTC().UnixMilli(),
			ListingTime:   listingTime,
			CompletedTime: completedTime,
			PayType:       mainOrder.PayType().ToOrderPB(),
		}
		allItems = append(allItems, item)

	}
	return &orderpb.GetMyOrdersResp{
		Items: allItems,
		Total: total,
	}, nil
}

// CheckOrderIsLimit 检查是否允许下单
// 检查半小时内未支付成功的订单数量是否超过限制
func (s *OrderService) CheckOrderIsLimit(ctx context.Context, userID string) (bool, error) {
	allow := s.limiter.Allow(ctx, fmt.Sprintf(redisOrderLimitKey, userID), rate.PerMinute(5))
	if !allow {
		return false, rate.ErrLimitExceeded
	}

	startTime := time.Now().Add(-8 * time.Minute)

	// 查询该用户8分钟内未成功的订单
	orders, err := s.orderRepo.GetUnsuccessfulOrdersByUserIDAndTime(ctx, userID, startTime, time.Now())
	if err != nil {
		return false, err
	}

	if len(orders) >= 1 {
		return true, nil
	}

	return false, nil
}

// GetOrdersWithSecondaryExtra 获取订单信息和对应二级市场订单额外信息
func (s *OrderService) GetOrdersWithSecondaryExtra(ctx context.Context, orderIDs []string) ([]*OrderWithSecondaryExtra, error) {
	// 获取主订单列表
	orders, err := s.orderRepo.GetOrdersByIDsWithSort(ctx, orderIDs)
	if err != nil {
		return nil, err
	}

	// 获取二级市场订单额外信息
	extraMap, err := s.GetSecondaryExtraMap(ctx, orderIDs)

	var result []*OrderWithSecondaryExtra
	for _, order := range orders {
		extra, ok := extraMap[order.ID]
		if !ok {
			continue
		}
		result = append(result, &OrderWithSecondaryExtra{
			Order:  order,
			Extras: extra,
		})
	}

	return result, nil
}

// GetSecondaryExtraMap 获取orderIDs对应二级市场订单额外信息map
func (s *OrderService) GetSecondaryExtraMap(ctx context.Context, orderIDs []string) (map[string][]*SecondaryExtra, error) {
	// 获取二级市场订单额外信息
	extras, err := s.orderRepo.GetSecondaryExtraByOrderIDs(ctx, orderIDs)
	if err != nil {
		return nil, err
	}

	extraMap := make(map[string][]*SecondaryExtra)
	for _, extra := range extras {
		extraMap[extra.OrderID()] = append(extraMap[extra.OrderID()], extra)
	}

	return extraMap, nil
}

// GetUserCompletedOrderCount 获取同一项目中用户已完成的订单数量
func (s *OrderService) GetUserCompletedOrderCount(ctx context.Context, userID string, projectID string) (int64, error) {
	//// 使用专门的方法获取用户的所有一级市场订单
	//primaryOrders, err := s.orderRepo.GetPrimaryOrdersByUserIDAndProjectID(ctx, userID, projectID)
	//if err != nil {
	//	return 0, fmt.Errorf("failed to get primary orders: %w", err)
	//}
	//
	//// 如果没有符合条件的订单，直接返回0
	//if len(primaryOrders) == 0 {
	//	return 0, nil
	//}
	//
	//// 统计有效的订单数量
	//now := time.Now()
	//var count int64
	//
	//
	//// 获取订单的详细信息并验证支付中订单是否已过期
	//for _, order := range primaryOrders {
	//	// 检查订单ID对应的主订单
	//	mainOrder, err := s.orderRepo.GetOrderByID(ctx, order.OrderID)
	//	if err != nil {
	//		continue // 如果无法获取主订单，跳过此订单
	//	}
	//
	//	if mainOrder.Status().Equal(StatusSuccess) {
	//		// 已完成的订单直接计数
	//		count++
	//	} else if mainOrder.Status().Equal(StatusPaying) {
	//		// 支付中的订单，检查是否过期
	//		if mainOrder.ExpiredTime().After(now) {
	//			// 未过期的支付中订单
	//			count++
	//		}
	//	}
	//}

	orderCount, err := s.orderRepo.GetUserCompletedAndPayingExtPrimaryOrderCount(ctx, userID, projectID)
	if err != nil {
		return 0, err
	}

	return orderCount, nil
}

func (s *OrderService) GetMarketOrderNFTID2ExtraIDMap(ctx context.Context, o *Order) (map[string]string, error) {
	// 根据订单类型获取额外信息
	switch o.OrderType() {
	case TypePrimaryMarket:
		extras, err := s.orderRepo.GetPrimaryExtraByOrderID(ctx, o.ID)
		if err != nil {
			return nil, err
		}
		nftID2PrimaryExtID := make(map[string]string, len(extras))
		for _, e := range extras {
			nftID2PrimaryExtID[e.NftID()] = e.ID
		}
		return nftID2PrimaryExtID, nil
	case TypeSecondaryMarket:
		extras, err := s.orderRepo.GetSecondaryExtraByOrderID(ctx, o.ID)
		if err != nil {
			return nil, err
		}
		nftID2SecondaryExtIDMap := make(map[string]string, len(extras))
		for _, e := range extras {
			nftID2SecondaryExtIDMap[e.NftID()] = e.ID
		}
		return nftID2SecondaryExtIDMap, nil
	}
	return nil, nil
}

func (s *OrderService) GetPrimaryByOrderCriteria(ctx context.Context, cris *OrderCriteria) (*orderpb.GetOrderDetailByUserIdRPCResp, error) {

	// 1. 获取主订单列表
	orders, total, err := s.orderRepo.GetPrimaryOrdersByCriteria(ctx, cris)
	if err != nil {
		return nil, err
	}

	// 2. 获取二级订单待支付列表额外信息
	var primaries []*model.PaymentOrderPrimary
	primaryOrderIDs := make([]string, 0, len(orders))
	for _, o := range orders {
		primaryOrderIDs = append(primaryOrderIDs, o.ID)
	}
	if len(primaryOrderIDs) > 0 {
		primaries, err = s.orderRepo.GetPrimaryOrdersByOrderIDs(ctx, primaryOrderIDs)
		if err != nil {
			return nil, err
		}
	}

	// 3.统计数量
	primaryMap := make(map[string][]*model.PaymentOrderPrimary, len(primaries))
	for _, s := range primaries {
		if _, ok := primaryMap[s.OrderID]; !ok {
			// 如果不存在，则初始化一个空切片
			primaryMap[s.OrderID] = make([]*model.PaymentOrderPrimary, 0)
		}
		primaryMap[s.OrderID] = append(primaryMap[s.OrderID], s)
	}

	// 5.构建返回结果
	var oderResp []*orderpb.GetOrderDetailByUserIdRPCResp_OrderItem
	for _, o := range orders {
		primaryList, ok := primaryMap[o.ID]
		if !ok {
			zap.L().Warn("order not found in extras", zap.String("order_id", o.ID))
			continue
		}

		oderResp = append(oderResp, &orderpb.GetOrderDetailByUserIdRPCResp_OrderItem{
			OrderId:     o.ID,
			Title:       o.title,
			TotaPrice:   o.Price().String(),
			Price:       primaryList[0].Price.String(),
			Num:         int64(len(primaryList)),
			Status:      int64(o.status.value),
			OrderNo:     o.OrderNo(),
			ImgUrl:      o.ImgURL(),
			CreateTime:  o.CreatedAt().UTC().UnixMilli(),
			ExpiredTime: o.ExpiredTime().UTC().UnixMilli(),
			UpdatedTime: o.UpdatedAt().UTC().UnixMilli(),
			ProjectId:   primaryList[0].ProjectID,
			NftId:       primaryList[0].NftID,
		})
	}

	resp := &orderpb.GetOrderDetailByUserIdRPCResp{
		Items: oderResp,
		Total: total,
	}

	return resp, nil
}

func (s *OrderService) GetSecondByOrderCriteria(ctx context.Context, cris *OrderCriteria) (*orderpb.GetOrderDetailByUserIdRPCResp, error) {
	// 1. 获取主订单列表
	orders, total, err := s.orderRepo.GetSecondOrdersByCriteria(ctx, cris)
	if err != nil {
		return nil, err
	}

	// 2. 获取二级订单待支付列表额外信息
	var secondaryOrders []*model.PaymentOrderSecondary
	secondaryOrderIDs := make([]string, 0)
	for _, o := range orders {
		secondaryOrderIDs = append(secondaryOrderIDs, o.ID)
	}
	if len(secondaryOrderIDs) > 0 {
		secondaryOrders, err = s.orderRepo.GetSecondaryOrdersByOrderIDs(ctx, secondaryOrderIDs)
		if err != nil {
			return nil, err
		}
	}

	// 3.统计数量
	secondaryMap := make(map[string][]*model.PaymentOrderSecondary, len(secondaryOrders))
	for _, s := range secondaryOrders {
		if _, ok := secondaryMap[s.OrderID]; !ok {
			secondaryMap[s.OrderID] = make([]*model.PaymentOrderSecondary, 0)
		}
		secondaryMap[s.OrderID] = append(secondaryMap[s.OrderID], s)
	}

	// 4.构建返回结果
	var oderResp []*orderpb.GetOrderDetailByUserIdRPCResp_OrderItem
	for _, o := range orders {
		secondList, ok := secondaryMap[o.ID]
		if !ok {
			zap.L().Warn("order not found in extras", zap.String("order_id", o.ID))
			continue
		}

		oderResp = append(oderResp, &orderpb.GetOrderDetailByUserIdRPCResp_OrderItem{
			OrderId:     o.ID,
			Title:       o.title,
			TotaPrice:   o.Price().String(),
			Price:       secondList[0].Price.String(),
			Num:         int64(len(secondList)),
			Status:      int64(o.status.value),
			OrderNo:     o.OrderNo(),
			ImgUrl:      o.ImgURL(),
			CreateTime:  o.CreatedAt().UTC().UnixMilli(),
			ExpiredTime: o.ExpiredTime().UTC().UnixMilli(),
			UpdatedTime: o.updatedAt.UTC().UnixMilli(),
			ProjectId:   secondList[0].ProjectID,
			NftId:       secondList[0].NftID,
		})
	}

	resp := &orderpb.GetOrderDetailByUserIdRPCResp{
		Items: oderResp,
		Total: total,
	}

	return resp, nil
}
