package order

import (
	"cnb.cool/cymirror/ces-services/common/db/optimisticlock"
	"github.com/shopspring/decimal"
	"time"
)

func (o *Order) Status() Status {
	return o.status
}

func (o *Order) OrderNo() string {
	return o.orderNo
}

func (o *Order) UserID() string {
	return o.userID
}

func (o *Order) OrderType() Type {
	return o.orderType
}

func (o *Order) Title() string {
	return o.title
}

func (o *Order) CreatedAt() time.Time {
	return o.createdAt
}

func (o *Order) UpdatedAt() time.Time {
	return o.updatedAt
}

func (o *Order) Version() optimisticlock.Version {
	return o.version
}

func (o *Order) PayType() PayType {
	return o.payType
}

func (o *Order) PayTime() *time.Time {
	return o.payTime
}

func (o *Order) ExpiredTime() time.Time {
	return o.expiredTime
}

func (o *Order) ImgURL() string {
	return o.imgURL
}

func (o *Order) Price() decimal.Decimal {
	return o.price
}

func (o *Order) PayID() *string {
	return o.payID
}

func (o *Order) SetStatus(status Status) {
	o.status = status
}

func (o *Order) SetOrderNo(orderNo string) {
	o.orderNo = orderNo
}

func (o *Order) SetUserID(userID string) {
	o.userID = userID
}

func (o *Order) SetOrderType(orderType Type) {
	o.orderType = orderType
}

func (o *Order) SetTitle(title string) {
	o.title = title
}

func (o *Order) SetCreatedAt(createdAt time.Time) {
	o.createdAt = createdAt
}

func (o *Order) SetUpdatedAt(updatedAt time.Time) {
	o.updatedAt = updatedAt
}

func (o *Order) SetVersion(version int64) {
	o.version = optimisticlock.Version{
		Int64: version,
		Valid: true,
	}
}

func (o *Order) SetPayType(payType PayType) {
	o.payType = payType
}

func (o *Order) SetPayTime(payTime time.Time) {
	o.payTime = &payTime
}

func (o *Order) SetExpiredTime(expiredTime time.Time) {
	o.expiredTime = expiredTime
}

func (o *Order) SetImgURL(imgURL string) {
	o.imgURL = imgURL
}

func (o *Order) SetPrice(price decimal.Decimal) {
	o.price = price
}

func (o *Order) SetPayID(s string) {
	o.payID = &s
}

func (o *Order) CanDivide() bool {
	if o == nil {
		return false
	}
	if !o.Status().Equal(StatusSuccess) {
		return false
	}
	return true
}
