package order

import (
	"time"

	"cnb.cool/cymirror/ces-services/common/db/optimisticlock"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/model"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/project"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type PrimaryExtra struct {
	ID        string          // id
	orderID   string          // 订单id
	projectID string          // 项目id
	nftID     string          // nft id
	userID    string          // 用户id
	price     decimal.Decimal // 价格

	createdAt time.Time // 创建时间
	updatedAt time.Time // 更新时间
	version   int64     // 版本
}

func (p *PrimaryExtra) ToModel() *model.PaymentOrderPrimary {
	return &model.PaymentOrderPrimary{
		ID:        p.ID,
		OrderID:   p.orderID,
		UserID:    p.userID,
		ProjectID: p.projectID,
		CreatedAt: p.createdAt,
		UpdatedAt: p.updatedAt,
		Version:   optimisticlock.Version{Int64: p.version, Valid: true},
		NftID:     p.nftID,
		Price:     p.price,
	}
}

func NewPrimaryExtraFromModel(m *model.PaymentOrderPrimary) *PrimaryExtra {
	return &PrimaryExtra{
		ID:        m.ID,
		orderID:   m.OrderID,
		projectID: m.ProjectID,
		nftID:     m.NftID,
		userID:    m.UserID,
		price:     m.Price,
		createdAt: m.CreatedAt,
		updatedAt: m.UpdatedAt,
		version:   m.Version.Int64,
	}
}

type PrimaryMarket struct {
	*Order
	extra []*PrimaryExtra
}

func NewPrimaryMarket(userID string, p *project.Project, count int32) *PrimaryMarket {
	orderID, _ := uuid.NewV7()

	o := &PrimaryMarket{
		Order: &Order{
			ID:          orderID.String(),
			status:      StatusReduceStock,
			orderNo:     generateOrderNo(userID, orderID.String(), TypePrimaryMarket),
			userID:      userID,
			orderType:   TypePrimaryMarket,
			expiredTime: time.Now().Add(time.Minute * 3), // TODO: 动态过期时间
		},
	}
	o.SetOrderInfo(p.Name(), p.ImgURL())
	o.SetPrice(p.Price().Mul(decimal.NewFromInt32(count)))
	return o
}

func (o *PrimaryMarket) Extra() []*PrimaryExtra {
	return o.extra
}

func (o *PrimaryMarket) SetPrimaryExtra(nftIDs []string, project *project.Project) {
	o.extra = make([]*PrimaryExtra, 0, len(nftIDs))
	for _, nftID := range nftIDs {
		o.extra = append(o.extra, &PrimaryExtra{
			orderID:   o.ID,
			projectID: project.ID,
			nftID:     nftID,
			userID:    o.userID,
			price:     project.Price(),
		})
	}
}
