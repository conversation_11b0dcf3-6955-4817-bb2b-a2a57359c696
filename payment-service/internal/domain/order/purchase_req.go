package order

import (
	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
	"github.com/shopspring/decimal"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"time"
)

type PurchaseRequest struct {
	*Order
}

func NewPurchaseRequest(req *orderpb.CreatePurchaseRequestOrderRPCReq) (*PurchaseRequest, error) {
	price, err := decimal.NewFromString(req.Price)
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, "price is invalid")
	}
	if price.IsNegative() || price.IsZero() || price.Exponent() < -2 {
		return nil, status.Error(codes.InvalidArgument, "price is invalid")
	}
	o := &PurchaseRequest{
		Order: &Order{
			ID:          req.OrderID,
			status:      StatusWaitPay,
			orderNo:     generateOrderNo(req.UserID, req.OrderID, TypePurchaseRequest),
			userID:      req.UserID,
			orderType:   TypePurchaseRequest,
			expiredTime: time.Now().Add(time.Minute * 15), // TODO: 动态过期时间
		},
	}
	o.SetOrderInfo(req.Name, req.ImgURL)
	o.SetPrice(price)
	return o, nil
}
