package wallet

var (
	// kfpayStatusUnknown 未知
	kfpayStatusUnknown = KfpayWalletStatus{value: 0}
	// KfpayStatusAuditing 审核中
	KfpayStatusAuditing = KfpayWalletStatus{value: 1}
	// KfpayStatusOpen 已开通
	KfpayStatusOpen = KfpayWalletStatus{value: 2}
	// KfpayStatusFail 审核失败
	KfpayStatusFail = KfpayWalletStatus{value: 3}
)

type KfpayWalletStatus struct {
	value int16
}

func (k KfpayWalletStatus) Int16() int16 {
	return k.value
}

func (k KfpayWalletStatus) Int32() int32 {
	return int32(k.value)
}

func NewKfpayWalletStatusFromInt16(value int16) KfpayWalletStatus {
	switch value {
	case 1:
		return KfpayStatusAuditing
	case 2:
		return KfpayStatusOpen
	case 3:
		return KfpayStatusFail
	default:
		return kfpayStatusUnknown
	}
}
