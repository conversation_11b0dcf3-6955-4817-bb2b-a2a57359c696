package wallet

type UserWalletStatus struct {
	userID             string
	yeepayWalletIsOpen bool
	kfpayWalletIsOpen  bool
	isOpen             bool
}

func (u UserWalletStatus) UserID() string {
	return u.userID
}

func (u UserWalletStatus) YeepayWalletIsOpen() bool {
	return u.yeepayWalletIsOpen
}

func (u UserWalletStatus) KfpayWalletIsOpen() bool {
	return u.kfpayWalletIsOpen
}

func (u UserWalletStatus) IsOpen() bool {
	return u.isOpen
}

func NewUserWalletStatus(userID string, yeepayWalletIsOpen bool, kfpayWalletIsOpen bool) *UserWalletStatus {
	return &UserWalletStatus{
		userID:             userID,
		yeepayWalletIsOpen: yeepayWalletIsOpen,
		kfpayWalletIsOpen:  kfpayWalletIsOpen,
		isOpen:             yeepayWalletIsOpen || kfpayWalletIsOpen,
	}
}
