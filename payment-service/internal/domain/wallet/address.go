package wallet

import (
	"math/rand/v2"
	"strconv"
	"strings"
)

// 定义一个包含常见街道名称用字的字符串池
const streetNameChars = "长短五一二三四桥门山河湖海江林花柳泉东西南中新旧大小长广文光华六七八九十安宁和乐平康福祥瑞泰景阳德胜昌"

func GenStreetName() string {
	// 将汉字池字符串转换为 rune 切片，以便正确处理每个汉字
	charPool := []rune(streetNameChars)
	poolSize := len(charPool)

	// 使用 strings.Builder 高效构建字符串
	var result strings.Builder
	// 设置要生成的汉字数量
	const count = 3

	for i := 0; i < count; i++ {
		// 1. 生成一个在 [0, poolSize) 范围内的随机索引
		randomIndex := rand.IntN(poolSize)

		// 2. 根据随机索引从池中获取一个 rune (汉字)
		selectedChar := charPool[randomIndex]

		// 3. 将选中的汉字写入 Builder
		result.WriteRune(selectedChar)
	}

	result.WriteRune('街')
	result.WriteString(strconv.Itoa(rand.IntN(9900) + 100))
	result.WriteRune('号')

	return result.String()
}
