package wallet

import (
	"cnb.cool/1024hub/kfpay-go-sdk"
	kycpb "cnb.cool/cymirror/ces-services/account-service/gen/proto/ces/account/kyc"
	"cnb.cool/cymirror/ces-services/common/server"
	walletpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/wallet"
	"context"
	"github.com/google/uuid"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"strings"
)

type Service struct {
	repo                      Repo
	kfpayClient               *kfpay.Client
	kycClient                 kycpb.KYCServiceClient
	kfpayWalletPasswordPepper *KfpayWalletPasswordPepper
}

func NewService(repo Repo, kfpayClient *kfpay.Client, kycClient kycpb.KYCServiceClient, kfpayWalletPasswordPepper *KfpayWalletPasswordPepper) *Service {
	return &Service{repo: repo, kfpayClient: kfpayClient, kycClient: kycClient, kfpayWalletPasswordPepper: kfpayWalletPasswordPepper}
}

func (s *Service) KfpayValidateBankCard(ctx context.Context, userID string, userIDCard *kycpb.GetIDCardByUserIDRPCResp,
	cardNo, phoneNumber string, reqCardType walletpb.BankCardType,
) (*kfpay.ValidateCardBinResult, *KfpayBankCode, error) {
	// 通过失败次数24H限流
	times, err := s.repo.GetKfpayVerifyBankFailedTimes(ctx, userID)
	if err != nil {
		return nil, nil, err
	}
	if times >= 3 {
		return nil, nil, status.Error(codes.Unavailable, "validate bank card function is locked")
	}

	// 调用「卡bin校验」获取bank code
	vcbResp, err := s.kfpayClient.ValidateCardBin(ctx, &kfpay.ValidateCardBinCmd{
		OrderNo: strings.ReplaceAll(uuid.New().String(), "-", "")[:30],
		CardNo:  cardNo,
	})
	if err != nil {
		zap.L().Error("kfpay validate card bin failed", zap.Error(err), zap.String("card no", cardNo))
		return nil, nil, server.InternalStatus
	}
	if vcbResp.Status != kfpay.StatusSuccess {
		zap.L().Error("kfpay validate card bin failed",
			zap.String("status", vcbResp.Status.String()),
			zap.String("card no", cardNo),
			zap.String("failure details", vcbResp.FailureDetails),
		)
		return nil, nil, status.Error(codes.InvalidArgument, "validate card no failed")
	}

	// 验证银行是否支持
	bankCode, err := s.repo.GetKfpayBankCodeByTypeCode(ctx, vcbResp.BankType)
	if err != nil {
		return nil, nil, server.InternalStatus
	}
	if bankCode == nil {
		return nil, nil, status.Error(codes.InvalidArgument, "bank code invalid or bank not support")
	}
	if !bankCode.IsSupport() {
		return nil, nil, status.Error(codes.InvalidArgument, "bank not support")
	}

	// 「单笔银行卡带手机号验证」 验证（储蓄卡，信用卡）信息是否正确 => 四要素（身份证，姓名，手机号，卡号）是否正确
	var acType kfpay.AccountCardType
	switch reqCardType {
	case walletpb.BankCardType_Credit:
		if vcbResp.CardType != kfpay.CardTypeCredit {
			return nil, nil, status.Error(codes.InvalidArgument, "card type is not credit")
		}
		acType = kfpay.AccountCardTypeCredit
	case walletpb.BankCardType_Debit:
		if vcbResp.CardType != kfpay.CardTypeDebit {
			return nil, nil, status.Error(codes.InvalidArgument, "card type is not debit")
		}
		acType = kfpay.AccountCardTypeDebit
	default:
		return nil, nil, status.Error(codes.InvalidArgument, "card type is invalid")
	}

	bcResp, err := s.kfpayClient.ValidateBankCard(ctx, &kfpay.ValidateBankCardCmd{
		OrderNo:                  strings.ReplaceAll(uuid.New().String(), "-", "")[:30],
		CustBankNo:               vcbResp.BankType,
		CustName:                 userIDCard.Name,
		CustBankAccountNo:        cardNo,
		CustAccountCreditOrDebit: acType,
		CustCertificationType:    kfpay.CertificationTypeIDCard,
		CustID:                   userIDCard.IdCard,
		CustBindPhoneNo:          phoneNumber,
		PersonalMandate:          kfpay.PersonalMandateYES,
	})
	if err != nil {
		zap.L().Error("kfpay validate bank card failed", zap.Error(err), zap.String("card no", cardNo))
		return nil, nil, server.InternalStatus
	}
	if bcResp.Status != kfpay.StatusSuccess {
		// 增加失败次数
		zap.L().Error("kfpay validate bank card failed",
			zap.String("status", bcResp.Status.String()),
			zap.String("card no", cardNo),
			zap.String("failure details", bcResp.FailureDetails),
		)

		_ = s.repo.IncrementKfpayVerifyBankFailedTimes(ctx, userID, times)
		return nil, nil, status.Error(codes.InvalidArgument, "validate bank card failed")
	}
	return vcbResp, bankCode, nil
}

func (s *Service) ValidateKfpayPassword(ctx context.Context, wallet *Kfpay, payPassword string) error {
	// TODO 没有做24H无尝试错误记录清0 现在是2H归0

	// 校验是否还允许输入密码
	times, err := s.repo.GetKfpayWalletSetPasswordErrorTimes(ctx, wallet.userID)
	if err != nil {
		return server.InternalStatus
	}
	//if times >= 5 {
	//	return status.Error(codes.Unavailable, "validate password function is locked")
	//}

	// 校验钱包密码
	if !wallet.ValidatePassword(payPassword, s.kfpayWalletPasswordPepper.Pepper()) {
		err = s.repo.IncrKfpayWalletSetPasswordErrorTimes(ctx, wallet.userID, times)
		if err != nil {
			return server.InternalStatus
		}
		return status.Error(codes.InvalidArgument, "password is not match")
	}

	// 重置密码尝试错误次数
	if times >= 0 {
		_ = s.repo.ResetKfpayWalletSetPasswordErrorTimes(ctx, wallet.userID)
	}
	return nil
}

func (s *Service) BatchGetUserWalletStatus(ctx context.Context, userIDs []string) ([]*UserWalletStatus, error) {
	if len(userIDs) == 0 {
		return []*UserWalletStatus{}, nil
	}

	// 批量查询用户易宝钱包状态
	yeepayWallets, err := s.repo.BatchGetYeePayWalletsByUserIDs(ctx, userIDs)
	if err != nil {
		return nil, err
	}
	userID2YeepayWalletMap := lo.SliceToMap(yeepayWallets, func(item *YeePay) (string, *YeePay) {
		return item.UserID(), item
	})

	kfpayWallets, err := s.repo.BatchGetKfpayWalletsByUserIDs(ctx, userIDs)
	if err != nil {
		return nil, err
	}
	userID2KfpayWalletMap := lo.SliceToMap(kfpayWallets, func(item *Kfpay) (string, *Kfpay) {
		return item.UserID(), item
	})

	// 创建结果切片
	userWalletStatuses := make([]*UserWalletStatus, 0, len(userIDs))

	// 组装返回数据
	for _, userID := range userIDs {
		// 检查该用户是否有钱包且钱包已激活
		yeepayWallet, yeepayExists := userID2YeepayWalletMap[userID]
		kfpayWallet, kfpayExists := userID2KfpayWalletMap[userID]
		yeepayWalletIsExist := yeepayExists && yeepayWallet.IsOpen()
		kfpayWalletIsExist := kfpayExists && kfpayWallet.IsOpen()

		userWalletStatuses = append(userWalletStatuses, NewUserWalletStatus(userID, yeepayWalletIsExist, kfpayWalletIsExist))
	}

	return userWalletStatuses, nil
}
