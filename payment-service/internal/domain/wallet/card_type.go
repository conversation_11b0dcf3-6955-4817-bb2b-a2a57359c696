package wallet

import (
	"cnb.cool/1024hub/kfpay-go-sdk"
	walletpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/wallet"
)

var (
	CardTypeUnknown = CardType{value: 0}
	// CardTypeDebit 借记卡
	CardTypeDebit = CardType{value: 1}
	// CardTypeCredit 信用卡
	CardTypeCredit = CardType{value: 2}
)

type CardType struct {
	value int16
}

func (c CardType) Value() int16 {
	return c.value
}

func NewCardTypeFromInt16(value int16) CardType {
	switch value {
	case 1:
		return CardTypeDebit
	case 2:
		return CardTypeCredit
	default:
		return CardTypeUnknown
	}
}

// ToPbBankCardType 将 CardType 转换为 walletpb.BankCardType
func (c CardType) ToPbBankCardType() walletpb.BankCardType {
	switch c.value {
	case 1:
		return walletpb.BankCardType_Debit
	case 2:
		return walletpb.BankCardType_Credit
	default:
		return walletpb.BankCardType_Unknown
	}
}

// ToSDKBankCardType 将 kfpay.BankCardType 转换为 CardType
func (c CardType) ToSDKBankCardType() kfpay.AccountCardType {
	switch c {
	case CardTypeDebit:
		return kfpay.AccountCardTypeDebit
	case CardTypeCredit:
		return kfpay.AccountCardTypeCredit
	default:
		return kfpay.AccountCardTypeDebit
	}
}
