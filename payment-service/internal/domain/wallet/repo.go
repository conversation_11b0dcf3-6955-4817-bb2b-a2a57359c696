package wallet

import (
	"context"
	"time"

	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
)

type Repo interface {
	// GetYeePayWalletByUserID 根据用户 ID 获取 Yeepay 钱包
	GetYeePayWalletByUserID(ctx context.Context, id string) (*YeePay, error)
	// BatchGetYeePayWalletsByUserIDs 批量根据用户ID获取Yeepay钱包
	BatchGetYeePayWalletsByUserIDs(ctx context.Context, ids []string) ([]*YeePay, error)
	// InsertYeePayWallet 插入 Yeepay 钱包
	InsertYeePayWallet(ctx context.Context, wallet *YeePay) error
	// ApplyWalletCallback 处理 Yeepay 钱包回调
	ApplyWalletCallback(ctx context.Context, wallet *YeePay) error
	// GetYeePayWalletByUserIDs 根据用户 IDs 获取 Yeepay 钱包
	GetYeePayWalletByUserIDs(ctx context.Context, ids []string) ([]*YeePay, error)

	// CreateKfpayWallet 创建 Kfpay 钱包
	CreateKfpayWallet(ctx context.Context, wallet *Kfpay) error

	// GetKfpayWalletByUserID 根据用户 ID 获取 Kfpay 钱包
	GetKfpayWalletByUserID(ctx context.Context, id string) (*Kfpay, error)
	// BatchGetKfpayWalletsByUserIDs 根据用户 ID 获取 Kfpay 钱包
	BatchGetKfpayWalletsByUserIDs(ctx context.Context, ids []string) ([]*Kfpay, error)
	// BatchGetAuditingKfpayWallets 批量获取审核中的钱包
	BatchGetAuditingKfpayWallets(ctx context.Context, limit int) ([]*Kfpay, error)

	// UpdateKfpayWallet 更新 Kfpay 钱包
	UpdateKfpayWallet(ctx context.Context, wallet *Kfpay) error

	// CreateKfpayUserBandCard 创建 Kfpay 钱包绑定的银行卡
	CreateKfpayUserBandCard(ctx context.Context, card *KfpayUserBandCard) error
	// CreateKfpayTradeLog 创建 Kfpay 交易记录
	CreateKfpayTradeLog(ctx context.Context, log *KfpayTradeLog) error

	// GetKfpayUserBandCard 根据 id 获取 Kfpay 钱包绑定的银行卡
	GetKfpayUserBandCard(ctx context.Context, id string) (*KfpayUserBandCard, error)
	// GetKfpayUserBandCardWithUserID 根据 绑定卡id和用户id 获取 Kfpay 钱包绑定的银行卡
	GetKfpayUserBandCardWithUserID(ctx context.Context, id string, userID string) (*KfpayUserBandCard, error)
	// GetKfpayUserBandCardByCardNoWithUserID 根据 卡号 和 用户 ID 获取 Kfpay 钱包绑定的银行卡
	GetKfpayUserBandCardByCardNoWithUserID(ctx context.Context, userID, cardNo string) (*KfpayUserBandCard, error)

	// GetKfpayTradeLogByReqNo 根据订单ID获取交易记录
	GetKfpayTradeLogByReqNo(ctx context.Context, reqNo string) (*KfpayTradeLog, error)
	// BatchGetExpiredKfpayTradeLog 批量进行中状态超时30分钟的订单
	BatchGetExpiredKfpayTradeLog(ctx context.Context, limit int) ([]*KfpayTradeLog, error)

	// UpdateKfpayTradeLog 更新 Kfpay 交易记录
	UpdateKfpayTradeLog(ctx context.Context, log *KfpayTradeLog) error

	// DeleteKfpayUserBankCardWithUserID 删除 Kfpay 钱包绑定的银行卡
	DeleteKfpayUserBankCardWithUserID(ctx context.Context, id, userID string) error
	// DeleteAllKfpayBankCardsByUserID 移除用户所有绑定的银行卡
	DeleteAllKfpayBankCardsByUserID(ctx context.Context, userID string) error
	// GetKfpayBankCodeByTypeCode 根据银行code获取银行名称
	GetKfpayBankCodeByTypeCode(ctx context.Context, typeCode string) (*KfpayBankCode, error)

	// GetKfpayUserBandCardsByUserID 根据 用户 ID 获取 Kfpay 钱包绑定的银行卡
	GetKfpayUserBandCardsByUserID(ctx context.Context, userID string) ([]*KfpayUserBandCard, error)
	// GetYeepayWalletByCondition 根据条件获取Yeepay钱包
	GetYeepayWalletByCondition(ctx context.Context, cmd *GetYeepayWalletByConditionCmd) ([]*YeePay, int64, error)

	// SetKfpayWalletResetPasswordToken 生成快付通钱包重置密码token
	SetKfpayWalletResetPasswordToken(ctx context.Context, userID, token string) error
	// GetKfpayWalletResetPasswordToken 获取快付通钱包重置密码token
	GetKfpayWalletResetPasswordToken(ctx context.Context, userID string) (string, error)
	// DeleteKfpayWalletResetPasswordToken 删除快付通钱包重置密码token
	DeleteKfpayWalletResetPasswordToken(ctx context.Context, userID string) error

	// IncrKfpayWalletSetPasswordErrorTimes 增加快付通钱包密码尝试错误次数
	IncrKfpayWalletSetPasswordErrorTimes(ctx context.Context, userID string, times int64) error
	// GetKfpayWalletSetPasswordErrorTimes 获取快付通钱包密码尝试错误次数
	GetKfpayWalletSetPasswordErrorTimes(ctx context.Context, userID string) (int64, error)
	// ResetKfpayWalletSetPasswordErrorTimes 重置快付通钱包密码尝试错误次数
	ResetKfpayWalletSetPasswordErrorTimes(ctx context.Context, userID string) error

	// GetWalletsByUserIDsAndPayType 根据用户IDs和支付类型获取钱包
	GetWalletsByUserIDsAndPayType(ctx context.Context, userIDs []string, t order.PayType) ([]Wallet, error)

	// UpdateKfpayBankCard 更新快付通银行卡信息
	UpdateKfpayBankCard(ctx context.Context, card *KfpayUserBandCard) error
	// BatchGetKfpayReviewingBankCards 批量获取快付通审核中的提现卡
	BatchGetKfpayReviewingBankCards(ctx context.Context, limit int) ([]*KfpayUserBandCard, error)
	// GetKfpayUserBankCardWithdrawStatusInReviewing 获取用户正在审核中的银行卡
	GetKfpayUserBankCardWithdrawStatusInReviewing(ctx context.Context, userID string) ([]*KfpayUserBandCard, error)
	// GetKfpayUserWithdrawBandCardWithUserID 根据 用户id 获取 Kfpay 钱包的提现银行卡
	GetKfpayUserWithdrawBandCardWithUserID(ctx context.Context, userID string) (*KfpayUserBandCard, error)
	// GetKfpayUserBandCardsByUserIDWithWithdrawStatus 根据用户id获取某个状态的快付通银行卡
	GetKfpayUserBandCardsByUserIDWithWithdrawStatus(ctx context.Context, userID string, status int16) ([]*KfpayUserBandCard, error)

	// SetKfpayWalletForgetPayPasswordToken 设置忘记密码某个步骤的token
	SetKfpayWalletForgetPayPasswordToken(ctx context.Context, userID string, token string, step ForgetPasswordResetStep) error
	// GetKfpayWalletForgetPayPasswordToken 获取忘记密码某个步骤的token
	GetKfpayWalletForgetPayPasswordToken(ctx context.Context, userID string, step ForgetPasswordResetStep) (string, error)
	// DeleteKfpayWalletForgetPayPasswordToken 删除忘记密码某个步骤的token
	DeleteKfpayWalletForgetPayPasswordToken(ctx context.Context, userID string, step ForgetPasswordResetStep) error
	// GetPhoneForgetPayPasswordOTPTTL 获取忘记密码验证码TTL
	GetPhoneForgetPayPasswordOTPTTL(ctx context.Context, phone string) (time.Duration, error)
	// SetPhoneForgetPayPasswordOTP 存储验证码
	SetPhoneForgetPayPasswordOTP(ctx context.Context, phone string, otp string, otpttl time.Duration) error
	// GetPhoneForgetPayPasswordOTP 获取验证码
	GetPhoneForgetPayPasswordOTP(ctx context.Context, phone string) (otp string, err error)
	// DeletePhoneForgetPayPasswordOTP 删除验证码
	DeletePhoneForgetPayPasswordOTP(ctx context.Context, phone string) error
	// SetKfpayCardAndPhone 将卡号和手机号存入redis
	SetKfpayCardAndPhone(ctx context.Context, userID string, cardNo string, phoneNumber string) error
	// GetKfpayCardAndPhone 从redis获取用户卡号和手机号
	GetKfpayCardAndPhone(ctx context.Context, userID string) (phone string, cardNo string, err error)
	// GetKfpayVerifyBankFailedTimes 从缓存中获取校验银行卡失败次数
	GetKfpayVerifyBankFailedTimes(ctx context.Context, userID string) (int64, error)
	// IncrementKfpayVerifyBankFailedTimes 将校验银行卡失败次数写入缓存(0点重置)
	IncrementKfpayVerifyBankFailedTimes(ctx context.Context, userID string, times int64) error
	// ResetKfpayVerifyBankFailedTimes 将校验银行卡失败次数重置为0
	ResetKfpayVerifyBankFailedTimes(ctx context.Context, userID string) error
}
type GetYeepayWalletByConditionCmd struct {
	StartTime    *time.Time
	EndTime      *time.Time
	Page         int64
	PageSize     int64
	Keyword      *string
	UserID       *string
	IsOpenYeepay *bool
}
