package wallet

import (
	"errors"
	"time"

	"cnb.cool/cymirror/ces-services/common/db/optimisticlock"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/model"
)

// KfpayUserBandCard 快付通用户绑定的银行卡
type KfpayUserBandCard struct {
	ID                       string // id
	createdAt                time.Time
	updatedAt                time.Time
	version                  optimisticlock.Version
	bankCardNumber           string         // 银行卡号
	bankName                 string         // 银行卡名字
	registeredMobileNumber   string         // 银行卡预留手机号
	cardType                 CardType       // 银行卡类型
	creditCardCvv            *string        // 信用卡cvv，仅类型为信用卡有效
	creditCardExpirationDate *time.Time     // 信用卡有效期，仅类型为信用卡有效
	userID                   string         // 用户id
	bankCode                 string         // 对应快付通bank code
	withdrawStatus           WithdrawStatus //提现卡状态
	reviewMessage            *string        // 提现卡审核结果
}

func (b *KfpayUserBandCard) CreatedAt() time.Time {
	return b.createdAt
}

func (b *KfpayUserBandCard) UpdatedAt() time.Time {
	return b.updatedAt
}

func (b *KfpayUserBandCard) Version() optimisticlock.Version {
	return b.version
}

func (b *KfpayUserBandCard) BankCardNumber() string {
	return b.bankCardNumber
}

func (b *KfpayUserBandCard) BankName() string {
	return b.bankName
}

func (b *KfpayUserBandCard) RegisteredMobileNumber() string {
	return b.registeredMobileNumber
}

func (b *KfpayUserBandCard) CardType() CardType {
	return b.cardType
}

func (b *KfpayUserBandCard) CreditCardCvv() *string {
	return b.creditCardCvv
}

func (b *KfpayUserBandCard) CreditCardExpirationDate() *time.Time {
	return b.creditCardExpirationDate
}

func (b *KfpayUserBandCard) UserID() string {
	return b.userID
}

func (b *KfpayUserBandCard) BankCode() string {
	return b.bankCode
}

func (b *KfpayUserBandCard) WithdrawStatus() WithdrawStatus {
	return b.withdrawStatus
}

func (b *KfpayUserBandCard) ReviewMessage() string {
	if b.reviewMessage == nil {
		return ""
	}
	return *b.reviewMessage
}

func (b *KfpayUserBandCard) SetWithdrawStatus(status WithdrawStatus) {
	b.withdrawStatus = status
}

func (b *KfpayUserBandCard) SetVersion(v int64) {
	b.version = optimisticlock.Version{
		Int64: v,
		Valid: true,
	}
}

func (b *KfpayUserBandCard) SetWithdrawStatusFail(message string) {
	b.withdrawStatus = WithdrawStatusFail
	b.reviewMessage = &message
}

func (b *KfpayUserBandCard) SetReviewMessage(message *string) {
	b.reviewMessage = message
}

func (b *KfpayUserBandCard) ToModel() *model.PaymentPayKfpayUserBankCard {
	return &model.PaymentPayKfpayUserBankCard{
		ID:                       b.ID,
		CreatedAt:                b.createdAt,
		UpdatedAt:                b.updatedAt,
		Version:                  b.version,
		BankCardNumber:           b.bankCardNumber,
		BankName:                 b.bankName,
		RegisteredMobileNumber:   b.registeredMobileNumber,
		CardType:                 b.cardType.Value(),
		CreditCardCvv:            b.creditCardCvv,
		CreditCardExpirationDate: b.creditCardExpirationDate,
		UserID:                   b.userID,
		BankCode:                 b.bankCode,
		WithdrawStatus:           b.withdrawStatus.Value(),
		ReviewMessage:            b.reviewMessage,
	}
}

type KfpayUserBandCardBuilder struct {
	builder *KfpayUserBandCard
}

func NewKfpayUserBandCardBuilder() *KfpayUserBandCardBuilder {
	return &KfpayUserBandCardBuilder{
		builder: &KfpayUserBandCard{},
	}
}

func (b *KfpayUserBandCardBuilder) Build() (*KfpayUserBandCard, error) {
	if b.builder.cardType == CardTypeCredit {
		if b.builder.creditCardCvv == nil || b.builder.creditCardExpirationDate == nil {
			return nil, errors.New("credit info must be set")
		}
	}
	return b.builder, nil
}

func (b *KfpayUserBandCardBuilder) SetBasic(
	bankCardNumber, bankName,
	registeredMobileNumber string, cardType CardType,
	userID, bankCode string,
	withDrawStatus WithdrawStatus,
) *KfpayUserBandCardBuilder {
	b.builder.bankCardNumber = bankCardNumber
	b.builder.bankName = bankName
	b.builder.registeredMobileNumber = registeredMobileNumber
	b.builder.userID = userID
	b.builder.bankCode = bankCode
	b.builder.cardType = cardType
	b.builder.withdrawStatus = withDrawStatus
	return b
}

func (b *KfpayUserBandCardBuilder) SetCredit(creditCardCvv string, creditCardExpirationDate time.Time) *KfpayUserBandCardBuilder {
	if b.builder.CardType() != CardTypeCredit {
		return b
	}
	b.builder.creditCardCvv = &creditCardCvv
	b.builder.creditCardExpirationDate = &creditCardExpirationDate
	return b
}

func NewKfpayUserBandCardFromModel(model *model.PaymentPayKfpayUserBankCard) *KfpayUserBandCard {
	if model == nil {
		return nil
	}
	return &KfpayUserBandCard{
		ID:                       model.ID,
		createdAt:                model.CreatedAt,
		updatedAt:                model.UpdatedAt,
		version:                  model.Version,
		bankCardNumber:           model.BankCardNumber,
		bankName:                 model.BankName,
		registeredMobileNumber:   model.RegisteredMobileNumber,
		cardType:                 NewCardTypeFromInt16(model.CardType),
		creditCardCvv:            model.CreditCardCvv,
		creditCardExpirationDate: model.CreditCardExpirationDate,
		userID:                   model.UserID,
		bankCode:                 model.BankCode,
		withdrawStatus:           NewWithdrawStatusFromInt16(model.WithdrawStatus),
		reviewMessage:            model.ReviewMessage,
	}
}

type KfpayCardAndPhone struct {
	Phone  string `json:"phone"`
	CardNo string `json:"card_no"`
}
