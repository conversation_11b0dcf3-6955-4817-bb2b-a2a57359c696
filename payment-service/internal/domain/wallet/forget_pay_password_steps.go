package wallet

var (
	// ForgetPayPasswordStepIdentityVerified 身份验证
	ForgetPayPasswordStepIdentityVerified = ForgetPasswordResetStep{value: 1}
	// ForgetPayPasswordStepBankCardVerified 银行卡验证
	ForgetPayPasswordStepBankCardVerified = ForgetPasswordResetStep{value: 2}
	// ForgetPayPasswordStepSmsSent 发送验证码
	ForgetPayPasswordStepSmsSent = ForgetPasswordResetStep{value: 3}
	// ForgetPayPasswordStepSmsVerified 验证验证码
	ForgetPayPasswordStepSmsVerified = ForgetPasswordResetStep{value: 4}
	// ForgetPayPasswordStepPasswordReset 重置密码
	ForgetPayPasswordStepPasswordReset = ForgetPasswordResetStep{value: 5}
)

type ForgetPasswordResetStep struct {
	value int16
}

func (f *ForgetPasswordResetStep) Value() int16 {
	return f.value
}
