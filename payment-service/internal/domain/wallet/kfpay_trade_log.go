package wallet

import (
	"cnb.cool/1024hub/kfpay-go-sdk"
	"github.com/google/uuid"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"strings"
	"time"

	"cnb.cool/cymirror/ces-services/common/db/optimisticlock"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/model"
	"github.com/shopspring/decimal"
)

var (
	// KfpayTradeLogTradeTypePay 支付
	KfpayTradeLogTradeTypePay = KfpayTradeLogTradeType{value: 1}
	// KfpayTradeLogTradeTypeRefund 退款
	KfpayTradeLogTradeTypeRefund = KfpayTradeLogTradeType{value: 2}
	//KfpayTradeLogTradeTypeCollection 收款
	KfpayTradeLogTradeTypeCollection = KfpayTradeLogTradeType{value: 3}
	// KfpayTradeLogTradeTypeRecharge 充值
	KfpayTradeLogTradeTypeRecharge = KfpayTradeLogTradeType{value: 4}
	// KfpayTradeLogTradeTypeWithdraw 提现
	KfpayTradeLogTradeTypeWithdraw = KfpayTradeLogTradeType{value: 5}
)

// KfpayTradeLogTradeType 交易类型
type KfpayTradeLogTradeType struct {
	value int16
}

func NewKfpayTradeLogTradeTypeFromI16(value int16) KfpayTradeLogTradeType {
	switch value {
	case KfpayTradeLogTradeTypePay.value:
		return KfpayTradeLogTradeTypePay
	case KfpayTradeLogTradeTypeRefund.value:
		return KfpayTradeLogTradeTypeRefund
	case KfpayTradeLogTradeTypeCollection.value:
		return KfpayTradeLogTradeTypeCollection
	case KfpayTradeLogTradeTypeRecharge.value:
		return KfpayTradeLogTradeTypeRecharge
	case KfpayTradeLogTradeTypeWithdraw.value:
		return KfpayTradeLogTradeTypeWithdraw
	default:
		panic("invalid value")
	}
}

func (t KfpayTradeLogTradeType) Int16() int16 {
	return t.value
}

func (t KfpayTradeLogTradeType) String() string {
	switch t {
	case KfpayTradeLogTradeTypePay:
		return "支付"
	case KfpayTradeLogTradeTypeRefund:
		return "退款"
	case KfpayTradeLogTradeTypeCollection:
		return "收款"
	case KfpayTradeLogTradeTypeRecharge:
		return "充值"
	case KfpayTradeLogTradeTypeWithdraw:
		return "提现"
	default:
		panic("invalid value")
	}
}

var (
	// KfpayTradeLogStatusSuccess 成功
	KfpayTradeLogStatusSuccess = KfpayTradeLogStatus{value: 1}
	// KfpayTradeLogStatusProcessing 处理中
	KfpayTradeLogStatusProcessing = KfpayTradeLogStatus{value: 2}
	// KfpayTradeLogStatusFail 失败
	KfpayTradeLogStatusFail = KfpayTradeLogStatus{value: 3}
	// KfpayTradeLogStatusCancel 取消
	KfpayTradeLogStatusCancel = KfpayTradeLogStatus{value: 4}
)

// KfpayTradeLogStatus 状态
type KfpayTradeLogStatus struct {
	value int16
}

func NewKfpayTradeLogStatusFromI16(value int16) KfpayTradeLogStatus {
	switch value {
	case KfpayTradeLogStatusSuccess.value:
		return KfpayTradeLogStatusSuccess
	case KfpayTradeLogStatusProcessing.value:
		return KfpayTradeLogStatusProcessing
	case KfpayTradeLogStatusFail.value:
		return KfpayTradeLogStatusFail
	case KfpayTradeLogStatusCancel.value:
		return KfpayTradeLogStatusCancel
	default:
		panic("invalid value")
	}
}

func NewKfpayTradeLogStatusFromKfpayStatus(status kfpay.Status) KfpayTradeLogStatus {
	switch status {
	case kfpay.StatusSuccess:
		return KfpayTradeLogStatusSuccess
	case kfpay.StatusProcessing:
		return KfpayTradeLogStatusProcessing
	case kfpay.StatusFailure:
		return KfpayTradeLogStatusFail
	default:
		return KfpayTradeLogStatusProcessing
	}
}

func (t KfpayTradeLogStatus) Int16() int16 {
	return t.value
}

// KfpayTradeLog 快付通交易日志
type KfpayTradeLog struct {
	ID          string                 // id
	createdAt   time.Time              // 创建时间
	updatedAt   time.Time              // 更新时间
	version     optimisticlock.Version // 乐观锁
	tradeType   KfpayTradeLogTradeType // 交易类型
	amount      decimal.Decimal        // 金额
	userID      string                 // 用户id
	status      KfpayTradeLogStatus    // 状态
	requestNo   string                 // 请求号
	description string                 // 描述
	remark      string                 // 备注
}

func NewKfpayTradeLogFromModel(m *model.PaymentPayKfpayTradeLog) *KfpayTradeLog {
	if m == nil {
		return nil
	}
	return &KfpayTradeLog{
		ID:          m.ID,
		createdAt:   m.CreatedAt,
		updatedAt:   m.UpdatedAt,
		version:     m.Version,
		tradeType:   NewKfpayTradeLogTradeTypeFromI16(m.TradeType),
		amount:      m.Amount,
		userID:      m.UserID,
		status:      NewKfpayTradeLogStatusFromI16(m.Status),
		requestNo:   m.RequestNo,
		description: m.Description,
		remark:      m.Remark,
	}
}

func NewKfpayTradeLogWithReqNo(tradeType KfpayTradeLogTradeType, amount decimal.Decimal,
	userID, description, remark, reqNo string) (*KfpayTradeLog, error) {
	if len(reqNo) > 30 {
		return nil, status.Error(codes.InvalidArgument, "request no is too long")
	}
	return &KfpayTradeLog{
		ID:          "",
		createdAt:   time.Time{},
		updatedAt:   time.Time{},
		version:     optimisticlock.Version{},
		tradeType:   tradeType,
		amount:      amount,
		userID:      userID,
		status:      KfpayTradeLogStatusProcessing,
		requestNo:   reqNo,
		description: description,
		remark:      remark,
	}, nil
}

func NewKfpayTradeLog(tradeType KfpayTradeLogTradeType, amount decimal.Decimal,
	userID, description, remark string) *KfpayTradeLog {
	return &KfpayTradeLog{
		ID:          "",
		createdAt:   time.Time{},
		updatedAt:   time.Time{},
		version:     optimisticlock.Version{},
		tradeType:   tradeType,
		amount:      amount,
		userID:      userID,
		status:      KfpayTradeLogStatusProcessing,
		requestNo:   strings.ReplaceAll(uuid.NewString(), "-", "")[:30],
		description: description,
		remark:      remark,
	}
}

func (l *KfpayTradeLog) ToModel() *model.PaymentPayKfpayTradeLog {
	return &model.PaymentPayKfpayTradeLog{
		ID:          l.ID,
		CreatedAt:   l.createdAt,
		UpdatedAt:   l.updatedAt,
		Version:     l.version,
		TradeType:   l.tradeType.Int16(),
		Amount:      l.amount,
		UserID:      l.userID,
		Status:      l.status.Int16(),
		RequestNo:   l.requestNo,
		Description: l.description,
		Remark:      l.remark,
	}
}

func (l *KfpayTradeLog) CreatedAt() time.Time {
	return l.createdAt
}

func (l *KfpayTradeLog) UpdatedAt() time.Time {
	return l.updatedAt
}

func (l *KfpayTradeLog) Version() optimisticlock.Version {
	return l.version
}

func (l *KfpayTradeLog) TradeType() KfpayTradeLogTradeType {
	return l.tradeType
}

func (l *KfpayTradeLog) Amount() decimal.Decimal {
	return l.amount
}

func (l *KfpayTradeLog) UserID() string {
	return l.userID
}

func (l *KfpayTradeLog) Status() KfpayTradeLogStatus {
	return l.status
}

func (l *KfpayTradeLog) RequestNo() string {
	return l.requestNo
}

func (l *KfpayTradeLog) Remark() string {
	return l.remark
}

func (l *KfpayTradeLog) SetStatus(s KfpayTradeLogStatus) {
	l.status = s
}

func (l *KfpayTradeLog) Description() string {
	return l.description
}

func (l *KfpayTradeLog) SetVersion(v int64) {
	l.version = optimisticlock.Version{
		Int64: v,
		Valid: true,
	}
}

func (l *KfpayTradeLog) SetRemark(remark string) {
	l.remark = remark
}
