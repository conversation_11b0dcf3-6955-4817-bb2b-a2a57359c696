package wallet

import "cnb.cool/cymirror/ces-services/payment-service/gen/gen/model"

// KfpayBankCode 快付通银行code对应的信息
type KfpayBankCode struct {
	ID           string
	bankName     string
	bankTypeCode string
	status       int16
}

func (k *KfpayBankCode) BankName() string {
	return k.bankName
}

func (k *KfpayBankCode) BankTypeCode() string {
	return k.bankTypeCode
}

func (k *KfpayBankCode) IsSupport() bool {
	return k.status == 1
}

func NewKfpayBankCodeFromModel(model *model.PaymentPayKfpayBankCode) *KfpayBankCode {
	if model == nil {
		return nil
	}
	return &KfpayBankCode{
		ID:           model.ID,
		bankName:     model.BankName,
		bankTypeCode: model.BankTypeCode,
		status:       model.Status,
	}
}
