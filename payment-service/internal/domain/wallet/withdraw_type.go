package wallet

import walletpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/wallet"

// WithdrawStatus 提现卡状态
var (
	WithdrawStatusNone      = WithdrawStatus{value: 0} // 非提现卡
	WithdrawStatusReviewing = WithdrawStatus{value: 1} // 提现卡审核中
	WithdrawStatusActive    = WithdrawStatus{value: 2} // 提现卡
	WithdrawStatusFail      = WithdrawStatus{value: 3} //提现卡审核失败
)

type WithdrawStatus struct {
	value int16
}

func (w WithdrawStatus) Value() int16 {
	return w.value
}

func NewWithdrawStatusFromInt16(value int16) WithdrawStatus {
	switch value {
	case 1:
		return WithdrawStatusReviewing
	case 2:
		return WithdrawStatusActive
	case 3:
		return WithdrawStatusFail
	default:
		return WithdrawStatusNone
	}
}

func (w WithdrawStatus) String() string {
	switch w.value {
	case 1:
		return "审核中"
	case 2:
		return "提现卡"
	case 3:
		return "审核失败"
	default:
		return "非提现卡"
	}
}

func (w WithdrawStatus) ToPbWithdrawStatus() walletpb.WithdrawStatus {
	switch w.value {
	case 0:
		return walletpb.WithdrawStatus_None
	case 1:
		return walletpb.WithdrawStatus_Reviewing
	case 2:
		return walletpb.WithdrawStatus_Active
	case 3:
		return walletpb.WithdrawStatus_Fail
	default:
		return walletpb.WithdrawStatus_None
	}
}
