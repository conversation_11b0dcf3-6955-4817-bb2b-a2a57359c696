package wallet

import (
	"cnb.cool/cymirror/ces-services/common/db/optimisticlock"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/model"
	"crypto/rand"
	"encoding/base64"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"strings"
	"time"
)

var (
	_ optimisticlock.Versioned = (*Kfpay)(nil)
	_ Wallet                   = (*Kfpay)(nil)
)

type KfpayWalletPasswordPepper struct {
	pepper []byte
}

func NewKfpayWalletPasswordPepper(pepperBase64 string) *KfpayWalletPasswordPepper {
	bytes, err := base64.StdEncoding.DecodeString(pepperBase64)
	if err != nil {
		panic(err)
	}
	if len(bytes) < 16 {
		panic("pepper is too short")
	}
	return &KfpayWalletPasswordPepper{pepper: bytes}
}

func (p *KfpayWalletPasswordPepper) Pepper() []byte {
	return p.pepper
}

type Kfpay struct {
	ID            string                 // id
	createdTime   time.Time              // 创建时间
	updatedTime   time.Time              // 更新时间
	version       optimisticlock.Version // 乐观锁
	expiredAt     time.Time              // 钱包过期时间
	userID        string                 // 用户id
	secMerchantID *string                // 二级商户id
	realName      string                 // 真实姓名，二级商户名称
	card          string                 // 身份证号
	orderNo       string                 // 快付通开通钱包订单号
	payPassword   string                 // 快付通支付密码
	status        KfpayWalletStatus      // 钱包开通状态
}

func NewKfpay(userID string, realName string, card string) (*Kfpay, error) {
	k := &Kfpay{
		userID:   userID,
		realName: realName,
		card:     card,
		orderNo:  strings.ReplaceAll(uuid.New().String(), "-", "")[:30],
		status:   KfpayStatusAuditing,
	}

	return k, nil
}

func (k *Kfpay) ToModel() *model.PaymentPayKfpayWallet {
	return &model.PaymentPayKfpayWallet{
		ID:            k.ID,
		CreatedAt:     k.createdTime,
		UpdatedAt:     k.updatedTime,
		Version:       k.version,
		ExpiredAt:     k.expiredAt,
		UserID:        k.userID,
		SecMerchantID: k.secMerchantID,
		RealName:      k.realName,
		Card:          k.card,
		OrderNo:       k.orderNo,
		PayPassword:   k.payPassword,
		Status:        k.status.value,
	}
}

func NewKfpayWalletFromModel(model *model.PaymentPayKfpayWallet) *Kfpay {
	if model == nil {
		return nil
	}
	return &Kfpay{
		ID:            model.ID,
		createdTime:   model.CreatedAt,
		updatedTime:   model.UpdatedAt,
		version:       model.Version,
		expiredAt:     model.ExpiredAt,
		userID:        model.UserID,
		secMerchantID: model.SecMerchantID,
		realName:      model.RealName,
		card:          model.Card,
		orderNo:       model.OrderNo,
		payPassword:   model.PayPassword,
		status:        NewKfpayWalletStatusFromInt16(model.Status),
	}
}

func (k *Kfpay) CreatedTime() time.Time {
	return k.createdTime
}

func (k *Kfpay) UpdatedTime() time.Time {
	return k.updatedTime
}

func (k *Kfpay) Version() optimisticlock.Version {
	return k.version
}

func (k *Kfpay) ExpiredAt() time.Time {
	return k.expiredAt
}

func (k *Kfpay) UserID() string {
	return k.userID
}

func (k *Kfpay) SecMerchantID() *string {
	return k.secMerchantID
}

func (k *Kfpay) RealName() string {
	return k.realName
}

func (k *Kfpay) Card() string {
	return k.card
}

func (k *Kfpay) OrderNo() string { return k.orderNo }

func (k *Kfpay) PayPassword() string {
	return k.payPassword
}

func (k *Kfpay) Status() KfpayWalletStatus {
	return k.status
}

func (k *Kfpay) SetCreatedTime(createdTime time.Time) {
	k.createdTime = createdTime
}

func (k *Kfpay) SetUpdatedTime(updatedTime time.Time) {
	k.updatedTime = updatedTime
}

func (k *Kfpay) SetVersion(version int64) {
	k.version = optimisticlock.Version{
		Int64: version,
		Valid: true,
	}
}

func (k *Kfpay) SetExpiredAt(expiredAt time.Time) {
	k.expiredAt = expiredAt
}

func (k *Kfpay) SetUserID(userID string) {
	k.userID = userID
}

func (k *Kfpay) SetSecMerchantID(secMerchantID *string) {
	k.secMerchantID = secMerchantID
}

func (k *Kfpay) SetRealName(realName string) {
	k.realName = realName
}

func (k *Kfpay) SetCard(card string) {
	k.card = card
}

func (k *Kfpay) SetOrderNo(orderNo string) {
	k.orderNo = orderNo
}

func (k *Kfpay) SetStatus(status KfpayWalletStatus) {
	k.status = status
}

func (k *Kfpay) SetPayPassword(payPassword string, pepper []byte) error {
	if len(payPassword) != 6 {
		return status.Error(codes.InvalidArgument, "payPassword length must be 6")
	}

	// 校验支付密码是否为纯数字
	for _, c := range payPassword {
		if c < '0' || c > '9' {
			return status.Error(codes.InvalidArgument, "payPassword must be a number")
		}
	}
	// 生成16字节随机盐
	salt := make([]byte, 16)
	if _, err := rand.Read(salt); err != nil {
		return err
	}

	// 将 salt + 原始密码 + pepper 组合后进行bcrypt哈希
	raw := make([]byte, len(payPassword)+len(pepper))
	copy(raw, payPassword)
	copy(raw[len(payPassword):], pepper)
	hashedPassword, err := bcrypt.GenerateFromPassword(raw, bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	k.payPassword = string(hashedPassword)
	return nil
}

func (k *Kfpay) ValidatePassword(password string, pepper []byte) bool {
	if len(password) != 6 {
		// 避免空字符串匹配
		return false
	}
	raw := make([]byte, len(password)+len(pepper))
	copy(raw, password)
	copy(raw[len(password):], pepper)
	return bcrypt.CompareHashAndPassword([]byte(k.payPassword), raw) == nil
}

func (k *Kfpay) IsOpen() bool {
	if k == nil || k.SecMerchantID() == nil || len(*k.SecMerchantID()) == 0 || k.status != KfpayStatusOpen {
		return false
	}
	return true
}

func (k *Kfpay) IsAuditing() bool {
	return k.status == KfpayStatusAuditing
}

func (k *Kfpay) SecondaryMerchantID() string {
	return *k.SecMerchantID()
}

func (k *Kfpay) WalletType() Type {
	return TypeKfpay
}
