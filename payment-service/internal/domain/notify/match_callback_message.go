package notify

import (
	"time"

	"cnb.cool/cymirror/ces-services/common/db/optimisticlock"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/model"
)

type MatchCallbackMessage struct {
	ID          string                 // id
	createdAt   time.Time              // 创建时间
	updatedAt   time.Time              // 更新时间
	version     optimisticlock.Version // 乐观锁
	tradeID     string                 // 交易id
	tryCount    int32                  // 尝试次数
	nextTryTime time.Time              // 下次尝试时间
	divideReqNo *string                // 分账请求号
	refundReqNo *string                // 退款请求号
}

func NewMatchCallbackMessageFromModel(model *model.PaymentMatchCallbackMessage) *MatchCallbackMessage {
	return &MatchCallbackMessage{
		ID:          model.ID,
		createdAt:   model.CreatedAt,
		updatedAt:   model.UpdatedAt,
		version:     model.Version,
		tradeID:     model.TradeID,
		tryCount:    model.TryCount,
		nextTryTime: model.NextTryTime,
		divideReqNo: model.DivideReqNo,
		refundReqNo: model.RefundReqNo,
	}
}

func (m *MatchCallbackMessage) ToModel() *model.PaymentMatchCallbackMessage {
	return &model.PaymentMatchCallbackMessage{
		ID:          m.ID,
		CreatedAt:   m.createdAt,
		UpdatedAt:   m.updatedAt,
		Version:     m.version,
		TradeID:     m.tradeID,
		TryCount:    m.tryCount,
		NextTryTime: m.nextTryTime,
		DivideReqNo: m.divideReqNo,
		RefundReqNo: m.refundReqNo,
	}
}

func (m *MatchCallbackMessage) TradeID() string {
	return m.tradeID
}

func (m *MatchCallbackMessage) DivideReqNo() *string {
	return m.divideReqNo
}

func (m *MatchCallbackMessage) SetDivideReqNo(divideReqNo string) {
	m.divideReqNo = &divideReqNo
}

func (m *MatchCallbackMessage) RefundReqNo() *string {
	return m.refundReqNo
}

func (m *MatchCallbackMessage) SetRefundReqNo(refundReqNo string) {
	m.refundReqNo = &refundReqNo
}

func (s *MatchCallbackMessage) Version() optimisticlock.Version {
	return s.version
}

func (s *MatchCallbackMessage) SetVersion(v int64) {
	s.version = optimisticlock.Version{Int64: v, Valid: true}
}
