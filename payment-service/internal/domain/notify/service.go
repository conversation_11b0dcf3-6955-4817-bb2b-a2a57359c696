package notify

import (
	"context"
	"time"

	"cnb.cool/1024hub/kfpay-go-sdk"
	"cnb.cool/cymirror/ces-services/payment-service/internal/infra/yeepay"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/divide"
	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/pay"

	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/common/server"
	matchpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/match"
	nftpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/nft"
	purchasereqpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/purchasereq"
	"cnb.cool/cymirror/ces-services/payment-service/gen/gen/query"

	"cnb.cool/cymirror/ces-services/payment-service/internal/domain/order"
	"go.uber.org/zap"
)

// PayNotify 通用支付通知结构体
type PayNotify struct {
	PaySuccessDate time.Time // 支付成功时间
	OrderId        string    // 订单ID
	UniqueOrderNo  string    // 第三方订单号
}

// Service 通知服务
type Service struct {
	orderRepo           order.Repo
	payRepo             pay.Repo
	divideRepo          divide.Repo
	nftServiceClient    nftpb.NFTServiceClient
	purchaseReqClient   purchasereqpb.PurchaseReqServiceClient
	orderService        *order.OrderService
	divideService       *divide.Service
	yeepayClient        *yeepay.Client
	kfpayClient         *kfpay.Client
	matchServiceClient  matchpb.MatchServiceClient
	MatchCallbackAesKey *MatchCallbackAesKey
}

func NewService(orderRepo order.Repo, payRepo pay.Repo, divideRepo divide.Repo, nftServiceClient nftpb.NFTServiceClient, purchaseReqClient purchasereqpb.PurchaseReqServiceClient, orderService *order.OrderService, divideService *divide.Service, yeepayClient *yeepay.Client, kfpayClient *kfpay.Client, matchServiceClient matchpb.MatchServiceClient, matchCallbackAesKey *MatchCallbackAesKey) *Service {
	return &Service{orderRepo: orderRepo, payRepo: payRepo, divideRepo: divideRepo, nftServiceClient: nftServiceClient, purchaseReqClient: purchaseReqClient, orderService: orderService, divideService: divideService, yeepayClient: yeepayClient, kfpayClient: kfpayClient, matchServiceClient: matchServiceClient, MatchCallbackAesKey: matchCallbackAesKey}
}

// ProcessPayNotifyCore 处理支付通知核心逻辑
func (s *Service) ProcessPayNotifyCore(ctx context.Context, notify *PayNotify) error {
	// 获取订单信息
	o, err := s.orderRepo.GetOrderByID(ctx, notify.OrderId)
	if err != nil {
		return err
	}

	// 校验
	if o.ExpiredTime().Add(time.Second * 40).Before(time.Now()) {
		zap.L().Warn("process pay notify error order is expired", zap.String("order_id", o.ID))
		return server.InternalStatus
	}
	if !o.Status().Equal(order.StatusPaying) {
		zap.L().Warn("order status not paying", zap.String("order_id", o.ID))
		return server.InternalStatus
	}

	switch o.OrderType() {
	case order.TypePrimaryMarket:
		return s.processMarketPayNotify(ctx, notify, o)
	case order.TypeSecondaryMarket:
		return s.processMarketPayNotify(ctx, notify, o)
	case order.TypePurchaseRequest:
		return s.processPurchaseRequestPayNotify(ctx, notify, o)
	case order.TypeFlashBuy:
		return s.processFlashBuyPayNotify(ctx, notify, o)
	default:
		zap.L().Error("order type not support", zap.String("order_id", o.ID))
		return server.InternalStatus
	}
}

// ---------- 处理支付成功通用逻辑开始 ----------

func (s *Service) processMarketPayNotify(ctx context.Context, notify *PayNotify, o *order.Order) error {
	// 处理订单支付成功的通用逻辑
	err := s.MakeOrderPaySuccess(ctx, o, notify)
	if err != nil {
		return err
	}

	err = s.PaySuccessPostProcess(ctx, o, notify)
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) processPurchaseRequestPayNotify(ctx context.Context, notify *PayNotify, o *order.Order) error {
	// 处理订单支付成功的通用逻辑
	if err := s.MakeOrderPaySuccess(ctx, o, notify); err != nil {
		return err
	}

	// 事务外的后置处理：通知求购单支付成功
	return s.paySuccessCreatePurchaseReq(ctx, o)
}

func (s *Service) processFlashBuyPayNotify(ctx context.Context, notify *PayNotify, o *order.Order) error {
	// 处理订单支付成功的通用逻辑
	if err := s.MakeOrderPaySuccess(ctx, o, notify); err != nil {
		return err
	}
	// 后置处理，通知闪购买单支付成功
	return s.paySuccessCreateFlashBuyOrder(ctx, o)
}
func (s *Service) MakeOrderPaySuccess(ctx context.Context, o *order.Order, notify *PayNotify) error {
	// 设置订单为支付成功状态
	if err := o.MakePaySuccess(notify.PaySuccessDate); err != nil {
		return err
	}

	// 在同一个事务中更新订单以及支付相关信息，并执行可选的扩展逻辑
	if err := db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
		// 更新订单状态
		if err := s.orderRepo.ApplyPayNotify(ctx, o); err != nil {
			return err
		}

		// 更新支付渠道对应的订单信息
		switch o.PayType() {
		case order.PayTypeYeePayQuick, order.PayTypeYeePayWallet:
			// 易宝支付（快捷支付和钱包支付）
			yeepayPay, err := s.payRepo.GetYeepayOrderByOrderID(ctx, o.ID)
			if err != nil {
				return err
			}
			yeepayPay.SetPayAt(notify.PaySuccessDate)
			yeepayPay.SetYeepayOrderNo(notify.UniqueOrderNo)
			err = s.payRepo.ApplyYeepayOrderPaySuccess(ctx, yeepayPay) // TODO: 修改为Update方法
			if err != nil {
				return err
			}
		//case order.PayTypeKfpayBalance, order.PayTypeKfpayBankCard:
		case order.PayTypeKfpayBalance:
			// 快付通支付（余额支付和银行卡支付）
			kfpayPay, err := s.payRepo.GetLatestKfpayOrderByOrderID(ctx, o.ID)
			if err != nil {
				return err
			}
			if kfpayPay != nil {
				kfpayPay.SetPayAt(notify.PaySuccessDate)
				kfpayPay.SetRequestNo(notify.UniqueOrderNo)
				err = s.payRepo.UpdateKfpayOrder(ctx, kfpayPay)
				if err != nil {
					return err
				}
			}
		default:
			zap.L().Error("pay type not support", zap.String("order_id", o.ID), zap.Int16("pay_type", o.PayType().Int16()))
			return server.InternalStatus
		}

		// 如果是二级市场订单，需要在事务内写入分账消息
		if o.OrderType().Equal(order.TypeSecondaryMarket) {
			return s.divideRepo.CreateSecondaryDivideMessage(ctx, divide.NewSecondaryDivideMessage(o.ID))
		}

		return nil
	}); err != nil {
		return err
	}

	return nil
}

func (s *Service) PaySuccessPostProcess(ctx context.Context, o *order.Order, notify *PayNotify) error {
	if !o.Status().Equal(order.StatusSuccess) {
		return status.Error(codes.FailedPrecondition, "order status is success")
	}

	switch o.OrderType() {
	case order.TypePrimaryMarket, order.TypeSecondaryMarket:
		// 转移藏品
		err := s.paySuccessTransferNFT(ctx, o, notify)
		if err != nil {
			return err
		}
		// 二级市场订单触发分账
		if o.OrderType().Equal(order.TypeSecondaryMarket) {
			if _, err := s.divideService.DivideSecondaryMarketOrder(ctx, o); err != nil {
				zap.L().Error("divide secondary market order failed", zap.Error(err), zap.String("order_id", notify.OrderId))
			}
		}
	case order.TypePurchaseRequest:
		err := s.paySuccessCreatePurchaseReq(ctx, o)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *Service) paySuccessCreatePurchaseReq(ctx context.Context, o *order.Order) error {
	if !o.Status().Equal(order.StatusSuccess) {
		return status.Error(codes.FailedPrecondition, "order status is success")
	}
	// rpc 应用求购单支付成功
	_, err := s.purchaseReqClient.ApplyPurchaseRequestPaySuccessRPC(ctx, &purchasereqpb.ApplyPurchaseRequestPaySuccessRPCReq{
		OrderID:    o.ID,
		WalletType: o.PayType().ToPurchaseReqWalletTypePB(),
	})
	if err != nil {
		zap.L().Error("apply purchase req pay success rpc failed", zap.Error(err), zap.String("order_id", o.ID))
	}
	return nil
}

func (s *Service) paySuccessCreateFlashBuyOrder(ctx context.Context, o *order.Order) error {
	if !o.Status().Equal(order.StatusSuccess) {
		return status.Error(codes.FailedPrecondition, "order status is success")
	}
	// rpc 应用闪购买单支付成功
	_, err := s.matchServiceClient.ApplyFlashBuyOrderPaySuccessRPC(ctx, &matchpb.ApplyFlashBuyOrderPaySuccessRPCReq{
		OrderId:    o.ID,
		WalletType: matchpb.WalletType(o.PayType().Int16()),
	})
	if err != nil {
		zap.L().Error("apply flash buy order pay success rpc failed", zap.Error(err), zap.String("order_id", o.ID))
	}
	return nil
}

func (s *Service) paySuccessTransferNFT(ctx context.Context, o *order.Order, notify *PayNotify) error {
	if !o.Status().Equal(order.StatusSuccess) {
		return status.Error(codes.FailedPrecondition, "order status is success")
	}

	rpcReq := &nftpb.ApplyPaySuccessTransferNFTOwnerRPCReq{
		UserID:     o.UserID(),
		OrderID:    o.ID,
		MarketType: o.OrderType().ToNFTPB(),
		PayTime:    notify.PaySuccessDate.UnixMilli(),
		Extra:      nil,
	}
	// 根据订单类型获取额外信息
	switch o.OrderType() {
	case order.TypePrimaryMarket:
		nftID2PrimaryExtID, err := s.orderService.GetMarketOrderNFTID2ExtraIDMap(ctx, o)
		if err != nil {
			return err
		}
		rpcReq.Extra = &nftpb.ApplyPaySuccessTransferNFTOwnerRPCReq_PrimaryMarketExtra{
			PrimaryMarketExtra: &nftpb.ApplyPaySuccessTransferNFTOwnerRPCReq_PrimaryMarket{
				NftID2PrimaryExtID: nftID2PrimaryExtID,
			},
		}
	case order.TypeSecondaryMarket:
		nftID2SecondaryExtIDMap, err := s.orderService.GetMarketOrderNFTID2ExtraIDMap(ctx, o)
		if err != nil {
			return err
		}
		rpcReq.Extra = &nftpb.ApplyPaySuccessTransferNFTOwnerRPCReq_SecondaryMarketExtra{
			SecondaryMarketExtra: &nftpb.ApplyPaySuccessTransferNFTOwnerRPCReq_SecondaryMarket{
				NftID2SecondaryExtID: nftID2SecondaryExtIDMap,
			},
		}
	}
	// rpc 转移nft所有者
	_, err := s.nftServiceClient.ApplyPaySuccessTransferNFTOwnerRPC(ctx, rpcReq)
	if err != nil {
		zap.L().Error("transfer nft failed", zap.Error(err), zap.String("order_id", notify.OrderId))
	}

	return nil
}

// ---------- 处理支付成功通用逻辑结束 ----------

func (s *Service) ReverseLookupAndUpdateOrderStatus(ctx context.Context, o *order.Order) error {
	if o == nil {
		return status.Error(codes.NotFound, "order not found")
	}

	// 如果不是支付中状态，不需要反查，状态已经流转
	if !o.Status().Equal(order.StatusPaying) || o.Status().Equal(order.StatusSuccess) {
		return nil
	}

	var notify *PayNotify
	switch o.PayType() {
	case order.PayTypeYeePayQuick, order.PayTypeYeePayWallet:
		yeepayOrderInfo, err := s.yeepayClient.GetOrderInfo(&yeepay.QueryOrderCmd{
			OrderID: o.ID,
		})
		if err != nil {
			return err
		}
		if yeepayOrderInfo.Status == yeepay.QueryOrderStatusSuccess {
			notify = &PayNotify{
				PaySuccessDate: yeepayOrderInfo.PayTime,
				OrderId:        o.ID,
				UniqueOrderNo:  "", // 没啥用
			}
		}
	//case order.PayTypeKfpayBalance, order.PayTypeKfpayBankCard:
	case order.PayTypeKfpayBalance:
		kfpayOrder, err := s.payRepo.GetLatestKfpayOrderByOrderID(ctx, o.ID)
		if err != nil {
			return err
		}
		if kfpayOrder == nil {
			return status.Error(codes.NotFound, "kfpay order not found")
		}
		kfpayOrderInfo, err := s.kfpayClient.QueryTradeRecord(ctx, &kfpay.QueryTradeRecordCmd{
			OriginalOrderNo: kfpayOrder.RequestNo(),
		})
		if err != nil {
			return err
		}
		if kfpayOrderInfo.Status == kfpay.StatusSuccess {
			notify = &PayNotify{
				PaySuccessDate: time.Now(),
				OrderId:        o.ID,
				UniqueOrderNo:  "", // 没啥用
			}
		}
	default:
		return status.Error(codes.Unimplemented, "not implement")
	}
	// 不成功返回
	if notify == nil {
		return nil
	}
	// 成功修改订单状态
	err := s.MakeOrderPaySuccess(ctx, o, notify)
	if err != nil {
		return err
	}
	return nil
}
