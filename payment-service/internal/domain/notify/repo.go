package notify

import "context"

type Repo interface {
	// GetMatchCallbackMessageByTradeID 获取撮合回调兜底消息
	GetMatchCallbackMessageByTradeID(ctx context.Context, tradeID string) (*MatchCallbackMessage, error)
	// UpdateMatchCallbackMessage 更新撮合回调兜底消息
	UpdateMatchCallbackMessage(ctx context.Context, message *MatchCallbackMessage) (int64, error)
	// DeleteMatchCallbackMessageByTradeID 删除撮合回调兜底消息
	DeleteMatchCallbackMessageByTradeID(ctx context.Context, tradeID string) (int64, error)

	// CreateMatchCallbackHistory 创建撮合回调历史
	CreateMatchCallbackHistory(ctx context.Context, tradeID string) error
}
