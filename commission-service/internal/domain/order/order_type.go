package order

var (
	// SecondaryMarket 二级市场订单
	SecondaryMarket = Type{value: 2}
	// PurchaseRequest 购买请求订单
	PurchaseRequest = Type{value: 3}
)

type Type struct {
	value int16
}

func (o Type) Int16() int16 {
	return o.value
}

func NewOrderTypeFromInt16(value int16) Type {
	switch value {
	case 2:
		return SecondaryMarket
	case 3:
		return PurchaseRequest
	default:
		return SecondaryMarket
	}
}

func (o Type) ToModel() int16 {
	return o.value
}
