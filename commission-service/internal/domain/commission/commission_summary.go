package commission

import (
	"cnb.cool/cymirror/ces-services/commission-service/gen/gen/model"
	"github.com/shopspring/decimal"
	"time"
)

type CommissionSummary struct {
	ID                    string
	UserID                string
	TotalCommissionAmount decimal.Decimal
	CreatedAt             time.Time
	UpdatedAt             time.Time
}

func NewCommissionSummary(id string, userID string, totalCommissionAmount decimal.Decimal, createdAt time.Time, updatedAt time.Time) *CommissionSummary {
	return &CommissionSummary{
		ID:                    id,
		UserID:                userID,
		TotalCommissionAmount: totalCommissionAmount,
		CreatedAt:             createdAt,
		UpdatedAt:             updatedAt,
	}
}

func (c CommissionSummary) ToModel() *model.CommissionSummary {
	return &model.CommissionSummary{
		ID:                    c.ID,
		UserID:                c.UserID,
		TotalCommissionAmount: c.TotalCommissionAmount,
		CreatedAt:             c.CreatedAt,
		UpdatedAt:             c.UpdatedAt,
	}
}
