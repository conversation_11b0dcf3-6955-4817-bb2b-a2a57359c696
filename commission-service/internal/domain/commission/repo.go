package commission

import (
	"context"

	"github.com/shopspring/decimal"
)

type Repo interface {
	// CreateCommissions 保存多条分佣记录
	CreateCommissions(ctx context.Context, commissions []*Commission) error
	// GetCommissionsRecord 获取分佣记录
	GetCommissionsRecord(ctx context.Context, userID string, startTime int64, endTime int64, page int32) ([]*Commission, int64, error)
	// GetCommissionTotal 获取分佣总数
	GetCommissionTotal(ctx context.Context, userID string) (*decimal.Decimal, error)
	// GetCommissionTotalByUserIDs 获取多个用户的分佣总数
	GetCommissionTotalByUserIDs(ctx context.Context, userIDs []string) ([]*CommissionSummary, error)
	// UpdateCommissionTotalByUserIDs 更新多个用户的分佣总数
	UpdateCommissionTotalByUserIDs(ctx context.Context, totals []*CommissionSummary) error
	// CreateCommissionSummaries 批量新增分佣总数记录
	CreateCommissionSummaries(ctx context.Context, summaries []*CommissionSummary) error
	// CheckCommissionsKeyExist 根据key查询分佣记录
	CheckCommissionsKeyExist(ctx context.Context, key string) (bool, error)
}
