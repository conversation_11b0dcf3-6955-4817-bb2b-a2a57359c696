package commission

import (
	"cnb.cool/cymirror/ces-services/commission-service/gen/gen/model"
	"cnb.cool/cymirror/ces-services/commission-service/internal/domain/order"
	dividepb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/divide"
	"github.com/shopspring/decimal"
	"time"
)

type Commission struct {
	ID                    string          // id
	OrderID               string          // 订单ID
	UserID                string          // userID
	InviteeID             string          // 邀请者ID
	CommissionRuleID      string          // 分佣规则ID
	CommissionRateApplied decimal.Decimal // 分佣比例
	CommissionAmount      decimal.Decimal // 分佣金额
	CreatedAt             time.Time       // 创建时间
	UpdatedAt             time.Time       // 更新时间
	IdempotencyKey        string          // 幂等性key
	NftID                 string          // NFT ID
	TradeType             order.Type      // 订单类型
	TradeTypeExtID        string          // 订单类型扩展ID
}

func NewCommission(
	divideFee *dividepb.GetDivideFeeByTimeRPCResp_DivideFee,
	inviterID string,
	startTime time.Time,
	endTime time.Time,
	ruleID string,
	commissionRate decimal.Decimal,
	commissionAmount decimal.Decimal,
	orderType order.Type,
	orderTypeExtID string,
) *Commission {
	return &Commission{
		OrderID:               divideFee.OrderID,
		UserID:                inviterID,
		InviteeID:             divideFee.UserID,
		CommissionRuleID:      ruleID,
		CommissionRateApplied: commissionRate,
		CommissionAmount:      commissionAmount,
		// 幂等key 格式： 开始时间:结束时间:订单ID:订单类型扩展ID
		IdempotencyKey: startTime.Format("2006-01-02 15:04:05") + ":" + endTime.Format("2006-01-02 15:04:05") + ":" + divideFee.OrderID + ":" + orderTypeExtID,
		NftID:          divideFee.NftID,
		TradeType:      orderType,
		TradeTypeExtID: orderTypeExtID,
	}
}

func (c *Commission) ToModel() *model.Commission {
	return &model.Commission{
		ID:                    c.ID,
		OrderID:               c.OrderID,
		UserID:                c.UserID,
		InviteeID:             c.InviteeID,
		CommissionRuleID:      c.CommissionRuleID,
		CommissionRateApplied: c.CommissionRateApplied,
		CommissionAmount:      c.CommissionAmount,
		CreatedAt:             c.CreatedAt,
		UpdatedAt:             c.UpdatedAt,
		IdempotencyKey:        c.IdempotencyKey,
		NftID:                 c.NftID,
		TradeType:             c.TradeType.Int16(),
		TradeTypeExtID:        c.TradeTypeExtID,
	}
}

func NewCommissionFromModel(m *model.Commission) *Commission {
	return &Commission{
		ID:                    m.ID,
		OrderID:               m.OrderID,
		UserID:                m.UserID,
		InviteeID:             m.InviteeID,
		CommissionRuleID:      m.CommissionRuleID,
		CommissionRateApplied: m.CommissionRateApplied,
		CommissionAmount:      m.CommissionAmount,
		CreatedAt:             m.CreatedAt,
		UpdatedAt:             m.UpdatedAt,
		IdempotencyKey:        m.IdempotencyKey,
		NftID:                 m.NftID,
		TradeType:             order.NewOrderTypeFromInt16(m.TradeType),
		TradeTypeExtID:        m.TradeTypeExtID,
	}
}
