package commission

import (
	"cnb.cool/cymirror/ces-services/commission-service/internal/domain/order"
	purchasereqpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/purchasereq"
	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
	"github.com/shopspring/decimal"
	"time"
)

type Service struct {
}

func NewService() *Service {
	return &Service{}
}

func (s *Service) GetKeyPrefix(startTime, endTime time.Time) string {
	return startTime.Format("2006-01-02 15:04:05") + ":" + endTime.Format("2006-01-02 15:04:05")
}

const (
	specialProjectID = "2f07d61d-b758-4a29-8418-1a1b06f22bf8"
	specialFeeRate   = 0.06
	normalFeeRate    = 0.05
)

type DivideFee struct {
	UserID  string
	OrderID string
	Fee     string
	NftID   string
	Type    order.Type
	ExtID   string
}

func (s *Service) CalcSecondaryOrderDivideFees(orders []*orderpb.GetSecondaryOrdersByTimeRPCResp_SecondaryOrder) []DivideFee {
	var fees []DivideFee
	for _, secondaryOrder := range orders {
		price, err := decimal.NewFromString(secondaryOrder.GetPrice())
		if err != nil {
			continue
		}
		var rate float64
		if secondaryOrder.GetProjectID() == specialProjectID {
			rate = specialFeeRate
		} else {
			rate = normalFeeRate
		}
		fee := price.Mul(decimal.NewFromFloat(rate))
		extID := secondaryOrder.ID
		fees = append(fees, DivideFee{
			UserID:  secondaryOrder.UserID,
			OrderID: secondaryOrder.OrderID,
			Fee:     fee.String(),
			NftID:   secondaryOrder.NftID,
			Type:    order.SecondaryMarket,
			ExtID:   extID,
		})
	}
	return fees
}

func (s *Service) CalcPurchaseReqDivideFees(details []*purchasereqpb.GetPurchaseRequestDetailsByTimeRPCResp_PurchaseRequestSupplyDetail) []DivideFee {
	var fees []DivideFee
	for _, d := range details {
		price, err := decimal.NewFromString(d.GetUnitPrice())
		if err != nil {
			continue
		}
		var rate float64
		if d.GetProjectID() == specialProjectID {
			rate = specialFeeRate
		} else {
			rate = normalFeeRate
		}
		fee := price.Mul(decimal.NewFromFloat(rate))
		extID := d.SupplyDetailID
		fees = append(fees, DivideFee{
			UserID:  d.UserID,
			OrderID: d.OrderID,
			Fee:     fee.String(),
			NftID:   d.NftID,
			Type:    order.PurchaseRequest,
			ExtID:   extID,
		})
	}
	return fees
}
