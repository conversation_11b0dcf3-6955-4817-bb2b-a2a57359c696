package rule

import (
	"time"

	"cnb.cool/cymirror/ces-services/commission-service/gen/gen/model"
	"github.com/shopspring/decimal"
)

type CommissionRule struct {
	id                                 string
	ruleName                           string
	description                        *string
	minValidInvitedUsers               int32
	minInviterCumulativeInviteeTxValue decimal.Decimal
	commissionPercentage               decimal.Decimal
	isActive                           bool
	createdAt                          time.Time
	updatedAt                          time.Time
}

func NewCommissionRuleFromModel(m *model.CommissionRule) *CommissionRule {
	return &CommissionRule{
		id:                                 m.ID,
		ruleName:                           m.RuleName,
		description:                        m.Description,
		minValidInvitedUsers:               m.MinValidInvitedUsers,
		minInviterCumulativeInviteeTxValue: m.MinInviterCumulativeInviteeTxValue,
		commissionPercentage:               m.CommissionPercentage,
		isActive:                           m.IsActive,
		createdAt:                          m.CreatedAt,
		updatedAt:                          m.UpdatedAt,
	}
}

func (c *CommissionRule) ToModel() *model.CommissionRule {
	return &model.CommissionRule{
		ID:                                 c.id,
		RuleName:                           c.ruleName,
		Description:                        c.description,
		MinValidInvitedUsers:               c.minValidInvitedUsers,
		MinInviterCumulativeInviteeTxValue: c.minInviterCumulativeInviteeTxValue,
		CommissionPercentage:               c.commissionPercentage,
		IsActive:                           c.isActive,
		CreatedAt:                          c.createdAt,
		UpdatedAt:                          c.updatedAt,
	}
}

// ID returns the commission rule id
func (c *CommissionRule) ID() string {
	return c.id
}

// MinValidInvitedUsers returns the minimum number of valid invited users
func (c *CommissionRule) MinValidInvitedUsers() int32 {
	return c.minValidInvitedUsers
}

// CommissionPercentage returns the commission percentage
func (c *CommissionRule) CommissionPercentage() decimal.Decimal {
	return c.commissionPercentage
}

// IsActive returns whether the rule is active
func (c *CommissionRule) IsActive() bool {
	return c.isActive
}
