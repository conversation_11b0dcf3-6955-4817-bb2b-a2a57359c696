package cron

import (
	"context"
	"errors"
	"github.com/bsm/redislock"
	"time"

	commissionpb "cnb.cool/cymirror/ces-services/commission-service/gen/proto/ces/commission"
	"cnb.cool/cymirror/ces-services/commission-service/internal/application/commission"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

var _ cron.Job = (*CreateCommissionRecordJob)(nil)
var createCommissionLockKey = "cron:commission:create"
var createCommissionLockTimeout = 10 * time.Minute

type CreateCommissionRecordJob struct {
	parentContext context.Context
	application   *commission.Application
	lock          *redislock.Client
}

func NewCreateCommissionRecordJob(parentContext context.Context, application *commission.Application, lock *redislock.Client) *CreateCommissionRecordJob {
	return &CreateCommissionRecordJob{parentContext: parentContext, application: application, lock: lock}
}

func (c *CreateCommissionRecordJob) Run() {
	ctx, cancel := context.WithTimeout(c.parentContext, createCommissionLockTimeout-time.Second)
	defer cancel()

	zap.L().Info("CreateCommissionRecordJob start")

	//左闭右开区间
	now := time.Now()
	currentHour := transformTime(now)

	endTime := currentHour

	//startTime := currentHour.Add(-3 * time.Hour)
	//startTime := currentHour.Add(-20 * time.Minute)

	startTime := currentHour.Add(-10 * time.Minute)

	lock, err := c.lock.Obtain(ctx, createCommissionLockKey, createCommissionLockTimeout, &redislock.Options{})
	if err != nil && !errors.Is(err, redislock.ErrNotObtained) {
		zap.L().Error("Obtain lock failed", zap.Error(err))
		return
	} else if errors.Is(err, redislock.ErrNotObtained) {
		return
	}
	defer func() {
		err := lock.Release(context.Background())
		if err != nil {
			zap.L().Error("Release lock failed", zap.Error(err))
		}
	}()

	_, err = c.application.CreateCommissionRecord(ctx, &commissionpb.CreateCommissionRecordRPCReq{
		StartTime: startTime.UnixMilli(),
		EndTime:   endTime.UnixMilli(),
	})

	if err != nil {
		zap.L().Error("CreateCommissionRecord failed", zap.Error(err))
		return
	}
}

func transformTime(originalTime time.Time) time.Time {
	return originalTime.Truncate(10 * time.Minute)
}
