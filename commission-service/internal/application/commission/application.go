package commission

import (
	"context"
	"strconv"
	"time"

	profilepb "cnb.cool/cymirror/ces-services/user-service/gen/proto/ces/user/profile"

	nftpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/nft"

	purchasereqpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/purchasereq"
	orderpb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/order"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"cnb.cool/cymirror/ces-services/commission-service/gen/gen/query"
	"cnb.cool/cymirror/ces-services/common/db"
	dividepb "cnb.cool/cymirror/ces-services/payment-service/gen/proto/ces/payment/divide"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"

	commissionpb "cnb.cool/cymirror/ces-services/commission-service/gen/proto/ces/commission"
	"cnb.cool/cymirror/ces-services/commission-service/internal/domain/commission"
	"cnb.cool/cymirror/ces-services/commission-service/internal/domain/rule"
	"cnb.cool/cymirror/ces-services/common/auth"
	invitationpb "cnb.cool/cymirror/ces-services/user-service/gen/proto/ces/user/invitation"
)

type Application struct {
	commissionRepo    commission.Repo
	ruleRepo          rule.Repo
	orderClient       orderpb.OrderServiceClient
	nftClient         nftpb.NFTServiceClient
	profileClient     profilepb.ProfileServiceClient
	purchaseReqClient purchasereqpb.PurchaseReqServiceClient
	invitationClient  invitationpb.InvitationServiceClient
	commissionService *commission.Service
}

func NewApplication(commissionRepo commission.Repo, ruleRepo rule.Repo, orderClient orderpb.OrderServiceClient, nftClient nftpb.NFTServiceClient, profileClient profilepb.ProfileServiceClient, purchaseReqClient purchasereqpb.PurchaseReqServiceClient, invitationClient invitationpb.InvitationServiceClient, commissionService *commission.Service) *Application {
	return &Application{commissionRepo: commissionRepo, ruleRepo: ruleRepo, orderClient: orderClient, nftClient: nftClient, profileClient: profileClient, purchaseReqClient: purchaseReqClient, invitationClient: invitationClient, commissionService: commissionService}
}

func (a *Application) GetCommissionsRecord(ctx context.Context, req *commissionpb.GetCommissionsRecordReq) (*commissionpb.GetCommissionsRecordResp, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, ErrUnAuthStatus
	}
	commissions, total, err := a.commissionRepo.GetCommissionsRecord(ctx, userID, req.StartTime, req.EndTime, req.Page)
	if err != nil {
		return nil, err
	}

	var orderIDs []string
	for _, c := range commissions {
		orderIDs = append(orderIDs, c.OrderID)
	}
	// 获取订单时间
	rpc, err := a.orderClient.GetOrderDetailByOrderIDsRPC(ctx, &orderpb.GetOrderDetailByOrderIDsRPCReq{
		OrderIDs: orderIDs,
	})
	if err != nil {
		return nil, err
	}

	// 构建 orderID -> orderTime 映射
	orderTimeMap := make(map[string]int64, len(rpc.GetOrders()))
	for _, o := range rpc.GetOrders() {
		orderTimeMap[o.GetID()] = o.GetCreatedAt()
	}

	// 获取邀请人信息
	var inviteeIDs []string
	for _, c := range commissions {
		inviteeIDs = append(inviteeIDs, c.InviteeID)
	}
	users, err := a.profileClient.BatchGetProfileRPC(ctx, &profilepb.BatchGetProfileRPCReq{
		UserID: inviteeIDs,
	})
	if err != nil {
		return nil, err
	}

	// 构建 inviteeID -> phone 映射
	invitePhoneMap := make(map[string]string, len(users.GetProfileList()))
	for _, u := range users.GetProfileList() {
		invitePhoneMap[u.GetUserId()] = u.GetPhone()
	}

	var nftIDs []string
	for _, c := range commissions {
		nftIDs = append(nftIDs, c.NftID)
	}
	nfts, err := a.nftClient.GetNFTByIDsRPC(ctx, &nftpb.GetNFTByIDsRPCReq{
		NftIDs: nftIDs,
	})
	if err != nil {
		return nil, err
	}

	// 构建 nftID -> nft 映射
	// 将查询到的nft信息缓存至map，方便后续查找
	var nftMap = make(map[string]*nftpb.GetNFTByIDsRPCResp_NFT, len(nfts.GetNfts()))
	for _, n := range nfts.GetNfts() {
		nftMap[n.GetId()] = n
	}

	resp := &commissionpb.GetCommissionsRecordResp{}
	for _, c := range commissions {
		// 获取对应nft信息
		var (
			nftName string
			chainID string
		)
		if nft, ok := nftMap[c.NftID]; ok {
			nftName = nft.GetName()
			chainID = nft.GetChainId()
		}
		resp.Commissions = append(resp.Commissions, &commissionpb.GetCommissionsRecordResp_Commission{
			CommissionAmount: c.CommissionAmount.String(),
			CreatedAt:        c.CreatedAt.UnixMilli(),
			OrderId:          c.OrderID,
			OrderTime:        orderTimeMap[c.OrderID],
			NftName:          nftName,
			ChainId:          chainID,
			InviteePhone:     invitePhoneMap[c.InviteeID],
		})
	}
	resp.Total = total
	return resp, nil
}

func (a *Application) GetCommissionTotal(ctx context.Context) (*commissionpb.GetCommissionTotalResp, error) {
	userID := auth.UserIDFromContext(ctx)
	if userID == "" {
		return nil, ErrUnAuthStatus
	}
	total, err := a.commissionRepo.GetCommissionTotal(ctx, userID)
	if err != nil {
		return nil, err
	}

	rules, err := a.ruleRepo.GetCommissionRules(ctx)
	if err != nil {
		return nil, err
	}

	inviteNums, err := a.invitationClient.GetInviteNumByUserIDs(ctx, &invitationpb.GetInviteNumByUserIDsReq{
		UserIDs: []string{userID},
	})
	if err != nil {
		return nil, err
	}

	if len(inviteNums.InviteNum) == 0 {
		return &commissionpb.GetCommissionTotalResp{
			InviteNum:        "0",
			CommissionRate:   "0",
			CommissionAmount: total.String(),
		}, nil
	}

	inviteCount := inviteNums.InviteNum[0].InviteNum

	var applicableRule *rule.CommissionRule
	for _, r := range rules {
		if r.IsActive() && int64(r.MinValidInvitedUsers()) <= inviteCount {
			if applicableRule == nil || r.MinValidInvitedUsers() > applicableRule.MinValidInvitedUsers() {
				applicableRule = r
			}
		}
	}

	var commissionRate string
	if applicableRule != nil {
		commissionRate = applicableRule.CommissionPercentage().String()
	} else {
		commissionRate = "0"
	}

	return &commissionpb.GetCommissionTotalResp{
		InviteNum:        strconv.FormatInt(inviteCount, 10),
		CommissionRate:   commissionRate,
		CommissionAmount: total.String(),
	}, nil
}

func (a *Application) CreateCommissionRecord(ctx context.Context, req *commissionpb.CreateCommissionRecordRPCReq) (*emptypb.Empty, error) {
	startTime := time.UnixMilli(req.StartTime)
	endTime := time.UnixMilli(req.EndTime)
	prefix := a.commissionService.GetKeyPrefix(startTime, endTime)
	// 判断是否已经存在该记录
	records, err := a.commissionRepo.CheckCommissionsKeyExist(ctx, prefix)
	if err != nil {
		zap.L().Error("Failed to get commissions record by key", zap.Error(err))
		return nil, err
	}
	if records {
		zap.L().Info("commissions already exist", zap.String("key", prefix))
		return nil, status.Error(codes.AlreadyExists, "commissions already exist")
	}
	// payment服务 获取二级订单
	secondaryOrders, err := a.orderClient.GetSecondaryOrdersByTimeRPC(ctx, &orderpb.GetSecondaryOrdersByTimeRPCReq{
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	})
	if err != nil {
		zap.L().Error("GetSecondaryOrdersByTimeRPC failed", zap.Error(err))
		return nil, err
	}
	// nft服务 获取求购订单
	purchaseReqDetails, err := a.purchaseReqClient.GetPurchaseRequestDetailsByTimeRPC(ctx, &purchasereqpb.GetPurchaseRequestDetailsByTimeRPCReq{
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	})
	if err != nil {
		zap.L().Error("GetPurchaseRequestDetailsByTimeRPC failed", zap.Error(err))
		return nil, err
	}
	// 计算手续费
	secondaryDivideFees := a.commissionService.CalcSecondaryOrderDivideFees(secondaryOrders.GetSecondaryOrders())
	purchaseDivideFees := a.commissionService.CalcPurchaseReqDivideFees(purchaseReqDetails.GetPurchaseRequestSupplyDetails())
	allDivideFees := append(secondaryDivideFees, purchaseDivideFees...)

	// 收集所有分佣相关用户ID并去重
	userIDSet := make(map[string]struct{})
	for _, d := range allDivideFees {
		userIDSet[d.UserID] = struct{}{}
	}
	var userIDs []string
	for uid := range userIDSet {
		userIDs = append(userIDs, uid)
	}

	if len(userIDs) == 0 {
		return nil, nil
	}
	// user服务 获取邀请人
	invitersResp, err := a.invitationClient.GetInviterByUserIDsRPC(ctx, &invitationpb.GetInviterByUserIDsRPCReq{
		UserIDs: userIDs, // 获取邀请人
	})
	if err != nil {
		zap.L().Error("GetInviterByUserIDs failed", zap.Error(err), zap.Strings("userIDs", userIDs))
		return nil, err
	}

	// 获取分佣规则
	commissionRules, err := a.ruleRepo.GetCommissionRules(ctx)
	if err != nil {
		zap.L().Error("GetCommissionRules failed", zap.Error(err))
		return nil, err
	}

	// 获取邀请人分佣数
	inviter2Map := make(map[string]string)
	inviter2InviteCountMap := make(map[string]int64)
	for _, inviter := range invitersResp.Inviter {
		inviter2Map[inviter.UserID] = inviter.InviterID
		inviter2InviteCountMap[inviter.InviterID] = inviter.InviteNum
	}

	// 收集所有邀请人ID
	inviterIDSet := make(map[string]struct{})
	for _, inviterID := range inviter2Map {
		inviterIDSet[inviterID] = struct{}{}
	}
	var inviterIDs []string
	for id := range inviterIDSet {
		inviterIDs = append(inviterIDs, id)
	}

	// 计算分佣
	var commissions []*commission.Commission
	for _, divideFee := range allDivideFees {
		inviterID, exists := inviter2Map[divideFee.UserID]
		if !exists {
			zap.L().Debug("No inviter found for user", zap.String("userID", divideFee.UserID))
			continue
		}

		amountDecimal, err := decimal.NewFromString(divideFee.Fee)
		if err != nil {
			zap.L().Error("Failed to convert fee to decimal", zap.String("fee", divideFee.Fee), zap.Error(err))
			continue
		}

		inviteCount := inviter2InviteCountMap[inviterID]

		var applicableRule *rule.CommissionRule
		for _, r := range commissionRules {
			if int64(r.MinValidInvitedUsers()) <= inviteCount && r.IsActive() {
				if applicableRule == nil || r.MinValidInvitedUsers() > applicableRule.MinValidInvitedUsers() {
					applicableRule = r
				}
			}
		}
		//TODO: 邀请人交易金额

		if applicableRule == nil {
			zap.L().Debug("No applicable commission rule found for inviter",
				zap.String("inviterID", inviterID),
				zap.Int64("inviteCount", inviteCount))
			continue
		}

		commissionRate := applicableRule.CommissionPercentage()
		commissionAmount := amountDecimal.Mul(commissionRate)

		commission := commission.NewCommission(
			&dividepb.GetDivideFeeByTimeRPCResp_DivideFee{
				UserID:  divideFee.UserID,
				OrderID: divideFee.OrderID,
				Fee:     divideFee.Fee,
				NftID:   divideFee.NftID,
			},
			inviterID,
			startTime,
			endTime,
			applicableRule.ID(),
			commissionRate,
			commissionAmount,
			divideFee.Type,
			divideFee.ExtID,
		)
		commissions = append(commissions, commission)
	}

	if len(commissions) > 0 {
		err = db.Transaction[*query.Query](ctx, func(ctx context.Context) error {
			// 创建分佣记录
			if err := a.commissionRepo.CreateCommissions(ctx, commissions); err != nil {
				return err
			}
			zap.L().Info("Created commission records", zap.Int("count", len(commissions)))

			// 1. 查出所有本次涉及的邀请人的 summary
			userCommission, err := a.commissionRepo.GetCommissionTotalByUserIDs(ctx, inviterIDs)
			if err != nil {
				return err
			}

			// 2. 构建 userID -> summary 的 map
			summaryMap := make(map[string]*commission.CommissionSummary)
			for _, s := range userCommission {
				summaryMap[s.UserID] = s
			}

			// 3. 统计本次分佣 - 确保对每个邀请人累加所有分佣记录
			// 这样即使一个新用户在同一批次中有多条记录，也能正确累加所有金额
			inviterCommissionMap := make(map[string]decimal.Decimal)
			for _, c := range commissions {
				if _, ok := inviterCommissionMap[c.UserID]; !ok {
					inviterCommissionMap[c.UserID] = decimal.Zero
				}
				inviterCommissionMap[c.UserID] = inviterCommissionMap[c.UserID].Add(c.CommissionAmount)
			}

			// 4. 分两类处理：update和insert
			var updateList []*commission.CommissionSummary
			var insertList []*commission.CommissionSummary
			for _, inviterID := range inviterIDs {
				// 获取该邀请人本次所有分佣记录的累计金额
				current, exists := inviterCommissionMap[inviterID]
				if !exists {
					// 如果该邀请人在本次批次中没有分佣记录，跳过
					continue
				}

				if s, ok := summaryMap[inviterID]; ok {
					// 已有summary记录，累加到历史总额上
					s.TotalCommissionAmount = s.TotalCommissionAmount.Add(current)
					updateList = append(updateList, s)
				} else {
					// 没有summary记录，新建并用本次累计金额初始化
					insertList = append(insertList, &commission.CommissionSummary{
						UserID:                inviterID,
						TotalCommissionAmount: current,
					})
				}
			}

			// 5. 批量 update
			if len(updateList) > 0 {
				if err := a.commissionRepo.UpdateCommissionTotalByUserIDs(ctx, updateList); err != nil {
					return err
				}
			}
			// 6. 批量 insert
			if len(insertList) > 0 {
				if err := a.commissionRepo.CreateCommissionSummaries(ctx, insertList); err != nil {
					return err
				}
			}
			return nil
		})
		if err != nil {
			return nil, err
		}
	}
	return &emptypb.Empty{}, nil
}
