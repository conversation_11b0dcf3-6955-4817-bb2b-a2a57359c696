package infra

import (
	"cnb.cool/cymirror/ces-services/commission-service/internal/domain/commission"
	"cnb.cool/cymirror/ces-services/commission-service/internal/domain/rule"
	"cnb.cool/cymirror/ces-services/commission-service/internal/infra/repository"
	"github.com/google/wire"
)

// InfraProviderSet Infrastructure providers
var InfraProviderSet = wire.NewSet(
	repository.NewCommissionRuleRepository,
	wire.Bind(new(rule.Repo), new(*repository.CommissionRuleRepository)),
	repository.NewCommissionRepository,
	wire.Bind(new(commission.Repo), new(*repository.CommissionRepository)),
)
