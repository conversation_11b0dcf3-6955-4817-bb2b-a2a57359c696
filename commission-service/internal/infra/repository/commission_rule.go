package repository

import (
	"cnb.cool/cymirror/ces-services/commission-service/gen/gen/query"
	"cnb.cool/cymirror/ces-services/commission-service/internal/domain/rule"
	"cnb.cool/cymirror/ces-services/common/db"
	"context"
)

var _ rule.Repo = (*CommissionRuleRepository)(nil)

type CommissionRuleRepository struct {
	db *db.DB[*query.Query]
}

func NewCommissionRuleRepository(db *db.DB[*query.Query]) *CommissionRuleRepository {
	return &CommissionRuleRepository{db: db}
}

func (c CommissionRuleRepository) GetCommissionRules(ctx context.Context) ([]*rule.CommissionRule, error) {
	db := c.db.Get(ctx)
	d, err := db.CommissionRule.WithContext(ctx).
		Find()
	if err != nil {
		return nil, err
	}
	rules := make([]*rule.CommissionRule, 0, len(d))
	for _, item := range d {
		ruleModel := rule.NewCommissionRuleFromModel(item)
		rules = append(rules, ruleModel)
	}
	return rules, nil
}
