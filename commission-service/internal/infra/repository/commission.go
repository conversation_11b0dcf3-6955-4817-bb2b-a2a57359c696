package repository

import (
	"cnb.cool/cymirror/ces-services/common/server"
	"context"
	"errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"
	"time"

	"cnb.cool/cymirror/ces-services/commission-service/gen/gen/model"
	"cnb.cool/cymirror/ces-services/commission-service/gen/gen/query"
	"cnb.cool/cymirror/ces-services/commission-service/internal/domain/commission"
	"cnb.cool/cymirror/ces-services/common/db"
	"github.com/shopspring/decimal"
)

var _ commission.Repo = (*CommissionRepository)(nil)

var ErrCommissionDuplicate = status.Error(codes.AlreadyExists, "commission already exist")

type CommissionRepository struct {
	db *db.DB[*query.Query]
}

func NewCommissionRepository(db *db.DB[*query.Query]) *CommissionRepository {
	return &CommissionRepository{db: db}
}

func (c CommissionRepository) CreateCommissions(ctx context.Context, commissions []*commission.Commission) error {
	db := c.db.Get(ctx)
	models := make([]*model.Commission, 0, len(commissions))
	for _, c := range commissions {
		models = append(models, c.ToModel())
	}

	// Process in batches of 1000
	batchSize := 1000
	for i := 0; i < len(models); i += batchSize {
		end := i + batchSize
		if end > len(models) {
			end = len(models)
		}

		err := db.Commission.WithContext(ctx).
			Create(models[i:end]...)

		if err != nil && errors.Is(err, gorm.ErrDuplicatedKey) {
			zap.L().Warn("commission already exist", zap.Error(err))
			return ErrCommissionDuplicate
		} else if err != nil {
			zap.L().Error("Failed to save commissions", zap.Error(err))
			return err
		}
	}

	return nil
}

func (c CommissionRepository) GetCommissionsRecord(ctx context.Context, userID string, startTime int64, endTime int64, page int32) ([]*commission.Commission, int64, error) {
	db := c.db.Get(ctx)
	start := time.UnixMilli(startTime)
	end := time.UnixMilli(endTime)
	d, count, err := db.Commission.WithContext(ctx).
		Where(db.Commission.UserID.Eq(userID)).
		Where(db.Commission.CreatedAt.Gte(start)).
		Where(db.Commission.CreatedAt.Lte(end)).
		Order(db.Commission.CreatedAt.Desc()).
		FindByPage(int((page-1)*10), 10)
	if err != nil {
		return nil, 0, err
	}
	commissions := make([]*commission.Commission, 0, len(d))
	for _, item := range d {
		commissionModel := commission.NewCommissionFromModel(item)
		commissions = append(commissions, commissionModel)
	}
	return commissions, count, nil
}

func (c CommissionRepository) GetCommissionTotal(ctx context.Context, userID string) (*decimal.Decimal, error) {
	db := c.db.Get(ctx)
	summary, err := db.CommissionSummary.
		WithContext(ctx).
		Where(db.CommissionSummary.UserID.Eq(userID)).
		First()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if summary == nil {
		return &decimal.Zero, nil
	}
	amount := summary.TotalCommissionAmount
	return &amount, nil
}
func (c CommissionRepository) GetCommissionTotalByUserIDs(ctx context.Context, userIDs []string) ([]*commission.CommissionSummary, error) {
	db := c.db.Get(ctx)
	summaries, err := db.CommissionSummary.WithContext(ctx).Where(db.CommissionSummary.UserID.In(userIDs...)).Find()
	if err != nil {
		return nil, err
	}
	var totals []*commission.CommissionSummary
	for _, summary := range summaries {
		totals = append(totals, commission.NewCommissionSummary(summary.ID, summary.UserID, summary.TotalCommissionAmount, summary.CreatedAt, summary.UpdatedAt))
	}
	return totals, nil
}

func (c CommissionRepository) UpdateCommissionTotalByUserIDs(ctx context.Context, totals []*commission.CommissionSummary) error {
	db := c.db.Get(ctx)
	for _, total := range totals {
		_, err := db.CommissionSummary.WithContext(ctx).
			Where(db.CommissionSummary.UserID.Eq(total.UserID)).
			UpdateColumn(db.CommissionSummary.TotalCommissionAmount, total.TotalCommissionAmount)
		if err != nil {
			return err
		}
	}
	return nil
}

func (c CommissionRepository) CreateCommissionSummaries(ctx context.Context, summaries []*commission.CommissionSummary) error {
	db := c.db.Get(ctx)
	models := make([]*model.CommissionSummary, 0, len(summaries))
	for _, s := range summaries {
		models = append(models, s.ToModel())
	}
	batchSize := 1000
	for i := 0; i < len(models); i += batchSize {
		end := i + batchSize
		if end > len(models) {
			end = len(models)
		}
		err := db.CommissionSummary.WithContext(ctx).
			Create(models[i:end]...)
		if err != nil {
			return ErrCommissionDuplicate
		}
	}
	return nil
}
func (c CommissionRepository) CheckCommissionsKeyExist(ctx context.Context, key string) (bool, error) {
	db := c.db.Get(ctx)
	commissions, err := db.Commission.WithContext(ctx).
		Where(db.Commission.IdempotencyKey.Like(key + "%")). //判断是否存在该记录
		First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		zap.L().Error("CheckCommissionsKeyExist failed", zap.Error(err))
		return false, server.InternalStatus
	}
	if commissions == nil {
		return false, nil
	}
	return true, nil
}
