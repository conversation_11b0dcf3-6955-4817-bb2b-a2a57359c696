syntax = "proto3";

package ces.commission;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

option go_package = "/commissionpb";

service CommissionService {
  // 获取分佣记录
  rpc GetCommissionsRecord(GetCommissionsRecordReq) returns (GetCommissionsRecordResp) {
    option (google.api.http) = {
      get: "/v1/commissions/record"
    };
  }
  // 创建分佣记录
  rpc CreateCommissionRecordRPC(CreateCommissionRecordRPCReq) returns (google.protobuf.Empty);
  // 获取分佣信息
  rpc GetCommissionTotal(google.protobuf.Empty) returns (GetCommissionTotalResp) {
    option (google.api.http) = {
      get: "/v1/commissions/total"
    };
  }
}

message CreateCommissionRecordRPCReq {
  // 开始时间
  int64 startTime = 1;
  // 结束时间
  int64 endTime = 2;
}

message GetCommissionsRecordReq {
  // 开始时间
  int64 startTime = 1;
  // 结束时间
  int64 endTime = 2;
  // 页码
  int32 page = 3;
}

message GetCommissionsRecordResp {
  message Commission {
    // 订单金额
    string commissionAmount = 1;
    // 返佣记录创建时间
    int64 createdAt = 2;
    // 订单 id
    string orderId = 3;
    // 订单时间
    int64 orderTime = 4;
    // nft
    string nftName = 5;
    // nft id
    string chainId = 6;
    // 邀请手机号
    string inviteePhone = 7;
  }
  repeated Commission commissions = 1;
  // 总数
  int64 total = 2;
}

message GetCommissionTotalResp {
  // 邀请总数
  string inviteNum = 1;
  // 返佣比例
  string commissionRate = 2;
  // 累计返佣
  string commissionAmount = 3;
}
