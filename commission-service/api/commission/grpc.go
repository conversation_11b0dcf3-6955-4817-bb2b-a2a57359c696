package commission

import (
	"context"

	commissionpb "cnb.cool/cymirror/ces-services/commission-service/gen/proto/ces/commission"
	"cnb.cool/cymirror/ces-services/commission-service/internal/application/commission"
	"cnb.cool/cymirror/ces-services/commission-service/internal/application/cron"
	"google.golang.org/protobuf/types/known/emptypb"
)

var _ commissionpb.CommissionServiceServer = (*CommissionServer)(nil)

type CommissionServer struct {
	application *commission.Application
	cron        *cron.CreateCommissionRecordJob
}

func NewCommissionServer(application *commission.Application, createCommissionJob *cron.CreateCommissionRecordJob) *CommissionServer {
	return &CommissionServer{
		application: application,
		cron:        createCommissionJob,
	}
}

func (c CommissionServer) GetCommissionsRecord(ctx context.Context, req *commissionpb.GetCommissionsRecordReq) (*commissionpb.GetCommissionsRecordResp, error) {
	return c.application.GetCommissionsRecord(ctx, req)
}
func (c CommissionServer) CreateCommissionRecordRPC(ctx context.Context, req *commissionpb.CreateCommissionRecordRPCReq) (*emptypb.Empty, error) {
	return c.application.CreateCommissionRecord(ctx, req)
}
func (c CommissionServer) GetCommissionTotal(ctx context.Context, empty *emptypb.Empty) (*commissionpb.GetCommissionTotalResp, error) {
	return c.application.GetCommissionTotal(ctx)
}
