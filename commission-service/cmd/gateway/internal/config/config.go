package config

import (
	"cnb.cool/cymirror/ces-services/common/conf"
	"cnb.cool/cymirror/ces-services/common/gateway/config"
)

type Config struct {
	config.GatewayConfig `mapstructure:",squash"`
}

func MustLoad(path string) *Config {
	_, c := conf.MustLoad[Config](path)
	return c
}

func GetGatewayConfig(cfg *Config) *config.GatewayConfig {
	if cfg == nil {
		panic("gateway config is nil")
	}
	return &cfg.GatewayConfig
}

func GetRegistryConfig(cfg *Config) *config.RegistryConfig {
	if cfg == nil {
		panic("registry config is nil")
	}
	return &cfg.Registry
}
