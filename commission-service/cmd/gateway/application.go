package main

import (
	commissionpb "cnb.cool/cymirror/ces-services/commission-service/gen/proto/ces/commission"
	"cnb.cool/cymirror/ces-services/common/auth"
	"cnb.cool/cymirror/ces-services/common/gateway"
	authpb "cnb.cool/cymirror/ces-services/user-service/gen/proto/ces/user/auth"
	"context"
	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"google.golang.org/grpc"
)

type Application struct {
	Gateway *gateway.Gateway
}

func NewApplication(
	gw *gateway.Gateway,
	authClient authpb.AuthServiceClient,
) *Application {
	_ = gw.RegisterHandler(func(gwmux *runtime.ServeMux, conn *grpc.ClientConn) error {
		var err error
		err = commissionpb.RegisterCommissionServiceHandler(context.Background(), gwmux, conn)
		if err != nil {
			return err
		}
		return err
	})

	e := gw.Echo
	e.Use(auth.BuildMetadataMiddleware(authClient))
	return &Application{
		Gateway: gw,
	}
}

func (s *Application) Run(ctx context.Context) error {
	return s.Gateway.Run(ctx)
}
