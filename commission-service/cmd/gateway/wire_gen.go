// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	client2 "cnb.cool/cymirror/ces-services/commission-service/cmd/gateway/internal/client"
	"cnb.cool/cymirror/ces-services/commission-service/cmd/gateway/internal/config"
	"cnb.cool/cymirror/ces-services/common/client"
	"cnb.cool/cymirror/ces-services/common/gateway"
	"context"
)

import (
	_ "cnb.cool/cymirror/ces-services/common/resolver"
)

// Injectors from wire.go:

func InitializeApplication(ctx context.Context, configPath2 string) (*Application, func()) {
	configConfig := config.MustLoad(configPath2)
	gatewayConfig := config.GetGatewayConfig(configConfig)
	gatewayGateway, cleanup := gateway.InitializeGateway(ctx, gatewayConfig)
	clientConfig := client.NewConfigFromGatewayConfig(gatewayConfig)
	authServiceClient, cleanup2 := client2.NewAuthServiceClient(clientConfig)
	application := NewApplication(gatewayGateway, authServiceClient)
	return application, func() {
		cleanup2()
		cleanup()
	}
}
