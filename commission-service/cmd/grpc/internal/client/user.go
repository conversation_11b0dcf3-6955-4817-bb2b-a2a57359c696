package client

import (
	"cnb.cool/cymirror/ces-services/common/client"
	invitationpb "cnb.cool/cymirror/ces-services/user-service/gen/proto/ces/user/invitation"
	profilepb "cnb.cool/cymirror/ces-services/user-service/gen/proto/ces/user/profile"
)

func NewInvitationServiceClient(registryCfg *client.Config) (invitationpb.InvitationServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "user-service", invitationpb.NewInvitationServiceClient)
}
func NewProfileServiceClient(registryCfg *client.Config) (profilepb.ProfileServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "user-service", profilepb.NewProfileServiceClient)
}
