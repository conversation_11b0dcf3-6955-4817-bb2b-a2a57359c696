package client

import (
	"cnb.cool/cymirror/ces-services/common/client"
	nftpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/nft"
	purchasereqpb "cnb.cool/cymirror/ces-services/nft-service/gen/proto/ces/nft/purchasereq"
)

func NewPurchaseServiceClient(registryCfg *client.Config) (purchasereqpb.PurchaseReqServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "nft-service", purchasereqpb.NewPurchaseReqServiceClient)
}
func NewNFTServiceClient(registryCfg *client.Config) (nftpb.NFTServiceClient, func()) {
	return client.NewGrpcClient(registryCfg, "nft-service", nftpb.NewNFTServiceClient)
}
