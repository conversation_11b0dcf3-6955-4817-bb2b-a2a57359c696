package config

import (
	"cnb.cool/cymirror/ces-services/common/conf"
	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/common/server/config"
)

type Config struct {
	config.GrpcServerConfig `mapstructure:",squash"`
	Database                db.DatabaseConfig `mapstructure:"database"`
	Redis                   db.RedisConfig    `mapstructure:"redis"`
}

func MustLoad(path string) *Config {
	_, c := conf.MustLoad[Config](path)
	return c
}

func GetGrpcServerConfig(cfg *Config) *config.GrpcServerConfig {
	if cfg == nil {
		panic("grpc server config is nil")
	}
	return &cfg.GrpcServerConfig
}

func GetDBConfig(cfg *Config) *db.DatabaseConfig {
	if cfg == nil {
		panic("database config is nil")
	}
	return &cfg.Database
}

func GetRedisConfig(cfg *Config) *db.RedisConfig {
	if cfg == nil {
		panic("redis config is nil")
	}
	return &cfg.Redis
}
