package internal

import (
	appCron "cnb.cool/cymirror/ces-services/commission-service/internal/application/cron"
	"github.com/robfig/cron/v3"
)

func NewCron(
	createCommissionRecordJob *appCron.CreateCommissionRecordJob,
) *cron.Cron {
	c := cron.New(cron.WithChain(
		cron.Recover(cron.DefaultLogger),
	))
	//_, err := c.AddFunc("5 */3 * * *", createCommissionRecordJob.Run)
	//_, err := c.AddFunc("5,25,45 * * * *", createCommissionRecordJob.Run)
	//_, err := c.AddFunc("1-59/2 * * * *", createCommissionRecordJob.Run)
	_, err := c.AddFunc("5,15,25,35,45,55 * * * *", createCommissionRecordJob.Run)
	if err != nil {
		panic(err)
	}
	return c
}
