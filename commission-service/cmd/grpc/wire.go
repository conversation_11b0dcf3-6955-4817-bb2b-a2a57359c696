//go:build wireinject

package main

import (
	"cnb.cool/cymirror/ces-services/commission-service/api"
	"cnb.cool/cymirror/ces-services/commission-service/cmd/grpc/internal"
	"cnb.cool/cymirror/ces-services/commission-service/cmd/grpc/internal/client"
	"cnb.cool/cymirror/ces-services/commission-service/cmd/grpc/internal/config"
	"cnb.cool/cymirror/ces-services/commission-service/internal/application"
	"cnb.cool/cymirror/ces-services/commission-service/internal/domain"
	"cnb.cool/cymirror/ces-services/commission-service/internal/infra"
	"cnb.cool/cymirror/ces-services/common/server"

	"context"
	"github.com/google/wire"
)

func InitializeApplication(ctx context.Context, configPath string) (*Application, func()) {
	panic(wire.Build(
		config.MustLoad,
		config.GetGrpcServerConfig,

		config.ConfigProviderSet,
		application.AppProviderSet,
		api.HandlerProviderSet,
		infra.InfraProviderSet,
		internal.InternalProviderSet,
		domain.DomainServiceProviderSet,

		client.GrpcClientProviderSet,

		server.InitializeServer,

		NewApplication,
	))
}
