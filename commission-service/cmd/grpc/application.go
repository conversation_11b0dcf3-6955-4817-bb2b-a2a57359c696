package main

import (
	"context"

	"cnb.cool/cymirror/ces-services/commission-service/api/commission"
	"cnb.cool/cymirror/ces-services/commission-service/cmd/grpc/internal/config"
	commissionpb "cnb.cool/cymirror/ces-services/commission-service/gen/proto/ces/commission"
	"cnb.cool/cymirror/ces-services/common/server"
	"github.com/robfig/cron/v3"
	"google.golang.org/grpc"
)

type Application struct {
	Server *server.Server
	cron   *cron.Cron
}

func NewApplication(
	cfg *config.Config,
	s *server.Server,
	commissionServer *commission.CommissionServer,
	cron *cron.Cron,
) *Application {

	s.RegisterServer(func(s *grpc.Server) {
		commissionpb.RegisterCommissionServiceServer(s, commissionServer)
	})

	return &Application{
		Server: s,
		cron:   cron,
	}
}

func (s *Application) Run(ctx context.Context) error {
	if s.cron != nil {
		s.cron.Start()
		defer s.cron.Stop()
	}
	return s.Server.Run(ctx)
}
