// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	commission3 "cnb.cool/cymirror/ces-services/commission-service/api/commission"
	"cnb.cool/cymirror/ces-services/commission-service/cmd/grpc/internal"
	client2 "cnb.cool/cymirror/ces-services/commission-service/cmd/grpc/internal/client"
	"cnb.cool/cymirror/ces-services/commission-service/cmd/grpc/internal/config"
	commission2 "cnb.cool/cymirror/ces-services/commission-service/internal/application/commission"
	"cnb.cool/cymirror/ces-services/commission-service/internal/application/cron"
	"cnb.cool/cymirror/ces-services/commission-service/internal/domain/commission"
	"cnb.cool/cymirror/ces-services/commission-service/internal/infra/repository"
	"cnb.cool/cymirror/ces-services/common/client"
	"cnb.cool/cymirror/ces-services/common/db"
	"cnb.cool/cymirror/ces-services/common/server"
	"context"
	"github.com/bsm/redislock"
)

// Injectors from wire.go:

func InitializeApplication(ctx context.Context, configPath2 string) (*Application, func()) {
	configConfig := config.MustLoad(configPath2)
	grpcServerConfig := config.GetGrpcServerConfig(configConfig)
	serverServer, cleanup := server.InitializeServer(ctx, grpcServerConfig)
	databaseConfig := config.GetDBConfig(configConfig)
	dbDB := internal.NewDB(databaseConfig)
	commissionRepository := repository.NewCommissionRepository(dbDB)
	commissionRuleRepository := repository.NewCommissionRuleRepository(dbDB)
	clientConfig := client.NewConfigFromGRPCConfig(grpcServerConfig)
	orderServiceClient, cleanup2 := client2.NewOrderServiceClient(clientConfig)
	nftServiceClient, cleanup3 := client2.NewNFTServiceClient(clientConfig)
	profileServiceClient, cleanup4 := client2.NewProfileServiceClient(clientConfig)
	purchaseReqServiceClient, cleanup5 := client2.NewPurchaseServiceClient(clientConfig)
	invitationServiceClient, cleanup6 := client2.NewInvitationServiceClient(clientConfig)
	service := commission.NewService()
	application := commission2.NewApplication(commissionRepository, commissionRuleRepository, orderServiceClient, nftServiceClient, profileServiceClient, purchaseReqServiceClient, invitationServiceClient, service)
	redisConfig := config.GetRedisConfig(configConfig)
	universalClient := db.NewRedis(redisConfig)
	redislockClient := redislock.New(universalClient)
	createCommissionRecordJob := cron.NewCreateCommissionRecordJob(ctx, application, redislockClient)
	commissionServer := commission3.NewCommissionServer(application, createCommissionRecordJob)
	cronCron := internal.NewCron(createCommissionRecordJob)
	mainApplication := NewApplication(configConfig, serverServer, commissionServer, cronCron)
	return mainApplication, func() {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}
}
