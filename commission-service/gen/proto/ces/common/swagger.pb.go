// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: ces/common/swagger.proto

package commonpb

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_ces_common_swagger_proto protoreflect.FileDescriptor

const file_ces_common_swagger_proto_rawDesc = "" +
	"\n" +
	"\x18ces/common/swagger.proto\x12\x0eces.sms.common\x1a\x1cgoogle/api/annotations.proto\x1a.protoc-gen-openapiv2/options/annotations.protoB\xb5\x01\x92A\xa6\x01\x12\x15\n" +
	"\fmanage服务2\x051.0.0*\x02\x01\x02Z#\n" +
	"!\n" +
	"\n" +
	"ApiKeyAuth\x12\x13\b\x02\x1a\rAuthorization \x02b\x10\n" +
	"\x0e\n" +
	"\n" +
	"ApiKeyAuth\x12\x00rR\n" +
	"J注意：200 响应中不包含code msg 等统一返回值，仅代表data\x12\x04noneZ\t/commonpbb\x06proto3"

var file_ces_common_swagger_proto_goTypes = []any{}
var file_ces_common_swagger_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_ces_common_swagger_proto_init() }
func file_ces_common_swagger_proto_init() {
	if File_ces_common_swagger_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_ces_common_swagger_proto_rawDesc), len(file_ces_common_swagger_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ces_common_swagger_proto_goTypes,
		DependencyIndexes: file_ces_common_swagger_proto_depIdxs,
	}.Build()
	File_ces_common_swagger_proto = out.File
	file_ces_common_swagger_proto_goTypes = nil
	file_ces_common_swagger_proto_depIdxs = nil
}
