// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: ces/commission/commission.proto

package commissionpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CommissionService_GetCommissionsRecord_FullMethodName      = "/ces.commission.CommissionService/GetCommissionsRecord"
	CommissionService_CreateCommissionRecordRPC_FullMethodName = "/ces.commission.CommissionService/CreateCommissionRecordRPC"
	CommissionService_GetCommissionTotal_FullMethodName        = "/ces.commission.CommissionService/GetCommissionTotal"
)

// CommissionServiceClient is the client API for CommissionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CommissionServiceClient interface {
	// 获取分佣记录
	GetCommissionsRecord(ctx context.Context, in *GetCommissionsRecordReq, opts ...grpc.CallOption) (*GetCommissionsRecordResp, error)
	// 创建分佣记录
	CreateCommissionRecordRPC(ctx context.Context, in *CreateCommissionRecordRPCReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取分佣信息
	GetCommissionTotal(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetCommissionTotalResp, error)
}

type commissionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCommissionServiceClient(cc grpc.ClientConnInterface) CommissionServiceClient {
	return &commissionServiceClient{cc}
}

func (c *commissionServiceClient) GetCommissionsRecord(ctx context.Context, in *GetCommissionsRecordReq, opts ...grpc.CallOption) (*GetCommissionsRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCommissionsRecordResp)
	err := c.cc.Invoke(ctx, CommissionService_GetCommissionsRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commissionServiceClient) CreateCommissionRecordRPC(ctx context.Context, in *CreateCommissionRecordRPCReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CommissionService_CreateCommissionRecordRPC_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commissionServiceClient) GetCommissionTotal(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetCommissionTotalResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCommissionTotalResp)
	err := c.cc.Invoke(ctx, CommissionService_GetCommissionTotal_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CommissionServiceServer is the server API for CommissionService service.
// All implementations should embed UnimplementedCommissionServiceServer
// for forward compatibility.
type CommissionServiceServer interface {
	// 获取分佣记录
	GetCommissionsRecord(context.Context, *GetCommissionsRecordReq) (*GetCommissionsRecordResp, error)
	// 创建分佣记录
	CreateCommissionRecordRPC(context.Context, *CreateCommissionRecordRPCReq) (*emptypb.Empty, error)
	// 获取分佣信息
	GetCommissionTotal(context.Context, *emptypb.Empty) (*GetCommissionTotalResp, error)
}

// UnimplementedCommissionServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCommissionServiceServer struct{}

func (UnimplementedCommissionServiceServer) GetCommissionsRecord(context.Context, *GetCommissionsRecordReq) (*GetCommissionsRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommissionsRecord not implemented")
}
func (UnimplementedCommissionServiceServer) CreateCommissionRecordRPC(context.Context, *CreateCommissionRecordRPCReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCommissionRecordRPC not implemented")
}
func (UnimplementedCommissionServiceServer) GetCommissionTotal(context.Context, *emptypb.Empty) (*GetCommissionTotalResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommissionTotal not implemented")
}
func (UnimplementedCommissionServiceServer) testEmbeddedByValue() {}

// UnsafeCommissionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CommissionServiceServer will
// result in compilation errors.
type UnsafeCommissionServiceServer interface {
	mustEmbedUnimplementedCommissionServiceServer()
}

func RegisterCommissionServiceServer(s grpc.ServiceRegistrar, srv CommissionServiceServer) {
	// If the following call pancis, it indicates UnimplementedCommissionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CommissionService_ServiceDesc, srv)
}

func _CommissionService_GetCommissionsRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommissionsRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommissionServiceServer).GetCommissionsRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommissionService_GetCommissionsRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommissionServiceServer).GetCommissionsRecord(ctx, req.(*GetCommissionsRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommissionService_CreateCommissionRecordRPC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCommissionRecordRPCReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommissionServiceServer).CreateCommissionRecordRPC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommissionService_CreateCommissionRecordRPC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommissionServiceServer).CreateCommissionRecordRPC(ctx, req.(*CreateCommissionRecordRPCReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommissionService_GetCommissionTotal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommissionServiceServer).GetCommissionTotal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommissionService_GetCommissionTotal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommissionServiceServer).GetCommissionTotal(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// CommissionService_ServiceDesc is the grpc.ServiceDesc for CommissionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CommissionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ces.commission.CommissionService",
	HandlerType: (*CommissionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCommissionsRecord",
			Handler:    _CommissionService_GetCommissionsRecord_Handler,
		},
		{
			MethodName: "CreateCommissionRecordRPC",
			Handler:    _CommissionService_CreateCommissionRecordRPC_Handler,
		},
		{
			MethodName: "GetCommissionTotal",
			Handler:    _CommissionService_GetCommissionTotal_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ces/commission/commission.proto",
}
