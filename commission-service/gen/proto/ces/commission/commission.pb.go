// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: ces/commission/commission.proto

package commissionpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateCommissionRecordRPCReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 开始时间
	StartTime int64 `protobuf:"varint,1,opt,name=startTime,proto3" json:"startTime,omitempty"`
	// 结束时间
	EndTime       int64 `protobuf:"varint,2,opt,name=endTime,proto3" json:"endTime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCommissionRecordRPCReq) Reset() {
	*x = CreateCommissionRecordRPCReq{}
	mi := &file_ces_commission_commission_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCommissionRecordRPCReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCommissionRecordRPCReq) ProtoMessage() {}

func (x *CreateCommissionRecordRPCReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_commission_commission_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCommissionRecordRPCReq.ProtoReflect.Descriptor instead.
func (*CreateCommissionRecordRPCReq) Descriptor() ([]byte, []int) {
	return file_ces_commission_commission_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCommissionRecordRPCReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *CreateCommissionRecordRPCReq) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

type GetCommissionsRecordReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 开始时间
	StartTime int64 `protobuf:"varint,1,opt,name=startTime,proto3" json:"startTime,omitempty"`
	// 结束时间
	EndTime int64 `protobuf:"varint,2,opt,name=endTime,proto3" json:"endTime,omitempty"`
	// 页码
	Page          int32 `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCommissionsRecordReq) Reset() {
	*x = GetCommissionsRecordReq{}
	mi := &file_ces_commission_commission_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCommissionsRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommissionsRecordReq) ProtoMessage() {}

func (x *GetCommissionsRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_ces_commission_commission_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommissionsRecordReq.ProtoReflect.Descriptor instead.
func (*GetCommissionsRecordReq) Descriptor() ([]byte, []int) {
	return file_ces_commission_commission_proto_rawDescGZIP(), []int{1}
}

func (x *GetCommissionsRecordReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GetCommissionsRecordReq) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *GetCommissionsRecordReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

type GetCommissionsRecordResp struct {
	state       protoimpl.MessageState                 `protogen:"open.v1"`
	Commissions []*GetCommissionsRecordResp_Commission `protobuf:"bytes,1,rep,name=commissions,proto3" json:"commissions,omitempty"`
	// 总数
	Total         int64 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCommissionsRecordResp) Reset() {
	*x = GetCommissionsRecordResp{}
	mi := &file_ces_commission_commission_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCommissionsRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommissionsRecordResp) ProtoMessage() {}

func (x *GetCommissionsRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_ces_commission_commission_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommissionsRecordResp.ProtoReflect.Descriptor instead.
func (*GetCommissionsRecordResp) Descriptor() ([]byte, []int) {
	return file_ces_commission_commission_proto_rawDescGZIP(), []int{2}
}

func (x *GetCommissionsRecordResp) GetCommissions() []*GetCommissionsRecordResp_Commission {
	if x != nil {
		return x.Commissions
	}
	return nil
}

func (x *GetCommissionsRecordResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type GetCommissionTotalResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 邀请总数
	InviteNum string `protobuf:"bytes,1,opt,name=inviteNum,proto3" json:"inviteNum,omitempty"`
	// 返佣比例
	CommissionRate string `protobuf:"bytes,2,opt,name=commissionRate,proto3" json:"commissionRate,omitempty"`
	// 累计返佣
	CommissionAmount string `protobuf:"bytes,3,opt,name=commissionAmount,proto3" json:"commissionAmount,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetCommissionTotalResp) Reset() {
	*x = GetCommissionTotalResp{}
	mi := &file_ces_commission_commission_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCommissionTotalResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommissionTotalResp) ProtoMessage() {}

func (x *GetCommissionTotalResp) ProtoReflect() protoreflect.Message {
	mi := &file_ces_commission_commission_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommissionTotalResp.ProtoReflect.Descriptor instead.
func (*GetCommissionTotalResp) Descriptor() ([]byte, []int) {
	return file_ces_commission_commission_proto_rawDescGZIP(), []int{3}
}

func (x *GetCommissionTotalResp) GetInviteNum() string {
	if x != nil {
		return x.InviteNum
	}
	return ""
}

func (x *GetCommissionTotalResp) GetCommissionRate() string {
	if x != nil {
		return x.CommissionRate
	}
	return ""
}

func (x *GetCommissionTotalResp) GetCommissionAmount() string {
	if x != nil {
		return x.CommissionAmount
	}
	return ""
}

type GetCommissionsRecordResp_Commission struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 订单金额
	CommissionAmount string `protobuf:"bytes,1,opt,name=commissionAmount,proto3" json:"commissionAmount,omitempty"`
	// 返佣记录创建时间
	CreatedAt int64 `protobuf:"varint,2,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	// 订单 id
	OrderId string `protobuf:"bytes,3,opt,name=orderId,proto3" json:"orderId,omitempty"`
	// 订单时间
	OrderTime int64 `protobuf:"varint,4,opt,name=orderTime,proto3" json:"orderTime,omitempty"`
	// nft
	NftName string `protobuf:"bytes,5,opt,name=nftName,proto3" json:"nftName,omitempty"`
	// nft id
	ChainId string `protobuf:"bytes,6,opt,name=chainId,proto3" json:"chainId,omitempty"`
	// 邀请手机号
	InviteePhone  string `protobuf:"bytes,7,opt,name=inviteePhone,proto3" json:"inviteePhone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCommissionsRecordResp_Commission) Reset() {
	*x = GetCommissionsRecordResp_Commission{}
	mi := &file_ces_commission_commission_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCommissionsRecordResp_Commission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommissionsRecordResp_Commission) ProtoMessage() {}

func (x *GetCommissionsRecordResp_Commission) ProtoReflect() protoreflect.Message {
	mi := &file_ces_commission_commission_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommissionsRecordResp_Commission.ProtoReflect.Descriptor instead.
func (*GetCommissionsRecordResp_Commission) Descriptor() ([]byte, []int) {
	return file_ces_commission_commission_proto_rawDescGZIP(), []int{2, 0}
}

func (x *GetCommissionsRecordResp_Commission) GetCommissionAmount() string {
	if x != nil {
		return x.CommissionAmount
	}
	return ""
}

func (x *GetCommissionsRecordResp_Commission) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *GetCommissionsRecordResp_Commission) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *GetCommissionsRecordResp_Commission) GetOrderTime() int64 {
	if x != nil {
		return x.OrderTime
	}
	return 0
}

func (x *GetCommissionsRecordResp_Commission) GetNftName() string {
	if x != nil {
		return x.NftName
	}
	return ""
}

func (x *GetCommissionsRecordResp_Commission) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *GetCommissionsRecordResp_Commission) GetInviteePhone() string {
	if x != nil {
		return x.InviteePhone
	}
	return ""
}

var File_ces_commission_commission_proto protoreflect.FileDescriptor

const file_ces_commission_commission_proto_rawDesc = "" +
	"\n" +
	"\x1fces/commission/commission.proto\x12\x0eces.commission\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a.protoc-gen-openapiv2/options/annotations.proto\"V\n" +
	"\x1cCreateCommissionRecordRPCReq\x12\x1c\n" +
	"\tstartTime\x18\x01 \x01(\x03R\tstartTime\x12\x18\n" +
	"\aendTime\x18\x02 \x01(\x03R\aendTime\"e\n" +
	"\x17GetCommissionsRecordReq\x12\x1c\n" +
	"\tstartTime\x18\x01 \x01(\x03R\tstartTime\x12\x18\n" +
	"\aendTime\x18\x02 \x01(\x03R\aendTime\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\"\xf0\x02\n" +
	"\x18GetCommissionsRecordResp\x12U\n" +
	"\vcommissions\x18\x01 \x03(\v23.ces.commission.GetCommissionsRecordResp.CommissionR\vcommissions\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x03R\x05total\x1a\xe6\x01\n" +
	"\n" +
	"Commission\x12*\n" +
	"\x10commissionAmount\x18\x01 \x01(\tR\x10commissionAmount\x12\x1c\n" +
	"\tcreatedAt\x18\x02 \x01(\x03R\tcreatedAt\x12\x18\n" +
	"\aorderId\x18\x03 \x01(\tR\aorderId\x12\x1c\n" +
	"\torderTime\x18\x04 \x01(\x03R\torderTime\x12\x18\n" +
	"\anftName\x18\x05 \x01(\tR\anftName\x12\x18\n" +
	"\achainId\x18\x06 \x01(\tR\achainId\x12\"\n" +
	"\finviteePhone\x18\a \x01(\tR\finviteePhone\"\x8a\x01\n" +
	"\x16GetCommissionTotalResp\x12\x1c\n" +
	"\tinviteNum\x18\x01 \x01(\tR\tinviteNum\x12&\n" +
	"\x0ecommissionRate\x18\x02 \x01(\tR\x0ecommissionRate\x12*\n" +
	"\x10commissionAmount\x18\x03 \x01(\tR\x10commissionAmount2\xf7\x02\n" +
	"\x11CommissionService\x12\x89\x01\n" +
	"\x14GetCommissionsRecord\x12'.ces.commission.GetCommissionsRecordReq\x1a(.ces.commission.GetCommissionsRecordResp\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/v1/commissions/record\x12a\n" +
	"\x19CreateCommissionRecordRPC\x12,.ces.commission.CreateCommissionRecordRPCReq\x1a\x16.google.protobuf.Empty\x12s\n" +
	"\x12GetCommissionTotal\x12\x16.google.protobuf.Empty\x1a&.ces.commission.GetCommissionTotalResp\"\x1d\x82\xd3\xe4\x93\x02\x17\x12\x15/v1/commissions/totalB\x0fZ\r/commissionpbb\x06proto3"

var (
	file_ces_commission_commission_proto_rawDescOnce sync.Once
	file_ces_commission_commission_proto_rawDescData []byte
)

func file_ces_commission_commission_proto_rawDescGZIP() []byte {
	file_ces_commission_commission_proto_rawDescOnce.Do(func() {
		file_ces_commission_commission_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_ces_commission_commission_proto_rawDesc), len(file_ces_commission_commission_proto_rawDesc)))
	})
	return file_ces_commission_commission_proto_rawDescData
}

var file_ces_commission_commission_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_ces_commission_commission_proto_goTypes = []any{
	(*CreateCommissionRecordRPCReq)(nil),        // 0: ces.commission.CreateCommissionRecordRPCReq
	(*GetCommissionsRecordReq)(nil),             // 1: ces.commission.GetCommissionsRecordReq
	(*GetCommissionsRecordResp)(nil),            // 2: ces.commission.GetCommissionsRecordResp
	(*GetCommissionTotalResp)(nil),              // 3: ces.commission.GetCommissionTotalResp
	(*GetCommissionsRecordResp_Commission)(nil), // 4: ces.commission.GetCommissionsRecordResp.Commission
	(*emptypb.Empty)(nil),                       // 5: google.protobuf.Empty
}
var file_ces_commission_commission_proto_depIdxs = []int32{
	4, // 0: ces.commission.GetCommissionsRecordResp.commissions:type_name -> ces.commission.GetCommissionsRecordResp.Commission
	1, // 1: ces.commission.CommissionService.GetCommissionsRecord:input_type -> ces.commission.GetCommissionsRecordReq
	0, // 2: ces.commission.CommissionService.CreateCommissionRecordRPC:input_type -> ces.commission.CreateCommissionRecordRPCReq
	5, // 3: ces.commission.CommissionService.GetCommissionTotal:input_type -> google.protobuf.Empty
	2, // 4: ces.commission.CommissionService.GetCommissionsRecord:output_type -> ces.commission.GetCommissionsRecordResp
	5, // 5: ces.commission.CommissionService.CreateCommissionRecordRPC:output_type -> google.protobuf.Empty
	3, // 6: ces.commission.CommissionService.GetCommissionTotal:output_type -> ces.commission.GetCommissionTotalResp
	4, // [4:7] is the sub-list for method output_type
	1, // [1:4] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_ces_commission_commission_proto_init() }
func file_ces_commission_commission_proto_init() {
	if File_ces_commission_commission_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_ces_commission_commission_proto_rawDesc), len(file_ces_commission_commission_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_ces_commission_commission_proto_goTypes,
		DependencyIndexes: file_ces_commission_commission_proto_depIdxs,
		MessageInfos:      file_ces_commission_commission_proto_msgTypes,
	}.Build()
	File_ces_commission_commission_proto = out.File
	file_ces_commission_commission_proto_goTypes = nil
	file_ces_commission_commission_proto_depIdxs = nil
}
