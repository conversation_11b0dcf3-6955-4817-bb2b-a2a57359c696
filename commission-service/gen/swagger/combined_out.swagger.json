{"swagger": "2.0", "info": {"title": "manage服务", "version": "1.0.0"}, "tags": [{"name": "CommissionService"}], "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/commissions/record": {"get": {"summary": "获取分佣记录", "operationId": "CommissionService_GetCommissionsRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/commissionGetCommissionsRecordResp"}}}, "parameters": [{"name": "startTime", "description": "开始时间", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "endTime", "description": "结束时间", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "page", "description": "页码", "in": "query", "required": false, "type": "integer", "format": "int32"}], "tags": ["CommissionService"]}}, "/v1/commissions/total": {"get": {"summary": "获取分佣信息", "operationId": "CommissionService_GetCommissionTotal", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/commissionGetCommissionTotalResp"}}}, "tags": ["CommissionService"]}}}, "definitions": {"GetCommissionsRecordRespCommission": {"type": "object", "properties": {"commissionAmount": {"type": "string", "title": "订单金额"}, "createdAt": {"type": "string", "format": "int64", "title": "返佣记录创建时间"}, "orderId": {"type": "string", "title": "订单 id"}, "orderTime": {"type": "string", "format": "int64", "title": "订单时间"}, "nftName": {"type": "string", "title": "nft"}, "chainId": {"type": "string", "title": "nft id"}, "inviteePhone": {"type": "string", "title": "邀请手机号"}}}, "commissionGetCommissionTotalResp": {"type": "object", "properties": {"inviteNum": {"type": "string", "title": "邀请总数"}, "commissionRate": {"type": "string", "title": "返佣比例"}, "commissionAmount": {"type": "string", "title": "累计返佣"}}}, "commissionGetCommissionsRecordResp": {"type": "object", "properties": {"commissions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/GetCommissionsRecordRespCommission"}}, "total": {"type": "string", "format": "int64", "title": "总数"}}}}, "securityDefinitions": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "security": [{"ApiKeyAuth": []}], "externalDocs": {"description": "注意：200 响应中不包含code msg 等统一返回值，仅代表data", "url": "none"}}