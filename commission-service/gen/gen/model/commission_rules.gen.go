// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"github.com/shopspring/decimal"
)

const TableNameCommissionRule = "commission_rules"

// CommissionRule mapped from table <commission_rules>
type CommissionRule struct {
	ID                                 string          `gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid();comment:规则ID (主键)" json:"id"`                                                                                             // 规则ID (主键)
	RuleName                           string          `gorm:"column:rule_name;type:text;not null;comment:规则名称" json:"rule_name"`                                                                                                                // 规则名称
	Description                        *string         `gorm:"column:description;type:text;comment:规则描述信息" json:"description"`                                                                                                                   // 规则描述信息
	MinValidInvitedUsers               int32           `gorm:"column:min_valid_invited_users;type:integer;not null;comment:阶梯条件：邀请人名下最低有效邀请人数 (大于等于此值)" json:"min_valid_invited_users"`                                                          // 阶梯条件：邀请人名下最低有效邀请人数 (大于等于此值)
	MinInviterCumulativeInviteeTxValue decimal.Decimal `gorm:"column:min_inviter_cumulative_invitee_tx_value;type:numeric(18,2);not null;default:0.00;comment:阶梯条件：邀请人名下所有有效被邀请人累计交易总额 (大于等于此值)" json:"min_inviter_cumulative_invitee_tx_value"` // 阶梯条件：邀请人名下所有有效被邀请人累计交易总额 (大于等于此值)
	CommissionPercentage               decimal.Decimal `gorm:"column:commission_percentage;type:numeric(5,4);not null;comment:此规则下的返佣百分比 (例如 0.10 代表10%)" json:"commission_percentage"`                                                          // 此规则下的返佣百分比 (例如 0.10 代表10%)
	IsActive                           bool            `gorm:"column:is_active;type:boolean;not null;default:true;comment:规则是否激活 (TRUE:激活, FALSE:未激活)" json:"is_active"`                                                                         // 规则是否激活 (TRUE:激活, FALSE:未激活)
	CreatedAt                          time.Time       `gorm:"column:created_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoCreateTime;comment:规则创建时间" json:"created_at"`                                            // 规则创建时间
	UpdatedAt                          time.Time       `gorm:"column:updated_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoUpdateTime;comment:规则最后更新时间" json:"updated_at"`                                          // 规则最后更新时间
}

// TableName CommissionRule's table name
func (*CommissionRule) TableName() string {
	return TableNameCommissionRule
}
