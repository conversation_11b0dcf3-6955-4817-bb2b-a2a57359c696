// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"github.com/shopspring/decimal"
)

const TableNameLaunchpad = "launchpad"

// Launchpad mapped from table <launchpad>
type Launchpad struct {
	ID                         string          `gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid();comment:蓝持派加成记录ID (主键)" json:"id"`                                               // 蓝持派加成记录ID (主键)
	InviterID                  string          `gorm:"column:inviter_id;type:uuid;not null;comment:获得加成的邀请人用户ID (逻辑外键)" json:"inviter_id"`                                                      // 获得加成的邀请人用户ID (逻辑外键)
	InviteeID                  string          `gorm:"column:invitee_id;type:uuid;not null;comment:触发加成的被邀请人用户ID (逻辑外键)" json:"invitee_id"`                                                     // 触发加成的被邀请人用户ID (逻辑外键)
	LaunchpadRuleID            *string         `gorm:"column:launchpad_rule_id;type:uuid;comment:计算此加成时应用的蓝持派规则ID (逻辑外键，指向 launchpad_rules.id)" json:"launchpad_rule_id"`                       // 计算此加成时应用的蓝持派规则ID (逻辑外键，指向 launchpad_rules.id)
	InviteeHoldingValueAtBoost decimal.Decimal `gorm:"column:invitee_holding_value_at_boost;type:numeric(18,2);not null;comment:计算加成时，被邀请人的持仓市值" json:"invitee_holding_value_at_boost"`         // 计算加成时，被邀请人的持仓市值
	InviterHoldingValueAtBoost decimal.Decimal `gorm:"column:inviter_holding_value_at_boost;type:numeric(18,2);not null;comment:计算加成时，邀请人的持仓市值" json:"inviter_holding_value_at_boost"`          // 计算加成时，邀请人的持仓市值
	BoostRateApplied           decimal.Decimal `gorm:"column:boost_rate_applied;type:numeric(5,4);not null;comment:实际应用的加成比例 (例如 0.10 代表10%)" json:"boost_rate_applied"`                        // 实际应用的加成比例 (例如 0.10 代表10%)
	ActualBoostApplied         decimal.Decimal `gorm:"column:actual_boost_applied;type:numeric(18,2);not null;comment:最终计算并应用的加成金额 (已考虑所有前提条件和封顶限制)" json:"actual_boost_applied"`               // 最终计算并应用的加成金额 (已考虑所有前提条件和封顶限制)
	CreatedAt                  time.Time       `gorm:"column:created_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoCreateTime;comment:记录创建时间" json:"created_at"`   // 记录创建时间
	UpdatedAt                  time.Time       `gorm:"column:updated_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoUpdateTime;comment:记录最后更新时间" json:"updated_at"` // 记录最后更新时间
}

// TableName Launchpad's table name
func (*Launchpad) TableName() string {
	return TableNameLaunchpad
}
