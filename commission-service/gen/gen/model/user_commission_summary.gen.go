// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"github.com/shopspring/decimal"
)

const TableNameUserCommissionSummary = "user_commission_summary"

// UserCommissionSummary mapped from table <user_commission_summary>
type UserCommissionSummary struct {
	ID                    string          `gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid();comment:汇总记录的唯一ID (主键)" json:"id"`                                                              // 汇总记录的唯一ID (主键)
	UserID                string          `gorm:"column:user_id;type:uuid;not null;comment:用户ID (唯一键，逻辑外键指向用户表)" json:"user_id"`                                                                          // 用户ID (唯一键，逻辑外键指向用户表)
	TotalCommissionAmount decimal.Decimal `gorm:"column:total_commission_amount;type:numeric;not null;comment:该用户累计获得的总佣金金额" json:"total_commission_amount"`                                              // 该用户累计获得的总佣金金额
	CreatedAt             time.Time       `gorm:"column:created_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoCreateTime;comment:记录创建时间 (当用户首次产生佣金汇总记录时)" json:"created_at"` // 记录创建时间 (当用户首次产生佣金汇总记录时)
	UpdatedAt             time.Time       `gorm:"column:updated_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoUpdateTime;comment:记录最后更新时间 (当总佣金更新时)" json:"updated_at"`      // 记录最后更新时间 (当总佣金更新时)
}

// TableName UserCommissionSummary's table name
func (*UserCommissionSummary) TableName() string {
	return TableNameUserCommissionSummary
}
