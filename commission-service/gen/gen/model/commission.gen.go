// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"github.com/shopspring/decimal"
)

const TableNameCommission = "commission"

// Commission mapped from table <commission>
type Commission struct {
	ID                    string          `gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid();comment:返佣流水记录ID (主键)" json:"id"`                                                // 返佣流水记录ID (主键)
	OrderID               string          `gorm:"column:order_id;type:uuid;not null;comment:产生此返佣的源订单ID (逻辑外键)" json:"order_id"`                                                           // 产生此返佣的源订单ID (逻辑外键)
	UserID                string          `gorm:"column:user_id;type:uuid;not null;comment:获得返佣的邀请人用户ID (逻辑外键)" json:"user_id"`                                                            // 获得返佣的邀请人用户ID (逻辑外键)
	InviteeID             string          `gorm:"column:invitee_id;type:uuid;not null;comment:产生交易的被邀请人用户ID (逻辑外键)" json:"invitee_id"`                                                     // 产生交易的被邀请人用户ID (逻辑外键)
	CommissionRuleID      string          `gorm:"column:commission_rule_id;type:uuid;not null;comment:计算此返佣时应用的返佣规则ID (逻辑外键，指向 commission_rules.id)" json:"commission_rule_id"`            // 计算此返佣时应用的返佣规则ID (逻辑外键，指向 commission_rules.id)
	CommissionRateApplied decimal.Decimal `gorm:"column:commission_rate_applied;type:numeric(5,4);not null;comment:实际应用的返佣比例 (例如 0.10 代表10%)" json:"commission_rate_applied"`              // 实际应用的返佣比例 (例如 0.10 代表10%)
	CommissionAmount      decimal.Decimal `gorm:"column:commission_amount;type:numeric;not null;comment:计算得出的返佣金额" json:"commission_amount"`                                               // 计算得出的返佣金额
	CreatedAt             time.Time       `gorm:"column:created_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoCreateTime;comment:记录创建时间" json:"created_at"`   // 记录创建时间
	UpdatedAt             time.Time       `gorm:"column:updated_at;type:timestamp(6) with time zone;not null;default:CURRENT_TIMESTAMP;autoUpdateTime;comment:记录最后更新时间" json:"updated_at"` // 记录最后更新时间
	IdempotencyKey        string          `gorm:"column:idempotency_key;type:text;not null;comment:幂等key 唯一索引" json:"idempotency_key"`                                                     // 幂等key 唯一索引
	NftID                 string          `gorm:"column:nft_id;type:uuid;not null;comment:nft id" json:"nft_id"`                                                                           // nft id
	TradeType             int16           `gorm:"column:trade_type;type:smallint;not null;comment:交易订单类型" json:"trade_type"`                                                               // 交易订单类型
	TradeTypeExtID        string          `gorm:"column:trade_type_ext_id;type:uuid;not null;comment:交易订单额外id，二级订单类型对应二级市场表，求购对应供应记录详情" json:"trade_type_ext_id"`                          // 交易订单额外id，二级订单类型对应二级市场表，求购对应供应记录详情
}

// TableName Commission's table name
func (*Commission) TableName() string {
	return TableNameCommission
}
