// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/commission-service/gen/gen/model"
)

func newLaunchpad(db *gorm.DB, opts ...gen.DOOption) launchpad {
	_launchpad := launchpad{}

	_launchpad.launchpadDo.UseDB(db, opts...)
	_launchpad.launchpadDo.UseModel(&model.Launchpad{})

	tableName := _launchpad.launchpadDo.TableName()
	_launchpad.ALL = field.NewAsterisk(tableName)
	_launchpad.ID = field.NewString(tableName, "id")
	_launchpad.InviterID = field.NewString(tableName, "inviter_id")
	_launchpad.InviteeID = field.NewString(tableName, "invitee_id")
	_launchpad.LaunchpadRuleID = field.NewString(tableName, "launchpad_rule_id")
	_launchpad.InviteeHoldingValueAtBoost = field.NewField(tableName, "invitee_holding_value_at_boost")
	_launchpad.InviterHoldingValueAtBoost = field.NewField(tableName, "inviter_holding_value_at_boost")
	_launchpad.BoostRateApplied = field.NewField(tableName, "boost_rate_applied")
	_launchpad.ActualBoostApplied = field.NewField(tableName, "actual_boost_applied")
	_launchpad.CreatedAt = field.NewTime(tableName, "created_at")
	_launchpad.UpdatedAt = field.NewTime(tableName, "updated_at")

	_launchpad.fillFieldMap()

	return _launchpad
}

type launchpad struct {
	launchpadDo launchpadDo

	ALL                        field.Asterisk
	ID                         field.String // 蓝持派加成记录ID (主键)
	InviterID                  field.String // 获得加成的邀请人用户ID (逻辑外键)
	InviteeID                  field.String // 触发加成的被邀请人用户ID (逻辑外键)
	LaunchpadRuleID            field.String // 计算此加成时应用的蓝持派规则ID (逻辑外键，指向 launchpad_rules.id)
	InviteeHoldingValueAtBoost field.Field  // 计算加成时，被邀请人的持仓市值
	InviterHoldingValueAtBoost field.Field  // 计算加成时，邀请人的持仓市值
	BoostRateApplied           field.Field  // 实际应用的加成比例 (例如 0.10 代表10%)
	ActualBoostApplied         field.Field  // 最终计算并应用的加成金额 (已考虑所有前提条件和封顶限制)
	CreatedAt                  field.Time   // 记录创建时间
	UpdatedAt                  field.Time   // 记录最后更新时间

	fieldMap map[string]field.Expr
}

func (l launchpad) Table(newTableName string) *launchpad {
	l.launchpadDo.UseTable(newTableName)
	return l.updateTableName(newTableName)
}

func (l launchpad) As(alias string) *launchpad {
	l.launchpadDo.DO = *(l.launchpadDo.As(alias).(*gen.DO))
	return l.updateTableName(alias)
}

func (l *launchpad) updateTableName(table string) *launchpad {
	l.ALL = field.NewAsterisk(table)
	l.ID = field.NewString(table, "id")
	l.InviterID = field.NewString(table, "inviter_id")
	l.InviteeID = field.NewString(table, "invitee_id")
	l.LaunchpadRuleID = field.NewString(table, "launchpad_rule_id")
	l.InviteeHoldingValueAtBoost = field.NewField(table, "invitee_holding_value_at_boost")
	l.InviterHoldingValueAtBoost = field.NewField(table, "inviter_holding_value_at_boost")
	l.BoostRateApplied = field.NewField(table, "boost_rate_applied")
	l.ActualBoostApplied = field.NewField(table, "actual_boost_applied")
	l.CreatedAt = field.NewTime(table, "created_at")
	l.UpdatedAt = field.NewTime(table, "updated_at")

	l.fillFieldMap()

	return l
}

func (l *launchpad) WithContext(ctx context.Context) ILaunchpadDo {
	return l.launchpadDo.WithContext(ctx)
}

func (l launchpad) TableName() string { return l.launchpadDo.TableName() }

func (l launchpad) Alias() string { return l.launchpadDo.Alias() }

func (l launchpad) Columns(cols ...field.Expr) gen.Columns { return l.launchpadDo.Columns(cols...) }

func (l *launchpad) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := l.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (l *launchpad) fillFieldMap() {
	l.fieldMap = make(map[string]field.Expr, 10)
	l.fieldMap["id"] = l.ID
	l.fieldMap["inviter_id"] = l.InviterID
	l.fieldMap["invitee_id"] = l.InviteeID
	l.fieldMap["launchpad_rule_id"] = l.LaunchpadRuleID
	l.fieldMap["invitee_holding_value_at_boost"] = l.InviteeHoldingValueAtBoost
	l.fieldMap["inviter_holding_value_at_boost"] = l.InviterHoldingValueAtBoost
	l.fieldMap["boost_rate_applied"] = l.BoostRateApplied
	l.fieldMap["actual_boost_applied"] = l.ActualBoostApplied
	l.fieldMap["created_at"] = l.CreatedAt
	l.fieldMap["updated_at"] = l.UpdatedAt
}

func (l launchpad) clone(db *gorm.DB) launchpad {
	l.launchpadDo.ReplaceConnPool(db.Statement.ConnPool)
	return l
}

func (l launchpad) replaceDB(db *gorm.DB) launchpad {
	l.launchpadDo.ReplaceDB(db)
	return l
}

type launchpadDo struct{ gen.DO }

type ILaunchpadDo interface {
	gen.SubQuery
	Debug() ILaunchpadDo
	WithContext(ctx context.Context) ILaunchpadDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ILaunchpadDo
	WriteDB() ILaunchpadDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ILaunchpadDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ILaunchpadDo
	Not(conds ...gen.Condition) ILaunchpadDo
	Or(conds ...gen.Condition) ILaunchpadDo
	Select(conds ...field.Expr) ILaunchpadDo
	Where(conds ...gen.Condition) ILaunchpadDo
	Order(conds ...field.Expr) ILaunchpadDo
	Distinct(cols ...field.Expr) ILaunchpadDo
	Omit(cols ...field.Expr) ILaunchpadDo
	Join(table schema.Tabler, on ...field.Expr) ILaunchpadDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ILaunchpadDo
	RightJoin(table schema.Tabler, on ...field.Expr) ILaunchpadDo
	Group(cols ...field.Expr) ILaunchpadDo
	Having(conds ...gen.Condition) ILaunchpadDo
	Limit(limit int) ILaunchpadDo
	Offset(offset int) ILaunchpadDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ILaunchpadDo
	Unscoped() ILaunchpadDo
	Create(values ...*model.Launchpad) error
	CreateInBatches(values []*model.Launchpad, batchSize int) error
	Save(values ...*model.Launchpad) error
	First() (*model.Launchpad, error)
	Take() (*model.Launchpad, error)
	Last() (*model.Launchpad, error)
	Find() ([]*model.Launchpad, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Launchpad, err error)
	FindInBatches(result *[]*model.Launchpad, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Launchpad) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ILaunchpadDo
	Assign(attrs ...field.AssignExpr) ILaunchpadDo
	Joins(fields ...field.RelationField) ILaunchpadDo
	Preload(fields ...field.RelationField) ILaunchpadDo
	FirstOrInit() (*model.Launchpad, error)
	FirstOrCreate() (*model.Launchpad, error)
	FindByPage(offset int, limit int) (result []*model.Launchpad, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ILaunchpadDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (l launchpadDo) Debug() ILaunchpadDo {
	return l.withDO(l.DO.Debug())
}

func (l launchpadDo) WithContext(ctx context.Context) ILaunchpadDo {
	return l.withDO(l.DO.WithContext(ctx))
}

func (l launchpadDo) ReadDB() ILaunchpadDo {
	return l.Clauses(dbresolver.Read)
}

func (l launchpadDo) WriteDB() ILaunchpadDo {
	return l.Clauses(dbresolver.Write)
}

func (l launchpadDo) Session(config *gorm.Session) ILaunchpadDo {
	return l.withDO(l.DO.Session(config))
}

func (l launchpadDo) Clauses(conds ...clause.Expression) ILaunchpadDo {
	return l.withDO(l.DO.Clauses(conds...))
}

func (l launchpadDo) Returning(value interface{}, columns ...string) ILaunchpadDo {
	return l.withDO(l.DO.Returning(value, columns...))
}

func (l launchpadDo) Not(conds ...gen.Condition) ILaunchpadDo {
	return l.withDO(l.DO.Not(conds...))
}

func (l launchpadDo) Or(conds ...gen.Condition) ILaunchpadDo {
	return l.withDO(l.DO.Or(conds...))
}

func (l launchpadDo) Select(conds ...field.Expr) ILaunchpadDo {
	return l.withDO(l.DO.Select(conds...))
}

func (l launchpadDo) Where(conds ...gen.Condition) ILaunchpadDo {
	return l.withDO(l.DO.Where(conds...))
}

func (l launchpadDo) Order(conds ...field.Expr) ILaunchpadDo {
	return l.withDO(l.DO.Order(conds...))
}

func (l launchpadDo) Distinct(cols ...field.Expr) ILaunchpadDo {
	return l.withDO(l.DO.Distinct(cols...))
}

func (l launchpadDo) Omit(cols ...field.Expr) ILaunchpadDo {
	return l.withDO(l.DO.Omit(cols...))
}

func (l launchpadDo) Join(table schema.Tabler, on ...field.Expr) ILaunchpadDo {
	return l.withDO(l.DO.Join(table, on...))
}

func (l launchpadDo) LeftJoin(table schema.Tabler, on ...field.Expr) ILaunchpadDo {
	return l.withDO(l.DO.LeftJoin(table, on...))
}

func (l launchpadDo) RightJoin(table schema.Tabler, on ...field.Expr) ILaunchpadDo {
	return l.withDO(l.DO.RightJoin(table, on...))
}

func (l launchpadDo) Group(cols ...field.Expr) ILaunchpadDo {
	return l.withDO(l.DO.Group(cols...))
}

func (l launchpadDo) Having(conds ...gen.Condition) ILaunchpadDo {
	return l.withDO(l.DO.Having(conds...))
}

func (l launchpadDo) Limit(limit int) ILaunchpadDo {
	return l.withDO(l.DO.Limit(limit))
}

func (l launchpadDo) Offset(offset int) ILaunchpadDo {
	return l.withDO(l.DO.Offset(offset))
}

func (l launchpadDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ILaunchpadDo {
	return l.withDO(l.DO.Scopes(funcs...))
}

func (l launchpadDo) Unscoped() ILaunchpadDo {
	return l.withDO(l.DO.Unscoped())
}

func (l launchpadDo) Create(values ...*model.Launchpad) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Create(values)
}

func (l launchpadDo) CreateInBatches(values []*model.Launchpad, batchSize int) error {
	return l.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (l launchpadDo) Save(values ...*model.Launchpad) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Save(values)
}

func (l launchpadDo) First() (*model.Launchpad, error) {
	if result, err := l.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Launchpad), nil
	}
}

func (l launchpadDo) Take() (*model.Launchpad, error) {
	if result, err := l.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Launchpad), nil
	}
}

func (l launchpadDo) Last() (*model.Launchpad, error) {
	if result, err := l.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Launchpad), nil
	}
}

func (l launchpadDo) Find() ([]*model.Launchpad, error) {
	result, err := l.DO.Find()
	return result.([]*model.Launchpad), err
}

func (l launchpadDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Launchpad, err error) {
	buf := make([]*model.Launchpad, 0, batchSize)
	err = l.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (l launchpadDo) FindInBatches(result *[]*model.Launchpad, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return l.DO.FindInBatches(result, batchSize, fc)
}

func (l launchpadDo) Attrs(attrs ...field.AssignExpr) ILaunchpadDo {
	return l.withDO(l.DO.Attrs(attrs...))
}

func (l launchpadDo) Assign(attrs ...field.AssignExpr) ILaunchpadDo {
	return l.withDO(l.DO.Assign(attrs...))
}

func (l launchpadDo) Joins(fields ...field.RelationField) ILaunchpadDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Joins(_f))
	}
	return &l
}

func (l launchpadDo) Preload(fields ...field.RelationField) ILaunchpadDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Preload(_f))
	}
	return &l
}

func (l launchpadDo) FirstOrInit() (*model.Launchpad, error) {
	if result, err := l.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Launchpad), nil
	}
}

func (l launchpadDo) FirstOrCreate() (*model.Launchpad, error) {
	if result, err := l.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Launchpad), nil
	}
}

func (l launchpadDo) FindByPage(offset int, limit int) (result []*model.Launchpad, count int64, err error) {
	result, err = l.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = l.Offset(-1).Limit(-1).Count()
	return
}

func (l launchpadDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = l.Count()
	if err != nil {
		return
	}

	err = l.Offset(offset).Limit(limit).Scan(result)
	return
}

func (l launchpadDo) Scan(result interface{}) (err error) {
	return l.DO.Scan(result)
}

func (l launchpadDo) Delete(models ...*model.Launchpad) (result gen.ResultInfo, err error) {
	return l.DO.Delete(models)
}

func (l *launchpadDo) withDO(do gen.Dao) *launchpadDo {
	l.DO = *do.(*gen.DO)
	return l
}
