// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/commission-service/gen/gen/model"
)

func newCommissionRule(db *gorm.DB, opts ...gen.DOOption) commissionRule {
	_commissionRule := commissionRule{}

	_commissionRule.commissionRuleDo.UseDB(db, opts...)
	_commissionRule.commissionRuleDo.UseModel(&model.CommissionRule{})

	tableName := _commissionRule.commissionRuleDo.TableName()
	_commissionRule.ALL = field.NewAsterisk(tableName)
	_commissionRule.ID = field.NewString(tableName, "id")
	_commissionRule.RuleName = field.NewString(tableName, "rule_name")
	_commissionRule.Description = field.NewString(tableName, "description")
	_commissionRule.MinValidInvitedUsers = field.NewInt32(tableName, "min_valid_invited_users")
	_commissionRule.MinInviterCumulativeInviteeTxValue = field.NewField(tableName, "min_inviter_cumulative_invitee_tx_value")
	_commissionRule.CommissionPercentage = field.NewField(tableName, "commission_percentage")
	_commissionRule.IsActive = field.NewBool(tableName, "is_active")
	_commissionRule.CreatedAt = field.NewTime(tableName, "created_at")
	_commissionRule.UpdatedAt = field.NewTime(tableName, "updated_at")

	_commissionRule.fillFieldMap()

	return _commissionRule
}

type commissionRule struct {
	commissionRuleDo commissionRuleDo

	ALL                                field.Asterisk
	ID                                 field.String // 规则ID (主键)
	RuleName                           field.String // 规则名称
	Description                        field.String // 规则描述信息
	MinValidInvitedUsers               field.Int32  // 阶梯条件：邀请人名下最低有效邀请人数 (大于等于此值)
	MinInviterCumulativeInviteeTxValue field.Field  // 阶梯条件：邀请人名下所有有效被邀请人累计交易总额 (大于等于此值)
	CommissionPercentage               field.Field  // 此规则下的返佣百分比 (例如 0.10 代表10%)
	IsActive                           field.Bool   // 规则是否激活 (TRUE:激活, FALSE:未激活)
	CreatedAt                          field.Time   // 规则创建时间
	UpdatedAt                          field.Time   // 规则最后更新时间

	fieldMap map[string]field.Expr
}

func (c commissionRule) Table(newTableName string) *commissionRule {
	c.commissionRuleDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c commissionRule) As(alias string) *commissionRule {
	c.commissionRuleDo.DO = *(c.commissionRuleDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *commissionRule) updateTableName(table string) *commissionRule {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewString(table, "id")
	c.RuleName = field.NewString(table, "rule_name")
	c.Description = field.NewString(table, "description")
	c.MinValidInvitedUsers = field.NewInt32(table, "min_valid_invited_users")
	c.MinInviterCumulativeInviteeTxValue = field.NewField(table, "min_inviter_cumulative_invitee_tx_value")
	c.CommissionPercentage = field.NewField(table, "commission_percentage")
	c.IsActive = field.NewBool(table, "is_active")
	c.CreatedAt = field.NewTime(table, "created_at")
	c.UpdatedAt = field.NewTime(table, "updated_at")

	c.fillFieldMap()

	return c
}

func (c *commissionRule) WithContext(ctx context.Context) ICommissionRuleDo {
	return c.commissionRuleDo.WithContext(ctx)
}

func (c commissionRule) TableName() string { return c.commissionRuleDo.TableName() }

func (c commissionRule) Alias() string { return c.commissionRuleDo.Alias() }

func (c commissionRule) Columns(cols ...field.Expr) gen.Columns {
	return c.commissionRuleDo.Columns(cols...)
}

func (c *commissionRule) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *commissionRule) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 9)
	c.fieldMap["id"] = c.ID
	c.fieldMap["rule_name"] = c.RuleName
	c.fieldMap["description"] = c.Description
	c.fieldMap["min_valid_invited_users"] = c.MinValidInvitedUsers
	c.fieldMap["min_inviter_cumulative_invitee_tx_value"] = c.MinInviterCumulativeInviteeTxValue
	c.fieldMap["commission_percentage"] = c.CommissionPercentage
	c.fieldMap["is_active"] = c.IsActive
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
}

func (c commissionRule) clone(db *gorm.DB) commissionRule {
	c.commissionRuleDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c commissionRule) replaceDB(db *gorm.DB) commissionRule {
	c.commissionRuleDo.ReplaceDB(db)
	return c
}

type commissionRuleDo struct{ gen.DO }

type ICommissionRuleDo interface {
	gen.SubQuery
	Debug() ICommissionRuleDo
	WithContext(ctx context.Context) ICommissionRuleDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ICommissionRuleDo
	WriteDB() ICommissionRuleDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ICommissionRuleDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ICommissionRuleDo
	Not(conds ...gen.Condition) ICommissionRuleDo
	Or(conds ...gen.Condition) ICommissionRuleDo
	Select(conds ...field.Expr) ICommissionRuleDo
	Where(conds ...gen.Condition) ICommissionRuleDo
	Order(conds ...field.Expr) ICommissionRuleDo
	Distinct(cols ...field.Expr) ICommissionRuleDo
	Omit(cols ...field.Expr) ICommissionRuleDo
	Join(table schema.Tabler, on ...field.Expr) ICommissionRuleDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ICommissionRuleDo
	RightJoin(table schema.Tabler, on ...field.Expr) ICommissionRuleDo
	Group(cols ...field.Expr) ICommissionRuleDo
	Having(conds ...gen.Condition) ICommissionRuleDo
	Limit(limit int) ICommissionRuleDo
	Offset(offset int) ICommissionRuleDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ICommissionRuleDo
	Unscoped() ICommissionRuleDo
	Create(values ...*model.CommissionRule) error
	CreateInBatches(values []*model.CommissionRule, batchSize int) error
	Save(values ...*model.CommissionRule) error
	First() (*model.CommissionRule, error)
	Take() (*model.CommissionRule, error)
	Last() (*model.CommissionRule, error)
	Find() ([]*model.CommissionRule, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CommissionRule, err error)
	FindInBatches(result *[]*model.CommissionRule, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.CommissionRule) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ICommissionRuleDo
	Assign(attrs ...field.AssignExpr) ICommissionRuleDo
	Joins(fields ...field.RelationField) ICommissionRuleDo
	Preload(fields ...field.RelationField) ICommissionRuleDo
	FirstOrInit() (*model.CommissionRule, error)
	FirstOrCreate() (*model.CommissionRule, error)
	FindByPage(offset int, limit int) (result []*model.CommissionRule, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ICommissionRuleDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c commissionRuleDo) Debug() ICommissionRuleDo {
	return c.withDO(c.DO.Debug())
}

func (c commissionRuleDo) WithContext(ctx context.Context) ICommissionRuleDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c commissionRuleDo) ReadDB() ICommissionRuleDo {
	return c.Clauses(dbresolver.Read)
}

func (c commissionRuleDo) WriteDB() ICommissionRuleDo {
	return c.Clauses(dbresolver.Write)
}

func (c commissionRuleDo) Session(config *gorm.Session) ICommissionRuleDo {
	return c.withDO(c.DO.Session(config))
}

func (c commissionRuleDo) Clauses(conds ...clause.Expression) ICommissionRuleDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c commissionRuleDo) Returning(value interface{}, columns ...string) ICommissionRuleDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c commissionRuleDo) Not(conds ...gen.Condition) ICommissionRuleDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c commissionRuleDo) Or(conds ...gen.Condition) ICommissionRuleDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c commissionRuleDo) Select(conds ...field.Expr) ICommissionRuleDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c commissionRuleDo) Where(conds ...gen.Condition) ICommissionRuleDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c commissionRuleDo) Order(conds ...field.Expr) ICommissionRuleDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c commissionRuleDo) Distinct(cols ...field.Expr) ICommissionRuleDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c commissionRuleDo) Omit(cols ...field.Expr) ICommissionRuleDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c commissionRuleDo) Join(table schema.Tabler, on ...field.Expr) ICommissionRuleDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c commissionRuleDo) LeftJoin(table schema.Tabler, on ...field.Expr) ICommissionRuleDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c commissionRuleDo) RightJoin(table schema.Tabler, on ...field.Expr) ICommissionRuleDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c commissionRuleDo) Group(cols ...field.Expr) ICommissionRuleDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c commissionRuleDo) Having(conds ...gen.Condition) ICommissionRuleDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c commissionRuleDo) Limit(limit int) ICommissionRuleDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c commissionRuleDo) Offset(offset int) ICommissionRuleDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c commissionRuleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ICommissionRuleDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c commissionRuleDo) Unscoped() ICommissionRuleDo {
	return c.withDO(c.DO.Unscoped())
}

func (c commissionRuleDo) Create(values ...*model.CommissionRule) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c commissionRuleDo) CreateInBatches(values []*model.CommissionRule, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c commissionRuleDo) Save(values ...*model.CommissionRule) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c commissionRuleDo) First() (*model.CommissionRule, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionRule), nil
	}
}

func (c commissionRuleDo) Take() (*model.CommissionRule, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionRule), nil
	}
}

func (c commissionRuleDo) Last() (*model.CommissionRule, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionRule), nil
	}
}

func (c commissionRuleDo) Find() ([]*model.CommissionRule, error) {
	result, err := c.DO.Find()
	return result.([]*model.CommissionRule), err
}

func (c commissionRuleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CommissionRule, err error) {
	buf := make([]*model.CommissionRule, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c commissionRuleDo) FindInBatches(result *[]*model.CommissionRule, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c commissionRuleDo) Attrs(attrs ...field.AssignExpr) ICommissionRuleDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c commissionRuleDo) Assign(attrs ...field.AssignExpr) ICommissionRuleDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c commissionRuleDo) Joins(fields ...field.RelationField) ICommissionRuleDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c commissionRuleDo) Preload(fields ...field.RelationField) ICommissionRuleDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c commissionRuleDo) FirstOrInit() (*model.CommissionRule, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionRule), nil
	}
}

func (c commissionRuleDo) FirstOrCreate() (*model.CommissionRule, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionRule), nil
	}
}

func (c commissionRuleDo) FindByPage(offset int, limit int) (result []*model.CommissionRule, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c commissionRuleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c commissionRuleDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c commissionRuleDo) Delete(models ...*model.CommissionRule) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *commissionRuleDo) withDO(do gen.Dao) *commissionRuleDo {
	c.DO = *do.(*gen.DO)
	return c
}
