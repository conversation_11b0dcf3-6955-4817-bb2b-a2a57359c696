// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/commission-service/gen/gen/model"
)

func newCommissionSummary(db *gorm.DB, opts ...gen.DOOption) commissionSummary {
	_commissionSummary := commissionSummary{}

	_commissionSummary.commissionSummaryDo.UseDB(db, opts...)
	_commissionSummary.commissionSummaryDo.UseModel(&model.CommissionSummary{})

	tableName := _commissionSummary.commissionSummaryDo.TableName()
	_commissionSummary.ALL = field.NewAsterisk(tableName)
	_commissionSummary.ID = field.NewString(tableName, "id")
	_commissionSummary.UserID = field.NewString(tableName, "user_id")
	_commissionSummary.TotalCommissionAmount = field.NewField(tableName, "total_commission_amount")
	_commissionSummary.CreatedAt = field.NewTime(tableName, "created_at")
	_commissionSummary.UpdatedAt = field.NewTime(tableName, "updated_at")

	_commissionSummary.fillFieldMap()

	return _commissionSummary
}

type commissionSummary struct {
	commissionSummaryDo commissionSummaryDo

	ALL                   field.Asterisk
	ID                    field.String // 汇总记录的唯一ID (主键)
	UserID                field.String // 用户ID (唯一键，逻辑外键指向用户表)
	TotalCommissionAmount field.Field  // 该用户累计获得的总佣金金额
	CreatedAt             field.Time   // 记录创建时间 (当用户首次产生佣金汇总记录时)
	UpdatedAt             field.Time   // 记录最后更新时间 (当总佣金更新时)

	fieldMap map[string]field.Expr
}

func (c commissionSummary) Table(newTableName string) *commissionSummary {
	c.commissionSummaryDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c commissionSummary) As(alias string) *commissionSummary {
	c.commissionSummaryDo.DO = *(c.commissionSummaryDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *commissionSummary) updateTableName(table string) *commissionSummary {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewString(table, "id")
	c.UserID = field.NewString(table, "user_id")
	c.TotalCommissionAmount = field.NewField(table, "total_commission_amount")
	c.CreatedAt = field.NewTime(table, "created_at")
	c.UpdatedAt = field.NewTime(table, "updated_at")

	c.fillFieldMap()

	return c
}

func (c *commissionSummary) WithContext(ctx context.Context) ICommissionSummaryDo {
	return c.commissionSummaryDo.WithContext(ctx)
}

func (c commissionSummary) TableName() string { return c.commissionSummaryDo.TableName() }

func (c commissionSummary) Alias() string { return c.commissionSummaryDo.Alias() }

func (c commissionSummary) Columns(cols ...field.Expr) gen.Columns {
	return c.commissionSummaryDo.Columns(cols...)
}

func (c *commissionSummary) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *commissionSummary) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 5)
	c.fieldMap["id"] = c.ID
	c.fieldMap["user_id"] = c.UserID
	c.fieldMap["total_commission_amount"] = c.TotalCommissionAmount
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
}

func (c commissionSummary) clone(db *gorm.DB) commissionSummary {
	c.commissionSummaryDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c commissionSummary) replaceDB(db *gorm.DB) commissionSummary {
	c.commissionSummaryDo.ReplaceDB(db)
	return c
}

type commissionSummaryDo struct{ gen.DO }

type ICommissionSummaryDo interface {
	gen.SubQuery
	Debug() ICommissionSummaryDo
	WithContext(ctx context.Context) ICommissionSummaryDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ICommissionSummaryDo
	WriteDB() ICommissionSummaryDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ICommissionSummaryDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ICommissionSummaryDo
	Not(conds ...gen.Condition) ICommissionSummaryDo
	Or(conds ...gen.Condition) ICommissionSummaryDo
	Select(conds ...field.Expr) ICommissionSummaryDo
	Where(conds ...gen.Condition) ICommissionSummaryDo
	Order(conds ...field.Expr) ICommissionSummaryDo
	Distinct(cols ...field.Expr) ICommissionSummaryDo
	Omit(cols ...field.Expr) ICommissionSummaryDo
	Join(table schema.Tabler, on ...field.Expr) ICommissionSummaryDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ICommissionSummaryDo
	RightJoin(table schema.Tabler, on ...field.Expr) ICommissionSummaryDo
	Group(cols ...field.Expr) ICommissionSummaryDo
	Having(conds ...gen.Condition) ICommissionSummaryDo
	Limit(limit int) ICommissionSummaryDo
	Offset(offset int) ICommissionSummaryDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ICommissionSummaryDo
	Unscoped() ICommissionSummaryDo
	Create(values ...*model.CommissionSummary) error
	CreateInBatches(values []*model.CommissionSummary, batchSize int) error
	Save(values ...*model.CommissionSummary) error
	First() (*model.CommissionSummary, error)
	Take() (*model.CommissionSummary, error)
	Last() (*model.CommissionSummary, error)
	Find() ([]*model.CommissionSummary, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CommissionSummary, err error)
	FindInBatches(result *[]*model.CommissionSummary, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.CommissionSummary) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ICommissionSummaryDo
	Assign(attrs ...field.AssignExpr) ICommissionSummaryDo
	Joins(fields ...field.RelationField) ICommissionSummaryDo
	Preload(fields ...field.RelationField) ICommissionSummaryDo
	FirstOrInit() (*model.CommissionSummary, error)
	FirstOrCreate() (*model.CommissionSummary, error)
	FindByPage(offset int, limit int) (result []*model.CommissionSummary, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ICommissionSummaryDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c commissionSummaryDo) Debug() ICommissionSummaryDo {
	return c.withDO(c.DO.Debug())
}

func (c commissionSummaryDo) WithContext(ctx context.Context) ICommissionSummaryDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c commissionSummaryDo) ReadDB() ICommissionSummaryDo {
	return c.Clauses(dbresolver.Read)
}

func (c commissionSummaryDo) WriteDB() ICommissionSummaryDo {
	return c.Clauses(dbresolver.Write)
}

func (c commissionSummaryDo) Session(config *gorm.Session) ICommissionSummaryDo {
	return c.withDO(c.DO.Session(config))
}

func (c commissionSummaryDo) Clauses(conds ...clause.Expression) ICommissionSummaryDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c commissionSummaryDo) Returning(value interface{}, columns ...string) ICommissionSummaryDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c commissionSummaryDo) Not(conds ...gen.Condition) ICommissionSummaryDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c commissionSummaryDo) Or(conds ...gen.Condition) ICommissionSummaryDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c commissionSummaryDo) Select(conds ...field.Expr) ICommissionSummaryDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c commissionSummaryDo) Where(conds ...gen.Condition) ICommissionSummaryDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c commissionSummaryDo) Order(conds ...field.Expr) ICommissionSummaryDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c commissionSummaryDo) Distinct(cols ...field.Expr) ICommissionSummaryDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c commissionSummaryDo) Omit(cols ...field.Expr) ICommissionSummaryDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c commissionSummaryDo) Join(table schema.Tabler, on ...field.Expr) ICommissionSummaryDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c commissionSummaryDo) LeftJoin(table schema.Tabler, on ...field.Expr) ICommissionSummaryDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c commissionSummaryDo) RightJoin(table schema.Tabler, on ...field.Expr) ICommissionSummaryDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c commissionSummaryDo) Group(cols ...field.Expr) ICommissionSummaryDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c commissionSummaryDo) Having(conds ...gen.Condition) ICommissionSummaryDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c commissionSummaryDo) Limit(limit int) ICommissionSummaryDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c commissionSummaryDo) Offset(offset int) ICommissionSummaryDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c commissionSummaryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ICommissionSummaryDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c commissionSummaryDo) Unscoped() ICommissionSummaryDo {
	return c.withDO(c.DO.Unscoped())
}

func (c commissionSummaryDo) Create(values ...*model.CommissionSummary) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c commissionSummaryDo) CreateInBatches(values []*model.CommissionSummary, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c commissionSummaryDo) Save(values ...*model.CommissionSummary) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c commissionSummaryDo) First() (*model.CommissionSummary, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionSummary), nil
	}
}

func (c commissionSummaryDo) Take() (*model.CommissionSummary, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionSummary), nil
	}
}

func (c commissionSummaryDo) Last() (*model.CommissionSummary, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionSummary), nil
	}
}

func (c commissionSummaryDo) Find() ([]*model.CommissionSummary, error) {
	result, err := c.DO.Find()
	return result.([]*model.CommissionSummary), err
}

func (c commissionSummaryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CommissionSummary, err error) {
	buf := make([]*model.CommissionSummary, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c commissionSummaryDo) FindInBatches(result *[]*model.CommissionSummary, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c commissionSummaryDo) Attrs(attrs ...field.AssignExpr) ICommissionSummaryDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c commissionSummaryDo) Assign(attrs ...field.AssignExpr) ICommissionSummaryDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c commissionSummaryDo) Joins(fields ...field.RelationField) ICommissionSummaryDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c commissionSummaryDo) Preload(fields ...field.RelationField) ICommissionSummaryDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c commissionSummaryDo) FirstOrInit() (*model.CommissionSummary, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionSummary), nil
	}
}

func (c commissionSummaryDo) FirstOrCreate() (*model.CommissionSummary, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionSummary), nil
	}
}

func (c commissionSummaryDo) FindByPage(offset int, limit int) (result []*model.CommissionSummary, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c commissionSummaryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c commissionSummaryDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c commissionSummaryDo) Delete(models ...*model.CommissionSummary) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *commissionSummaryDo) withDO(do gen.Dao) *commissionSummaryDo {
	c.DO = *do.(*gen.DO)
	return c
}
