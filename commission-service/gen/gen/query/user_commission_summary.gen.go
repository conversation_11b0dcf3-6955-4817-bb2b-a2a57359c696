// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/commission-service/gen/gen/model"
)

func newUserCommissionSummary(db *gorm.DB, opts ...gen.DOOption) userCommissionSummary {
	_userCommissionSummary := userCommissionSummary{}

	_userCommissionSummary.userCommissionSummaryDo.UseDB(db, opts...)
	_userCommissionSummary.userCommissionSummaryDo.UseModel(&model.UserCommissionSummary{})

	tableName := _userCommissionSummary.userCommissionSummaryDo.TableName()
	_userCommissionSummary.ALL = field.NewAsterisk(tableName)
	_userCommissionSummary.ID = field.NewString(tableName, "id")
	_userCommissionSummary.UserID = field.NewString(tableName, "user_id")
	_userCommissionSummary.TotalCommissionAmount = field.NewField(tableName, "total_commission_amount")
	_userCommissionSummary.CreatedAt = field.NewTime(tableName, "created_at")
	_userCommissionSummary.UpdatedAt = field.NewTime(tableName, "updated_at")

	_userCommissionSummary.fillFieldMap()

	return _userCommissionSummary
}

type userCommissionSummary struct {
	userCommissionSummaryDo userCommissionSummaryDo

	ALL                   field.Asterisk
	ID                    field.String // 汇总记录的唯一ID (主键)
	UserID                field.String // 用户ID (唯一键，逻辑外键指向用户表)
	TotalCommissionAmount field.Field  // 该用户累计获得的总佣金金额
	CreatedAt             field.Time   // 记录创建时间 (当用户首次产生佣金汇总记录时)
	UpdatedAt             field.Time   // 记录最后更新时间 (当总佣金更新时)

	fieldMap map[string]field.Expr
}

func (u userCommissionSummary) Table(newTableName string) *userCommissionSummary {
	u.userCommissionSummaryDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userCommissionSummary) As(alias string) *userCommissionSummary {
	u.userCommissionSummaryDo.DO = *(u.userCommissionSummaryDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userCommissionSummary) updateTableName(table string) *userCommissionSummary {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewString(table, "id")
	u.UserID = field.NewString(table, "user_id")
	u.TotalCommissionAmount = field.NewField(table, "total_commission_amount")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")

	u.fillFieldMap()

	return u
}

func (u *userCommissionSummary) WithContext(ctx context.Context) IUserCommissionSummaryDo {
	return u.userCommissionSummaryDo.WithContext(ctx)
}

func (u userCommissionSummary) TableName() string { return u.userCommissionSummaryDo.TableName() }

func (u userCommissionSummary) Alias() string { return u.userCommissionSummaryDo.Alias() }

func (u userCommissionSummary) Columns(cols ...field.Expr) gen.Columns {
	return u.userCommissionSummaryDo.Columns(cols...)
}

func (u *userCommissionSummary) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userCommissionSummary) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 5)
	u.fieldMap["id"] = u.ID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["total_commission_amount"] = u.TotalCommissionAmount
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
}

func (u userCommissionSummary) clone(db *gorm.DB) userCommissionSummary {
	u.userCommissionSummaryDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userCommissionSummary) replaceDB(db *gorm.DB) userCommissionSummary {
	u.userCommissionSummaryDo.ReplaceDB(db)
	return u
}

type userCommissionSummaryDo struct{ gen.DO }

type IUserCommissionSummaryDo interface {
	gen.SubQuery
	Debug() IUserCommissionSummaryDo
	WithContext(ctx context.Context) IUserCommissionSummaryDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserCommissionSummaryDo
	WriteDB() IUserCommissionSummaryDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserCommissionSummaryDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserCommissionSummaryDo
	Not(conds ...gen.Condition) IUserCommissionSummaryDo
	Or(conds ...gen.Condition) IUserCommissionSummaryDo
	Select(conds ...field.Expr) IUserCommissionSummaryDo
	Where(conds ...gen.Condition) IUserCommissionSummaryDo
	Order(conds ...field.Expr) IUserCommissionSummaryDo
	Distinct(cols ...field.Expr) IUserCommissionSummaryDo
	Omit(cols ...field.Expr) IUserCommissionSummaryDo
	Join(table schema.Tabler, on ...field.Expr) IUserCommissionSummaryDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserCommissionSummaryDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserCommissionSummaryDo
	Group(cols ...field.Expr) IUserCommissionSummaryDo
	Having(conds ...gen.Condition) IUserCommissionSummaryDo
	Limit(limit int) IUserCommissionSummaryDo
	Offset(offset int) IUserCommissionSummaryDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserCommissionSummaryDo
	Unscoped() IUserCommissionSummaryDo
	Create(values ...*model.UserCommissionSummary) error
	CreateInBatches(values []*model.UserCommissionSummary, batchSize int) error
	Save(values ...*model.UserCommissionSummary) error
	First() (*model.UserCommissionSummary, error)
	Take() (*model.UserCommissionSummary, error)
	Last() (*model.UserCommissionSummary, error)
	Find() ([]*model.UserCommissionSummary, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserCommissionSummary, err error)
	FindInBatches(result *[]*model.UserCommissionSummary, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.UserCommissionSummary) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserCommissionSummaryDo
	Assign(attrs ...field.AssignExpr) IUserCommissionSummaryDo
	Joins(fields ...field.RelationField) IUserCommissionSummaryDo
	Preload(fields ...field.RelationField) IUserCommissionSummaryDo
	FirstOrInit() (*model.UserCommissionSummary, error)
	FirstOrCreate() (*model.UserCommissionSummary, error)
	FindByPage(offset int, limit int) (result []*model.UserCommissionSummary, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserCommissionSummaryDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userCommissionSummaryDo) Debug() IUserCommissionSummaryDo {
	return u.withDO(u.DO.Debug())
}

func (u userCommissionSummaryDo) WithContext(ctx context.Context) IUserCommissionSummaryDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userCommissionSummaryDo) ReadDB() IUserCommissionSummaryDo {
	return u.Clauses(dbresolver.Read)
}

func (u userCommissionSummaryDo) WriteDB() IUserCommissionSummaryDo {
	return u.Clauses(dbresolver.Write)
}

func (u userCommissionSummaryDo) Session(config *gorm.Session) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Session(config))
}

func (u userCommissionSummaryDo) Clauses(conds ...clause.Expression) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userCommissionSummaryDo) Returning(value interface{}, columns ...string) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userCommissionSummaryDo) Not(conds ...gen.Condition) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userCommissionSummaryDo) Or(conds ...gen.Condition) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userCommissionSummaryDo) Select(conds ...field.Expr) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userCommissionSummaryDo) Where(conds ...gen.Condition) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userCommissionSummaryDo) Order(conds ...field.Expr) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userCommissionSummaryDo) Distinct(cols ...field.Expr) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userCommissionSummaryDo) Omit(cols ...field.Expr) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userCommissionSummaryDo) Join(table schema.Tabler, on ...field.Expr) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userCommissionSummaryDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserCommissionSummaryDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userCommissionSummaryDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserCommissionSummaryDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userCommissionSummaryDo) Group(cols ...field.Expr) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userCommissionSummaryDo) Having(conds ...gen.Condition) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userCommissionSummaryDo) Limit(limit int) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userCommissionSummaryDo) Offset(offset int) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userCommissionSummaryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userCommissionSummaryDo) Unscoped() IUserCommissionSummaryDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userCommissionSummaryDo) Create(values ...*model.UserCommissionSummary) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userCommissionSummaryDo) CreateInBatches(values []*model.UserCommissionSummary, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userCommissionSummaryDo) Save(values ...*model.UserCommissionSummary) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userCommissionSummaryDo) First() (*model.UserCommissionSummary, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserCommissionSummary), nil
	}
}

func (u userCommissionSummaryDo) Take() (*model.UserCommissionSummary, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserCommissionSummary), nil
	}
}

func (u userCommissionSummaryDo) Last() (*model.UserCommissionSummary, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserCommissionSummary), nil
	}
}

func (u userCommissionSummaryDo) Find() ([]*model.UserCommissionSummary, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserCommissionSummary), err
}

func (u userCommissionSummaryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserCommissionSummary, err error) {
	buf := make([]*model.UserCommissionSummary, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userCommissionSummaryDo) FindInBatches(result *[]*model.UserCommissionSummary, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userCommissionSummaryDo) Attrs(attrs ...field.AssignExpr) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userCommissionSummaryDo) Assign(attrs ...field.AssignExpr) IUserCommissionSummaryDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userCommissionSummaryDo) Joins(fields ...field.RelationField) IUserCommissionSummaryDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userCommissionSummaryDo) Preload(fields ...field.RelationField) IUserCommissionSummaryDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userCommissionSummaryDo) FirstOrInit() (*model.UserCommissionSummary, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserCommissionSummary), nil
	}
}

func (u userCommissionSummaryDo) FirstOrCreate() (*model.UserCommissionSummary, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserCommissionSummary), nil
	}
}

func (u userCommissionSummaryDo) FindByPage(offset int, limit int) (result []*model.UserCommissionSummary, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userCommissionSummaryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userCommissionSummaryDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userCommissionSummaryDo) Delete(models ...*model.UserCommissionSummary) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userCommissionSummaryDo) withDO(do gen.Dao) *userCommissionSummaryDo {
	u.DO = *do.(*gen.DO)
	return u
}
