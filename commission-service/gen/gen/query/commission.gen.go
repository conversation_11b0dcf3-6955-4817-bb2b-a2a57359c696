// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"cnb.cool/cymirror/ces-services/commission-service/gen/gen/model"
)

func newCommission(db *gorm.DB, opts ...gen.DOOption) commission {
	_commission := commission{}

	_commission.commissionDo.UseDB(db, opts...)
	_commission.commissionDo.UseModel(&model.Commission{})

	tableName := _commission.commissionDo.TableName()
	_commission.ALL = field.NewAsterisk(tableName)
	_commission.ID = field.NewString(tableName, "id")
	_commission.OrderID = field.NewString(tableName, "order_id")
	_commission.UserID = field.NewString(tableName, "user_id")
	_commission.InviteeID = field.NewString(tableName, "invitee_id")
	_commission.CommissionRuleID = field.NewString(tableName, "commission_rule_id")
	_commission.CommissionRateApplied = field.NewField(tableName, "commission_rate_applied")
	_commission.CommissionAmount = field.NewField(tableName, "commission_amount")
	_commission.CreatedAt = field.NewTime(tableName, "created_at")
	_commission.UpdatedAt = field.NewTime(tableName, "updated_at")
	_commission.IdempotencyKey = field.NewString(tableName, "idempotency_key")
	_commission.NftID = field.NewString(tableName, "nft_id")
	_commission.TradeType = field.NewInt16(tableName, "trade_type")
	_commission.TradeTypeExtID = field.NewString(tableName, "trade_type_ext_id")

	_commission.fillFieldMap()

	return _commission
}

type commission struct {
	commissionDo commissionDo

	ALL                   field.Asterisk
	ID                    field.String // 返佣流水记录ID (主键)
	OrderID               field.String // 产生此返佣的源订单ID (逻辑外键)
	UserID                field.String // 获得返佣的邀请人用户ID (逻辑外键)
	InviteeID             field.String // 产生交易的被邀请人用户ID (逻辑外键)
	CommissionRuleID      field.String // 计算此返佣时应用的返佣规则ID (逻辑外键，指向 commission_rules.id)
	CommissionRateApplied field.Field  // 实际应用的返佣比例 (例如 0.10 代表10%)
	CommissionAmount      field.Field  // 计算得出的返佣金额
	CreatedAt             field.Time   // 记录创建时间
	UpdatedAt             field.Time   // 记录最后更新时间
	IdempotencyKey        field.String // 幂等key 唯一索引
	NftID                 field.String // nft id
	TradeType             field.Int16  // 交易订单类型
	TradeTypeExtID        field.String // 交易订单额外id，二级订单类型对应二级市场表，求购对应供应记录详情

	fieldMap map[string]field.Expr
}

func (c commission) Table(newTableName string) *commission {
	c.commissionDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c commission) As(alias string) *commission {
	c.commissionDo.DO = *(c.commissionDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *commission) updateTableName(table string) *commission {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewString(table, "id")
	c.OrderID = field.NewString(table, "order_id")
	c.UserID = field.NewString(table, "user_id")
	c.InviteeID = field.NewString(table, "invitee_id")
	c.CommissionRuleID = field.NewString(table, "commission_rule_id")
	c.CommissionRateApplied = field.NewField(table, "commission_rate_applied")
	c.CommissionAmount = field.NewField(table, "commission_amount")
	c.CreatedAt = field.NewTime(table, "created_at")
	c.UpdatedAt = field.NewTime(table, "updated_at")
	c.IdempotencyKey = field.NewString(table, "idempotency_key")
	c.NftID = field.NewString(table, "nft_id")
	c.TradeType = field.NewInt16(table, "trade_type")
	c.TradeTypeExtID = field.NewString(table, "trade_type_ext_id")

	c.fillFieldMap()

	return c
}

func (c *commission) WithContext(ctx context.Context) ICommissionDo {
	return c.commissionDo.WithContext(ctx)
}

func (c commission) TableName() string { return c.commissionDo.TableName() }

func (c commission) Alias() string { return c.commissionDo.Alias() }

func (c commission) Columns(cols ...field.Expr) gen.Columns { return c.commissionDo.Columns(cols...) }

func (c *commission) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *commission) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 13)
	c.fieldMap["id"] = c.ID
	c.fieldMap["order_id"] = c.OrderID
	c.fieldMap["user_id"] = c.UserID
	c.fieldMap["invitee_id"] = c.InviteeID
	c.fieldMap["commission_rule_id"] = c.CommissionRuleID
	c.fieldMap["commission_rate_applied"] = c.CommissionRateApplied
	c.fieldMap["commission_amount"] = c.CommissionAmount
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["idempotency_key"] = c.IdempotencyKey
	c.fieldMap["nft_id"] = c.NftID
	c.fieldMap["trade_type"] = c.TradeType
	c.fieldMap["trade_type_ext_id"] = c.TradeTypeExtID
}

func (c commission) clone(db *gorm.DB) commission {
	c.commissionDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c commission) replaceDB(db *gorm.DB) commission {
	c.commissionDo.ReplaceDB(db)
	return c
}

type commissionDo struct{ gen.DO }

type ICommissionDo interface {
	gen.SubQuery
	Debug() ICommissionDo
	WithContext(ctx context.Context) ICommissionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ICommissionDo
	WriteDB() ICommissionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ICommissionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ICommissionDo
	Not(conds ...gen.Condition) ICommissionDo
	Or(conds ...gen.Condition) ICommissionDo
	Select(conds ...field.Expr) ICommissionDo
	Where(conds ...gen.Condition) ICommissionDo
	Order(conds ...field.Expr) ICommissionDo
	Distinct(cols ...field.Expr) ICommissionDo
	Omit(cols ...field.Expr) ICommissionDo
	Join(table schema.Tabler, on ...field.Expr) ICommissionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ICommissionDo
	RightJoin(table schema.Tabler, on ...field.Expr) ICommissionDo
	Group(cols ...field.Expr) ICommissionDo
	Having(conds ...gen.Condition) ICommissionDo
	Limit(limit int) ICommissionDo
	Offset(offset int) ICommissionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ICommissionDo
	Unscoped() ICommissionDo
	Create(values ...*model.Commission) error
	CreateInBatches(values []*model.Commission, batchSize int) error
	Save(values ...*model.Commission) error
	First() (*model.Commission, error)
	Take() (*model.Commission, error)
	Last() (*model.Commission, error)
	Find() ([]*model.Commission, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Commission, err error)
	FindInBatches(result *[]*model.Commission, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Commission) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ICommissionDo
	Assign(attrs ...field.AssignExpr) ICommissionDo
	Joins(fields ...field.RelationField) ICommissionDo
	Preload(fields ...field.RelationField) ICommissionDo
	FirstOrInit() (*model.Commission, error)
	FirstOrCreate() (*model.Commission, error)
	FindByPage(offset int, limit int) (result []*model.Commission, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ICommissionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c commissionDo) Debug() ICommissionDo {
	return c.withDO(c.DO.Debug())
}

func (c commissionDo) WithContext(ctx context.Context) ICommissionDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c commissionDo) ReadDB() ICommissionDo {
	return c.Clauses(dbresolver.Read)
}

func (c commissionDo) WriteDB() ICommissionDo {
	return c.Clauses(dbresolver.Write)
}

func (c commissionDo) Session(config *gorm.Session) ICommissionDo {
	return c.withDO(c.DO.Session(config))
}

func (c commissionDo) Clauses(conds ...clause.Expression) ICommissionDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c commissionDo) Returning(value interface{}, columns ...string) ICommissionDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c commissionDo) Not(conds ...gen.Condition) ICommissionDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c commissionDo) Or(conds ...gen.Condition) ICommissionDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c commissionDo) Select(conds ...field.Expr) ICommissionDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c commissionDo) Where(conds ...gen.Condition) ICommissionDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c commissionDo) Order(conds ...field.Expr) ICommissionDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c commissionDo) Distinct(cols ...field.Expr) ICommissionDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c commissionDo) Omit(cols ...field.Expr) ICommissionDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c commissionDo) Join(table schema.Tabler, on ...field.Expr) ICommissionDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c commissionDo) LeftJoin(table schema.Tabler, on ...field.Expr) ICommissionDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c commissionDo) RightJoin(table schema.Tabler, on ...field.Expr) ICommissionDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c commissionDo) Group(cols ...field.Expr) ICommissionDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c commissionDo) Having(conds ...gen.Condition) ICommissionDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c commissionDo) Limit(limit int) ICommissionDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c commissionDo) Offset(offset int) ICommissionDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c commissionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ICommissionDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c commissionDo) Unscoped() ICommissionDo {
	return c.withDO(c.DO.Unscoped())
}

func (c commissionDo) Create(values ...*model.Commission) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c commissionDo) CreateInBatches(values []*model.Commission, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c commissionDo) Save(values ...*model.Commission) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c commissionDo) First() (*model.Commission, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Commission), nil
	}
}

func (c commissionDo) Take() (*model.Commission, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Commission), nil
	}
}

func (c commissionDo) Last() (*model.Commission, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Commission), nil
	}
}

func (c commissionDo) Find() ([]*model.Commission, error) {
	result, err := c.DO.Find()
	return result.([]*model.Commission), err
}

func (c commissionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Commission, err error) {
	buf := make([]*model.Commission, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c commissionDo) FindInBatches(result *[]*model.Commission, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c commissionDo) Attrs(attrs ...field.AssignExpr) ICommissionDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c commissionDo) Assign(attrs ...field.AssignExpr) ICommissionDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c commissionDo) Joins(fields ...field.RelationField) ICommissionDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c commissionDo) Preload(fields ...field.RelationField) ICommissionDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c commissionDo) FirstOrInit() (*model.Commission, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Commission), nil
	}
}

func (c commissionDo) FirstOrCreate() (*model.Commission, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Commission), nil
	}
}

func (c commissionDo) FindByPage(offset int, limit int) (result []*model.Commission, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c commissionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c commissionDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c commissionDo) Delete(models ...*model.Commission) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *commissionDo) withDO(do gen.Dao) *commissionDo {
	c.DO = *do.(*gen.DO)
	return c
}
