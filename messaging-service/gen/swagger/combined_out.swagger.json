{"swagger": "2.0", "info": {"title": "消息中台服务", "version": "1.0.0"}, "tags": [{"name": "MessagingService"}, {"name": "TemplateService"}, {"name": "TaskService"}, {"name": "MessageQueueService"}], "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {}, "definitions": {"messagingCheckTaskStateResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "taskID": {"type": "string"}, "state": {"type": "string"}}}, "messagingCreateTemplateResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "templateID": {"type": "string"}}}, "messagingDeleteTemplateResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}}}, "messagingGetTemplateResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "templateID": {"type": "string"}, "description": {"type": "string"}, "templateSubject": {"type": "string"}, "templateBody": {"type": "string"}, "variableKeys": {"type": "array", "items": {"type": "string"}}, "messageType": {"type": "array", "items": {"type": "string"}}, "bussinessTag": {"type": "array", "items": {"type": "string"}}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}}, "messagingListTemplatesResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "templateAbstracts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/messagingTemplateAbstract"}}, "total": {"type": "integer", "format": "int64"}}}, "messagingRecipient": {"type": "object", "properties": {"receiver": {"type": "string"}, "personalizedVariables": {"type": "object", "additionalProperties": {"type": "string"}, "title": "可覆盖外层消息中 templateVariables 变量"}}}, "messagingRetryTaskResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}}}, "messagingSendResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "taskID": {"type": "string"}}}, "messagingTemplateAbstract": {"type": "object", "properties": {"templateID": {"type": "string"}, "description": {"type": "string"}}}, "messagingUpdateTemplateResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}}}, "mqGetSomeMessageResponse": {"type": "object", "properties": {"resultLength": {"type": "integer", "format": "int64"}, "messages": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/mqMessageEntity"}}}}, "mqMessageEntity": {"type": "object", "properties": {"topic": {"type": "string"}, "msgBody": {"type": "string", "format": "byte"}, "tag": {"type": "string"}}}, "mqProduceBatchMessagesResponse": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "success": {"type": "integer", "format": "int64"}}}, "mqProduceMessageResponse": {"type": "object", "properties": {"success": {"type": "boolean"}}}}, "securityDefinitions": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "security": [{"ApiKeyAuth": []}], "externalDocs": {"description": "注意: 200 响应中不包含code msg 等统一返回值, 仅代表data", "url": "none"}}