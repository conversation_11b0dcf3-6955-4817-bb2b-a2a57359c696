// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: ces/mq/mq/mq.proto

package mqpb

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MessageEntity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Topic         string                 `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	MsgBody       []byte                 `protobuf:"bytes,2,opt,name=msg_body,json=msgBody,proto3" json:"msg_body,omitempty"`
	Tag           string                 `protobuf:"bytes,3,opt,name=tag,proto3" json:"tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageEntity) Reset() {
	*x = MessageEntity{}
	mi := &file_ces_mq_mq_mq_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageEntity) ProtoMessage() {}

func (x *MessageEntity) ProtoReflect() protoreflect.Message {
	mi := &file_ces_mq_mq_mq_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageEntity.ProtoReflect.Descriptor instead.
func (*MessageEntity) Descriptor() ([]byte, []int) {
	return file_ces_mq_mq_mq_proto_rawDescGZIP(), []int{0}
}

func (x *MessageEntity) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *MessageEntity) GetMsgBody() []byte {
	if x != nil {
		return x.MsgBody
	}
	return nil
}

func (x *MessageEntity) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

type GetSomeMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	N             uint32                 `protobuf:"varint,1,opt,name=n,proto3" json:"n,omitempty"`
	Topic         string                 `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	Tag           string                 `protobuf:"bytes,3,opt,name=tag,proto3" json:"tag,omitempty"`
	TimeoutSecond uint32                 `protobuf:"varint,4,opt,name=timeout_second,json=timeoutSecond,proto3" json:"timeout_second,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSomeMessageRequest) Reset() {
	*x = GetSomeMessageRequest{}
	mi := &file_ces_mq_mq_mq_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSomeMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSomeMessageRequest) ProtoMessage() {}

func (x *GetSomeMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ces_mq_mq_mq_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSomeMessageRequest.ProtoReflect.Descriptor instead.
func (*GetSomeMessageRequest) Descriptor() ([]byte, []int) {
	return file_ces_mq_mq_mq_proto_rawDescGZIP(), []int{1}
}

func (x *GetSomeMessageRequest) GetN() uint32 {
	if x != nil {
		return x.N
	}
	return 0
}

func (x *GetSomeMessageRequest) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *GetSomeMessageRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *GetSomeMessageRequest) GetTimeoutSecond() uint32 {
	if x != nil {
		return x.TimeoutSecond
	}
	return 0
}

type GetSomeMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResultLength  uint32                 `protobuf:"varint,1,opt,name=result_length,json=resultLength,proto3" json:"result_length,omitempty"`
	Messages      []*MessageEntity       `protobuf:"bytes,2,rep,name=messages,proto3" json:"messages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSomeMessageResponse) Reset() {
	*x = GetSomeMessageResponse{}
	mi := &file_ces_mq_mq_mq_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSomeMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSomeMessageResponse) ProtoMessage() {}

func (x *GetSomeMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ces_mq_mq_mq_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSomeMessageResponse.ProtoReflect.Descriptor instead.
func (*GetSomeMessageResponse) Descriptor() ([]byte, []int) {
	return file_ces_mq_mq_mq_proto_rawDescGZIP(), []int{2}
}

func (x *GetSomeMessageResponse) GetResultLength() uint32 {
	if x != nil {
		return x.ResultLength
	}
	return 0
}

func (x *GetSomeMessageResponse) GetMessages() []*MessageEntity {
	if x != nil {
		return x.Messages
	}
	return nil
}

type GetMessageStreamRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Topic         string                 `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	Tag           string                 `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMessageStreamRequest) Reset() {
	*x = GetMessageStreamRequest{}
	mi := &file_ces_mq_mq_mq_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMessageStreamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMessageStreamRequest) ProtoMessage() {}

func (x *GetMessageStreamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ces_mq_mq_mq_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMessageStreamRequest.ProtoReflect.Descriptor instead.
func (*GetMessageStreamRequest) Descriptor() ([]byte, []int) {
	return file_ces_mq_mq_mq_proto_rawDescGZIP(), []int{3}
}

func (x *GetMessageStreamRequest) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *GetMessageStreamRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

type ProduceMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Msg           *MessageEntity         `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProduceMessageRequest) Reset() {
	*x = ProduceMessageRequest{}
	mi := &file_ces_mq_mq_mq_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProduceMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProduceMessageRequest) ProtoMessage() {}

func (x *ProduceMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ces_mq_mq_mq_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProduceMessageRequest.ProtoReflect.Descriptor instead.
func (*ProduceMessageRequest) Descriptor() ([]byte, []int) {
	return file_ces_mq_mq_mq_proto_rawDescGZIP(), []int{4}
}

func (x *ProduceMessageRequest) GetMsg() *MessageEntity {
	if x != nil {
		return x.Msg
	}
	return nil
}

type ProduceMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProduceMessageResponse) Reset() {
	*x = ProduceMessageResponse{}
	mi := &file_ces_mq_mq_mq_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProduceMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProduceMessageResponse) ProtoMessage() {}

func (x *ProduceMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ces_mq_mq_mq_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProduceMessageResponse.ProtoReflect.Descriptor instead.
func (*ProduceMessageResponse) Descriptor() ([]byte, []int) {
	return file_ces_mq_mq_mq_proto_rawDescGZIP(), []int{5}
}

func (x *ProduceMessageResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type ProduceBatchMessagesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BatchLength   uint32                 `protobuf:"varint,1,opt,name=batch_length,json=batchLength,proto3" json:"batch_length,omitempty"`
	MessageBatch  []*MessageEntity       `protobuf:"bytes,2,rep,name=message_batch,json=messageBatch,proto3" json:"message_batch,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProduceBatchMessagesRequest) Reset() {
	*x = ProduceBatchMessagesRequest{}
	mi := &file_ces_mq_mq_mq_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProduceBatchMessagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProduceBatchMessagesRequest) ProtoMessage() {}

func (x *ProduceBatchMessagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ces_mq_mq_mq_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProduceBatchMessagesRequest.ProtoReflect.Descriptor instead.
func (*ProduceBatchMessagesRequest) Descriptor() ([]byte, []int) {
	return file_ces_mq_mq_mq_proto_rawDescGZIP(), []int{6}
}

func (x *ProduceBatchMessagesRequest) GetBatchLength() uint32 {
	if x != nil {
		return x.BatchLength
	}
	return 0
}

func (x *ProduceBatchMessagesRequest) GetMessageBatch() []*MessageEntity {
	if x != nil {
		return x.MessageBatch
	}
	return nil
}

type ProduceBatchMessagesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         uint32                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Success       uint32                 `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProduceBatchMessagesResponse) Reset() {
	*x = ProduceBatchMessagesResponse{}
	mi := &file_ces_mq_mq_mq_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProduceBatchMessagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProduceBatchMessagesResponse) ProtoMessage() {}

func (x *ProduceBatchMessagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ces_mq_mq_mq_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProduceBatchMessagesResponse.ProtoReflect.Descriptor instead.
func (*ProduceBatchMessagesResponse) Descriptor() ([]byte, []int) {
	return file_ces_mq_mq_mq_proto_rawDescGZIP(), []int{7}
}

func (x *ProduceBatchMessagesResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ProduceBatchMessagesResponse) GetSuccess() uint32 {
	if x != nil {
		return x.Success
	}
	return 0
}

var File_ces_mq_mq_mq_proto protoreflect.FileDescriptor

const file_ces_mq_mq_mq_proto_rawDesc = "" +
	"\n" +
	"\x12ces/mq/mq/mq.proto\x12\tces.mq.mq\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a.protoc-gen-openapiv2/options/annotations.proto\"R\n" +
	"\rMessageEntity\x12\x14\n" +
	"\x05topic\x18\x01 \x01(\tR\x05topic\x12\x19\n" +
	"\bmsg_body\x18\x02 \x01(\fR\amsgBody\x12\x10\n" +
	"\x03tag\x18\x03 \x01(\tR\x03tag\"t\n" +
	"\x15GetSomeMessageRequest\x12\f\n" +
	"\x01n\x18\x01 \x01(\rR\x01n\x12\x14\n" +
	"\x05topic\x18\x02 \x01(\tR\x05topic\x12\x10\n" +
	"\x03tag\x18\x03 \x01(\tR\x03tag\x12%\n" +
	"\x0etimeout_second\x18\x04 \x01(\rR\rtimeoutSecond\"s\n" +
	"\x16GetSomeMessageResponse\x12#\n" +
	"\rresult_length\x18\x01 \x01(\rR\fresultLength\x124\n" +
	"\bmessages\x18\x02 \x03(\v2\x18.ces.mq.mq.MessageEntityR\bmessages\"A\n" +
	"\x17GetMessageStreamRequest\x12\x14\n" +
	"\x05topic\x18\x01 \x01(\tR\x05topic\x12\x10\n" +
	"\x03tag\x18\x02 \x01(\tR\x03tag\"C\n" +
	"\x15ProduceMessageRequest\x12*\n" +
	"\x03msg\x18\x01 \x01(\v2\x18.ces.mq.mq.MessageEntityR\x03msg\"2\n" +
	"\x16ProduceMessageResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"\x7f\n" +
	"\x1bProduceBatchMessagesRequest\x12!\n" +
	"\fbatch_length\x18\x01 \x01(\rR\vbatchLength\x12=\n" +
	"\rmessage_batch\x18\x02 \x03(\v2\x18.ces.mq.mq.MessageEntityR\fmessageBatch\"N\n" +
	"\x1cProduceBatchMessagesResponse\x12\x14\n" +
	"\x05total\x18\x01 \x01(\rR\x05total\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\rR\asuccess2\x80\x03\n" +
	"\x13MessageQueueService\x12U\n" +
	"\x0eGetSomeMessage\x12 .ces.mq.mq.GetSomeMessageRequest\x1a!.ces.mq.mq.GetSomeMessageResponse\x12R\n" +
	"\x10GetMessageStream\x12\".ces.mq.mq.GetMessageStreamRequest\x1a\x18.ces.mq.mq.MessageEntity0\x01\x12U\n" +
	"\x0eProduceMessage\x12 .ces.mq.mq.ProduceMessageRequest\x1a!.ces.mq.mq.ProduceMessageResponse\x12g\n" +
	"\x14ProduceBatchMessages\x12&.ces.mq.mq.ProduceBatchMessagesRequest\x1a'.ces.mq.mq.ProduceBatchMessagesResponseB\aZ\x05/mqpbb\x06proto3"

var (
	file_ces_mq_mq_mq_proto_rawDescOnce sync.Once
	file_ces_mq_mq_mq_proto_rawDescData []byte
)

func file_ces_mq_mq_mq_proto_rawDescGZIP() []byte {
	file_ces_mq_mq_mq_proto_rawDescOnce.Do(func() {
		file_ces_mq_mq_mq_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_ces_mq_mq_mq_proto_rawDesc), len(file_ces_mq_mq_mq_proto_rawDesc)))
	})
	return file_ces_mq_mq_mq_proto_rawDescData
}

var file_ces_mq_mq_mq_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_ces_mq_mq_mq_proto_goTypes = []any{
	(*MessageEntity)(nil),                // 0: ces.mq.mq.MessageEntity
	(*GetSomeMessageRequest)(nil),        // 1: ces.mq.mq.GetSomeMessageRequest
	(*GetSomeMessageResponse)(nil),       // 2: ces.mq.mq.GetSomeMessageResponse
	(*GetMessageStreamRequest)(nil),      // 3: ces.mq.mq.GetMessageStreamRequest
	(*ProduceMessageRequest)(nil),        // 4: ces.mq.mq.ProduceMessageRequest
	(*ProduceMessageResponse)(nil),       // 5: ces.mq.mq.ProduceMessageResponse
	(*ProduceBatchMessagesRequest)(nil),  // 6: ces.mq.mq.ProduceBatchMessagesRequest
	(*ProduceBatchMessagesResponse)(nil), // 7: ces.mq.mq.ProduceBatchMessagesResponse
}
var file_ces_mq_mq_mq_proto_depIdxs = []int32{
	0, // 0: ces.mq.mq.GetSomeMessageResponse.messages:type_name -> ces.mq.mq.MessageEntity
	0, // 1: ces.mq.mq.ProduceMessageRequest.msg:type_name -> ces.mq.mq.MessageEntity
	0, // 2: ces.mq.mq.ProduceBatchMessagesRequest.message_batch:type_name -> ces.mq.mq.MessageEntity
	1, // 3: ces.mq.mq.MessageQueueService.GetSomeMessage:input_type -> ces.mq.mq.GetSomeMessageRequest
	3, // 4: ces.mq.mq.MessageQueueService.GetMessageStream:input_type -> ces.mq.mq.GetMessageStreamRequest
	4, // 5: ces.mq.mq.MessageQueueService.ProduceMessage:input_type -> ces.mq.mq.ProduceMessageRequest
	6, // 6: ces.mq.mq.MessageQueueService.ProduceBatchMessages:input_type -> ces.mq.mq.ProduceBatchMessagesRequest
	2, // 7: ces.mq.mq.MessageQueueService.GetSomeMessage:output_type -> ces.mq.mq.GetSomeMessageResponse
	0, // 8: ces.mq.mq.MessageQueueService.GetMessageStream:output_type -> ces.mq.mq.MessageEntity
	5, // 9: ces.mq.mq.MessageQueueService.ProduceMessage:output_type -> ces.mq.mq.ProduceMessageResponse
	7, // 10: ces.mq.mq.MessageQueueService.ProduceBatchMessages:output_type -> ces.mq.mq.ProduceBatchMessagesResponse
	7, // [7:11] is the sub-list for method output_type
	3, // [3:7] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_ces_mq_mq_mq_proto_init() }
func file_ces_mq_mq_mq_proto_init() {
	if File_ces_mq_mq_mq_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_ces_mq_mq_mq_proto_rawDesc), len(file_ces_mq_mq_mq_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_ces_mq_mq_mq_proto_goTypes,
		DependencyIndexes: file_ces_mq_mq_mq_proto_depIdxs,
		MessageInfos:      file_ces_mq_mq_mq_proto_msgTypes,
	}.Build()
	File_ces_mq_mq_mq_proto = out.File
	file_ces_mq_mq_mq_proto_goTypes = nil
	file_ces_mq_mq_mq_proto_depIdxs = nil
}
