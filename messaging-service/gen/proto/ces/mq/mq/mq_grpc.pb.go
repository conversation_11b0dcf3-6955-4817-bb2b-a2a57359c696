// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: ces/mq/mq/mq.proto

package mqpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MessageQueueService_GetSomeMessage_FullMethodName       = "/ces.mq.mq.MessageQueueService/GetSomeMessage"
	MessageQueueService_GetMessageStream_FullMethodName     = "/ces.mq.mq.MessageQueueService/GetMessageStream"
	MessageQueueService_ProduceMessage_FullMethodName       = "/ces.mq.mq.MessageQueueService/ProduceMessage"
	MessageQueueService_ProduceBatchMessages_FullMethodName = "/ces.mq.mq.MessageQueueService/ProduceBatchMessages"
)

// MessageQueueServiceClient is the client API for MessageQueueService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MessageQueueServiceClient interface {
	// 获取n条topic下的消息
	GetSomeMessage(ctx context.Context, in *GetSomeMessageRequest, opts ...grpc.CallOption) (*GetSomeMessageResponse, error)
	// 建立消息流
	GetMessageStream(ctx context.Context, in *GetMessageStreamRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[MessageEntity], error)
	// 生产一条消息
	ProduceMessage(ctx context.Context, in *ProduceMessageRequest, opts ...grpc.CallOption) (*ProduceMessageResponse, error)
	// 生产n条消息
	ProduceBatchMessages(ctx context.Context, in *ProduceBatchMessagesRequest, opts ...grpc.CallOption) (*ProduceBatchMessagesResponse, error)
}

type messageQueueServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMessageQueueServiceClient(cc grpc.ClientConnInterface) MessageQueueServiceClient {
	return &messageQueueServiceClient{cc}
}

func (c *messageQueueServiceClient) GetSomeMessage(ctx context.Context, in *GetSomeMessageRequest, opts ...grpc.CallOption) (*GetSomeMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSomeMessageResponse)
	err := c.cc.Invoke(ctx, MessageQueueService_GetSomeMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageQueueServiceClient) GetMessageStream(ctx context.Context, in *GetMessageStreamRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[MessageEntity], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &MessageQueueService_ServiceDesc.Streams[0], MessageQueueService_GetMessageStream_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[GetMessageStreamRequest, MessageEntity]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type MessageQueueService_GetMessageStreamClient = grpc.ServerStreamingClient[MessageEntity]

func (c *messageQueueServiceClient) ProduceMessage(ctx context.Context, in *ProduceMessageRequest, opts ...grpc.CallOption) (*ProduceMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProduceMessageResponse)
	err := c.cc.Invoke(ctx, MessageQueueService_ProduceMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageQueueServiceClient) ProduceBatchMessages(ctx context.Context, in *ProduceBatchMessagesRequest, opts ...grpc.CallOption) (*ProduceBatchMessagesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProduceBatchMessagesResponse)
	err := c.cc.Invoke(ctx, MessageQueueService_ProduceBatchMessages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageQueueServiceServer is the server API for MessageQueueService service.
// All implementations should embed UnimplementedMessageQueueServiceServer
// for forward compatibility.
type MessageQueueServiceServer interface {
	// 获取n条topic下的消息
	GetSomeMessage(context.Context, *GetSomeMessageRequest) (*GetSomeMessageResponse, error)
	// 建立消息流
	GetMessageStream(*GetMessageStreamRequest, grpc.ServerStreamingServer[MessageEntity]) error
	// 生产一条消息
	ProduceMessage(context.Context, *ProduceMessageRequest) (*ProduceMessageResponse, error)
	// 生产n条消息
	ProduceBatchMessages(context.Context, *ProduceBatchMessagesRequest) (*ProduceBatchMessagesResponse, error)
}

// UnimplementedMessageQueueServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMessageQueueServiceServer struct{}

func (UnimplementedMessageQueueServiceServer) GetSomeMessage(context.Context, *GetSomeMessageRequest) (*GetSomeMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSomeMessage not implemented")
}
func (UnimplementedMessageQueueServiceServer) GetMessageStream(*GetMessageStreamRequest, grpc.ServerStreamingServer[MessageEntity]) error {
	return status.Errorf(codes.Unimplemented, "method GetMessageStream not implemented")
}
func (UnimplementedMessageQueueServiceServer) ProduceMessage(context.Context, *ProduceMessageRequest) (*ProduceMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProduceMessage not implemented")
}
func (UnimplementedMessageQueueServiceServer) ProduceBatchMessages(context.Context, *ProduceBatchMessagesRequest) (*ProduceBatchMessagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProduceBatchMessages not implemented")
}
func (UnimplementedMessageQueueServiceServer) testEmbeddedByValue() {}

// UnsafeMessageQueueServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessageQueueServiceServer will
// result in compilation errors.
type UnsafeMessageQueueServiceServer interface {
	mustEmbedUnimplementedMessageQueueServiceServer()
}

func RegisterMessageQueueServiceServer(s grpc.ServiceRegistrar, srv MessageQueueServiceServer) {
	// If the following call pancis, it indicates UnimplementedMessageQueueServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MessageQueueService_ServiceDesc, srv)
}

func _MessageQueueService_GetSomeMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSomeMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageQueueServiceServer).GetSomeMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageQueueService_GetSomeMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageQueueServiceServer).GetSomeMessage(ctx, req.(*GetSomeMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageQueueService_GetMessageStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetMessageStreamRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(MessageQueueServiceServer).GetMessageStream(m, &grpc.GenericServerStream[GetMessageStreamRequest, MessageEntity]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type MessageQueueService_GetMessageStreamServer = grpc.ServerStreamingServer[MessageEntity]

func _MessageQueueService_ProduceMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProduceMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageQueueServiceServer).ProduceMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageQueueService_ProduceMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageQueueServiceServer).ProduceMessage(ctx, req.(*ProduceMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageQueueService_ProduceBatchMessages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProduceBatchMessagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageQueueServiceServer).ProduceBatchMessages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageQueueService_ProduceBatchMessages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageQueueServiceServer).ProduceBatchMessages(ctx, req.(*ProduceBatchMessagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MessageQueueService_ServiceDesc is the grpc.ServiceDesc for MessageQueueService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessageQueueService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ces.mq.mq.MessageQueueService",
	HandlerType: (*MessageQueueServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSomeMessage",
			Handler:    _MessageQueueService_GetSomeMessage_Handler,
		},
		{
			MethodName: "ProduceMessage",
			Handler:    _MessageQueueService_ProduceMessage_Handler,
		},
		{
			MethodName: "ProduceBatchMessages",
			Handler:    _MessageQueueService_ProduceBatchMessages_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetMessageStream",
			Handler:       _MessageQueueService_GetMessageStream_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "ces/mq/mq/mq.proto",
}
