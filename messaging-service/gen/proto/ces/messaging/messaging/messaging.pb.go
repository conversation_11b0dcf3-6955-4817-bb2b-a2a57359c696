// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: ces/messaging/messaging/messaging.proto

package messagingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MessageType int32

const (
	// 短信
	MessageType_SMS MessageType = 0
	// 邮件
	MessageType_EMAIL MessageType = 1
	// 站内信
	MessageType_NOTIFICATION MessageType = 2
)

// Enum value maps for MessageType.
var (
	MessageType_name = map[int32]string{
		0: "SMS",
		1: "EMAIL",
		2: "NOTIFICATION",
	}
	MessageType_value = map[string]int32{
		"SMS":          0,
		"EMAIL":        1,
		"NOTIFICATION": 2,
	}
)

func (x MessageType) Enum() *MessageType {
	p := new(MessageType)
	*p = x
	return p
}

func (x MessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_ces_messaging_messaging_messaging_proto_enumTypes[0].Descriptor()
}

func (MessageType) Type() protoreflect.EnumType {
	return &file_ces_messaging_messaging_messaging_proto_enumTypes[0]
}

func (x MessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageType.Descriptor instead.
func (MessageType) EnumDescriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{0}
}

type TemplateAbstract struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TemplateID    string                 `protobuf:"bytes,1,opt,name=templateID,proto3" json:"templateID,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TemplateAbstract) Reset() {
	*x = TemplateAbstract{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TemplateAbstract) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateAbstract) ProtoMessage() {}

func (x *TemplateAbstract) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateAbstract.ProtoReflect.Descriptor instead.
func (*TemplateAbstract) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{0}
}

func (x *TemplateAbstract) GetTemplateID() string {
	if x != nil {
		return x.TemplateID
	}
	return ""
}

func (x *TemplateAbstract) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type CreateTemplateRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TemplateID      string                 `protobuf:"bytes,1,opt,name=templateID,proto3" json:"templateID,omitempty"`
	Description     string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	TemplateSubject string                 `protobuf:"bytes,3,opt,name=templateSubject,proto3" json:"templateSubject,omitempty"`
	TemplateBody    string                 `protobuf:"bytes,4,opt,name=templateBody,proto3" json:"templateBody,omitempty"`
	VariableKeys    []string               `protobuf:"bytes,5,rep,name=variableKeys,proto3" json:"variableKeys,omitempty"`
	MessageType     []string               `protobuf:"bytes,6,rep,name=messageType,proto3" json:"messageType,omitempty"`
	BussinessTag    []string               `protobuf:"bytes,7,rep,name=bussinessTag,proto3" json:"bussinessTag,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreateTemplateRequest) Reset() {
	*x = CreateTemplateRequest{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTemplateRequest) ProtoMessage() {}

func (x *CreateTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTemplateRequest.ProtoReflect.Descriptor instead.
func (*CreateTemplateRequest) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{1}
}

func (x *CreateTemplateRequest) GetTemplateID() string {
	if x != nil {
		return x.TemplateID
	}
	return ""
}

func (x *CreateTemplateRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateTemplateRequest) GetTemplateSubject() string {
	if x != nil {
		return x.TemplateSubject
	}
	return ""
}

func (x *CreateTemplateRequest) GetTemplateBody() string {
	if x != nil {
		return x.TemplateBody
	}
	return ""
}

func (x *CreateTemplateRequest) GetVariableKeys() []string {
	if x != nil {
		return x.VariableKeys
	}
	return nil
}

func (x *CreateTemplateRequest) GetMessageType() []string {
	if x != nil {
		return x.MessageType
	}
	return nil
}

func (x *CreateTemplateRequest) GetBussinessTag() []string {
	if x != nil {
		return x.BussinessTag
	}
	return nil
}

type CreateTemplateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	TemplateID    string                 `protobuf:"bytes,3,opt,name=templateID,proto3" json:"templateID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTemplateResponse) Reset() {
	*x = CreateTemplateResponse{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTemplateResponse) ProtoMessage() {}

func (x *CreateTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTemplateResponse.ProtoReflect.Descriptor instead.
func (*CreateTemplateResponse) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{2}
}

func (x *CreateTemplateResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateTemplateResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateTemplateResponse) GetTemplateID() string {
	if x != nil {
		return x.TemplateID
	}
	return ""
}

type UpdateTemplateRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TemplateID      string                 `protobuf:"bytes,1,opt,name=templateID,proto3" json:"templateID,omitempty"`
	Description     string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	TemplateSubject string                 `protobuf:"bytes,3,opt,name=templateSubject,proto3" json:"templateSubject,omitempty"`
	TemplateBody    string                 `protobuf:"bytes,4,opt,name=templateBody,proto3" json:"templateBody,omitempty"`
	VariableKeys    []string               `protobuf:"bytes,5,rep,name=variableKeys,proto3" json:"variableKeys,omitempty"`
	MessageType     []string               `protobuf:"bytes,6,rep,name=messageType,proto3" json:"messageType,omitempty"`
	BussinessTag    []string               `protobuf:"bytes,7,rep,name=bussinessTag,proto3" json:"bussinessTag,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UpdateTemplateRequest) Reset() {
	*x = UpdateTemplateRequest{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTemplateRequest) ProtoMessage() {}

func (x *UpdateTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTemplateRequest.ProtoReflect.Descriptor instead.
func (*UpdateTemplateRequest) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateTemplateRequest) GetTemplateID() string {
	if x != nil {
		return x.TemplateID
	}
	return ""
}

func (x *UpdateTemplateRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateTemplateRequest) GetTemplateSubject() string {
	if x != nil {
		return x.TemplateSubject
	}
	return ""
}

func (x *UpdateTemplateRequest) GetTemplateBody() string {
	if x != nil {
		return x.TemplateBody
	}
	return ""
}

func (x *UpdateTemplateRequest) GetVariableKeys() []string {
	if x != nil {
		return x.VariableKeys
	}
	return nil
}

func (x *UpdateTemplateRequest) GetMessageType() []string {
	if x != nil {
		return x.MessageType
	}
	return nil
}

func (x *UpdateTemplateRequest) GetBussinessTag() []string {
	if x != nil {
		return x.BussinessTag
	}
	return nil
}

type UpdateTemplateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTemplateResponse) Reset() {
	*x = UpdateTemplateResponse{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTemplateResponse) ProtoMessage() {}

func (x *UpdateTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTemplateResponse.ProtoReflect.Descriptor instead.
func (*UpdateTemplateResponse) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateTemplateResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateTemplateResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteTemplateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TemplateID    string                 `protobuf:"bytes,1,opt,name=templateID,proto3" json:"templateID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteTemplateRequest) Reset() {
	*x = DeleteTemplateRequest{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTemplateRequest) ProtoMessage() {}

func (x *DeleteTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTemplateRequest.ProtoReflect.Descriptor instead.
func (*DeleteTemplateRequest) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteTemplateRequest) GetTemplateID() string {
	if x != nil {
		return x.TemplateID
	}
	return ""
}

type DeleteTemplateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteTemplateResponse) Reset() {
	*x = DeleteTemplateResponse{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTemplateResponse) ProtoMessage() {}

func (x *DeleteTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTemplateResponse.ProtoReflect.Descriptor instead.
func (*DeleteTemplateResponse) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteTemplateResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeleteTemplateResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetTemplateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TemplateID    string                 `protobuf:"bytes,1,opt,name=templateID,proto3" json:"templateID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTemplateRequest) Reset() {
	*x = GetTemplateRequest{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplateRequest) ProtoMessage() {}

func (x *GetTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplateRequest.ProtoReflect.Descriptor instead.
func (*GetTemplateRequest) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{7}
}

func (x *GetTemplateRequest) GetTemplateID() string {
	if x != nil {
		return x.TemplateID
	}
	return ""
}

type GetTemplateResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Code            uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message         string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	TemplateID      string                 `protobuf:"bytes,3,opt,name=templateID,proto3" json:"templateID,omitempty"`
	Description     string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	TemplateSubject string                 `protobuf:"bytes,5,opt,name=templateSubject,proto3" json:"templateSubject,omitempty"`
	TemplateBody    string                 `protobuf:"bytes,6,opt,name=templateBody,proto3" json:"templateBody,omitempty"`
	VariableKeys    []string               `protobuf:"bytes,7,rep,name=variableKeys,proto3" json:"variableKeys,omitempty"`
	MessageType     []string               `protobuf:"bytes,8,rep,name=messageType,proto3" json:"messageType,omitempty"`
	BussinessTag    []string               `protobuf:"bytes,9,rep,name=bussinessTag,proto3" json:"bussinessTag,omitempty"`
	CreateTime      string                 `protobuf:"bytes,10,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime      string                 `protobuf:"bytes,11,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetTemplateResponse) Reset() {
	*x = GetTemplateResponse{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplateResponse) ProtoMessage() {}

func (x *GetTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplateResponse.ProtoReflect.Descriptor instead.
func (*GetTemplateResponse) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{8}
}

func (x *GetTemplateResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetTemplateResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetTemplateResponse) GetTemplateID() string {
	if x != nil {
		return x.TemplateID
	}
	return ""
}

func (x *GetTemplateResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GetTemplateResponse) GetTemplateSubject() string {
	if x != nil {
		return x.TemplateSubject
	}
	return ""
}

func (x *GetTemplateResponse) GetTemplateBody() string {
	if x != nil {
		return x.TemplateBody
	}
	return ""
}

func (x *GetTemplateResponse) GetVariableKeys() []string {
	if x != nil {
		return x.VariableKeys
	}
	return nil
}

func (x *GetTemplateResponse) GetMessageType() []string {
	if x != nil {
		return x.MessageType
	}
	return nil
}

func (x *GetTemplateResponse) GetBussinessTag() []string {
	if x != nil {
		return x.BussinessTag
	}
	return nil
}

func (x *GetTemplateResponse) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *GetTemplateResponse) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

type ListTemplatesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Offset        uint32                 `protobuf:"varint,1,opt,name=Offset,proto3" json:"Offset,omitempty"`
	Limit         uint32                 `protobuf:"varint,2,opt,name=Limit,proto3" json:"Limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTemplatesRequest) Reset() {
	*x = ListTemplatesRequest{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatesRequest) ProtoMessage() {}

func (x *ListTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatesRequest.ProtoReflect.Descriptor instead.
func (*ListTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{9}
}

func (x *ListTemplatesRequest) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ListTemplatesRequest) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type ListTemplatesResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Code              uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message           string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	TemplateAbstracts []*TemplateAbstract    `protobuf:"bytes,3,rep,name=templateAbstracts,proto3" json:"templateAbstracts,omitempty"`
	Total             uint32                 `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ListTemplatesResponse) Reset() {
	*x = ListTemplatesResponse{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatesResponse) ProtoMessage() {}

func (x *ListTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatesResponse.ProtoReflect.Descriptor instead.
func (*ListTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{10}
}

func (x *ListTemplatesResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListTemplatesResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListTemplatesResponse) GetTemplateAbstracts() []*TemplateAbstract {
	if x != nil {
		return x.TemplateAbstracts
	}
	return nil
}

func (x *ListTemplatesResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type SendRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 模板 code 对应管理员创建的模板
	TemplateID  string `protobuf:"bytes,1,opt,name=templateID,proto3" json:"templateID,omitempty"`
	MessageType string `protobuf:"bytes,2,opt,name=messageType,proto3" json:"messageType,omitempty"`
	// 接收方
	Receiver string `protobuf:"bytes,3,opt,name=receiver,proto3" json:"receiver,omitempty"`
	// 模板变量数据，key为模板中的变量名，value为替换的值
	TemplateVariables map[string]string `protobuf:"bytes,4,rep,name=templateVariables,proto3" json:"templateVariables,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SendRequest) Reset() {
	*x = SendRequest{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendRequest) ProtoMessage() {}

func (x *SendRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendRequest.ProtoReflect.Descriptor instead.
func (*SendRequest) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{11}
}

func (x *SendRequest) GetTemplateID() string {
	if x != nil {
		return x.TemplateID
	}
	return ""
}

func (x *SendRequest) GetMessageType() string {
	if x != nil {
		return x.MessageType
	}
	return ""
}

func (x *SendRequest) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

func (x *SendRequest) GetTemplateVariables() map[string]string {
	if x != nil {
		return x.TemplateVariables
	}
	return nil
}

type BatchSendRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 模板 code 对应管理员创建的模板
	TemplateID  string `protobuf:"bytes,1,opt,name=templateID,proto3" json:"templateID,omitempty"`
	MessageType string `protobuf:"bytes,2,opt,name=messageType,proto3" json:"messageType,omitempty"`
	// 接收方
	Recipients []*Recipient `protobuf:"bytes,3,rep,name=recipients,proto3" json:"recipients,omitempty"`
	// 模板变量数据，key为模板中的变量名，value为替换的值
	TemplateVariables map[string]string `protobuf:"bytes,4,rep,name=templateVariables,proto3" json:"templateVariables,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BatchSendRequest) Reset() {
	*x = BatchSendRequest{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchSendRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchSendRequest) ProtoMessage() {}

func (x *BatchSendRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchSendRequest.ProtoReflect.Descriptor instead.
func (*BatchSendRequest) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{12}
}

func (x *BatchSendRequest) GetTemplateID() string {
	if x != nil {
		return x.TemplateID
	}
	return ""
}

func (x *BatchSendRequest) GetMessageType() string {
	if x != nil {
		return x.MessageType
	}
	return ""
}

func (x *BatchSendRequest) GetRecipients() []*Recipient {
	if x != nil {
		return x.Recipients
	}
	return nil
}

func (x *BatchSendRequest) GetTemplateVariables() map[string]string {
	if x != nil {
		return x.TemplateVariables
	}
	return nil
}

type Recipient struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Receiver string                 `protobuf:"bytes,1,opt,name=receiver,proto3" json:"receiver,omitempty"`
	// 可覆盖外层消息中 templateVariables 变量
	PersonalizedVariables map[string]string `protobuf:"bytes,2,rep,name=personalizedVariables,proto3" json:"personalizedVariables,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *Recipient) Reset() {
	*x = Recipient{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Recipient) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Recipient) ProtoMessage() {}

func (x *Recipient) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Recipient.ProtoReflect.Descriptor instead.
func (*Recipient) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{13}
}

func (x *Recipient) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

func (x *Recipient) GetPersonalizedVariables() map[string]string {
	if x != nil {
		return x.PersonalizedVariables
	}
	return nil
}

type SendResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	TaskID        string                 `protobuf:"bytes,3,opt,name=taskID,proto3" json:"taskID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendResponse) Reset() {
	*x = SendResponse{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendResponse) ProtoMessage() {}

func (x *SendResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendResponse.ProtoReflect.Descriptor instead.
func (*SendResponse) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{14}
}

func (x *SendResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SendResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SendResponse) GetTaskID() string {
	if x != nil {
		return x.TaskID
	}
	return ""
}

type CheckTaskStateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskID        string                 `protobuf:"bytes,1,opt,name=taskID,proto3" json:"taskID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckTaskStateRequest) Reset() {
	*x = CheckTaskStateRequest{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckTaskStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTaskStateRequest) ProtoMessage() {}

func (x *CheckTaskStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTaskStateRequest.ProtoReflect.Descriptor instead.
func (*CheckTaskStateRequest) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{15}
}

func (x *CheckTaskStateRequest) GetTaskID() string {
	if x != nil {
		return x.TaskID
	}
	return ""
}

type CheckTaskStateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	TaskID        string                 `protobuf:"bytes,3,opt,name=taskID,proto3" json:"taskID,omitempty"`
	State         string                 `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckTaskStateResponse) Reset() {
	*x = CheckTaskStateResponse{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckTaskStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTaskStateResponse) ProtoMessage() {}

func (x *CheckTaskStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTaskStateResponse.ProtoReflect.Descriptor instead.
func (*CheckTaskStateResponse) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{16}
}

func (x *CheckTaskStateResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckTaskStateResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CheckTaskStateResponse) GetTaskID() string {
	if x != nil {
		return x.TaskID
	}
	return ""
}

func (x *CheckTaskStateResponse) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

type RetryTaskRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskID        string                 `protobuf:"bytes,1,opt,name=taskID,proto3" json:"taskID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RetryTaskRequest) Reset() {
	*x = RetryTaskRequest{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RetryTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryTaskRequest) ProtoMessage() {}

func (x *RetryTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryTaskRequest.ProtoReflect.Descriptor instead.
func (*RetryTaskRequest) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{17}
}

func (x *RetryTaskRequest) GetTaskID() string {
	if x != nil {
		return x.TaskID
	}
	return ""
}

type RetryTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RetryTaskResponse) Reset() {
	*x = RetryTaskResponse{}
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RetryTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryTaskResponse) ProtoMessage() {}

func (x *RetryTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ces_messaging_messaging_messaging_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryTaskResponse.ProtoReflect.Descriptor instead.
func (*RetryTaskResponse) Descriptor() ([]byte, []int) {
	return file_ces_messaging_messaging_messaging_proto_rawDescGZIP(), []int{18}
}

func (x *RetryTaskResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RetryTaskResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_ces_messaging_messaging_messaging_proto protoreflect.FileDescriptor

const file_ces_messaging_messaging_messaging_proto_rawDesc = "" +
	"\n" +
	"'ces/messaging/messaging/messaging.proto\x12\x17ces.messaging.messaging\"T\n" +
	"\x10TemplateAbstract\x12\x1e\n" +
	"\n" +
	"templateID\x18\x01 \x01(\tR\n" +
	"templateID\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\"\x91\x02\n" +
	"\x15CreateTemplateRequest\x12\x1e\n" +
	"\n" +
	"templateID\x18\x01 \x01(\tR\n" +
	"templateID\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12(\n" +
	"\x0ftemplateSubject\x18\x03 \x01(\tR\x0ftemplateSubject\x12\"\n" +
	"\ftemplateBody\x18\x04 \x01(\tR\ftemplateBody\x12\"\n" +
	"\fvariableKeys\x18\x05 \x03(\tR\fvariableKeys\x12 \n" +
	"\vmessageType\x18\x06 \x03(\tR\vmessageType\x12\"\n" +
	"\fbussinessTag\x18\a \x03(\tR\fbussinessTag\"f\n" +
	"\x16CreateTemplateResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1e\n" +
	"\n" +
	"templateID\x18\x03 \x01(\tR\n" +
	"templateID\"\x91\x02\n" +
	"\x15UpdateTemplateRequest\x12\x1e\n" +
	"\n" +
	"templateID\x18\x01 \x01(\tR\n" +
	"templateID\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12(\n" +
	"\x0ftemplateSubject\x18\x03 \x01(\tR\x0ftemplateSubject\x12\"\n" +
	"\ftemplateBody\x18\x04 \x01(\tR\ftemplateBody\x12\"\n" +
	"\fvariableKeys\x18\x05 \x03(\tR\fvariableKeys\x12 \n" +
	"\vmessageType\x18\x06 \x03(\tR\vmessageType\x12\"\n" +
	"\fbussinessTag\x18\a \x03(\tR\fbussinessTag\"F\n" +
	"\x16UpdateTemplateResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"7\n" +
	"\x15DeleteTemplateRequest\x12\x1e\n" +
	"\n" +
	"templateID\x18\x01 \x01(\tR\n" +
	"templateID\"F\n" +
	"\x16DeleteTemplateResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"4\n" +
	"\x12GetTemplateRequest\x12\x1e\n" +
	"\n" +
	"templateID\x18\x01 \x01(\tR\n" +
	"templateID\"\xfd\x02\n" +
	"\x13GetTemplateResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1e\n" +
	"\n" +
	"templateID\x18\x03 \x01(\tR\n" +
	"templateID\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12(\n" +
	"\x0ftemplateSubject\x18\x05 \x01(\tR\x0ftemplateSubject\x12\"\n" +
	"\ftemplateBody\x18\x06 \x01(\tR\ftemplateBody\x12\"\n" +
	"\fvariableKeys\x18\a \x03(\tR\fvariableKeys\x12 \n" +
	"\vmessageType\x18\b \x03(\tR\vmessageType\x12\"\n" +
	"\fbussinessTag\x18\t \x03(\tR\fbussinessTag\x12\x1e\n" +
	"\n" +
	"createTime\x18\n" +
	" \x01(\tR\n" +
	"createTime\x12\x1e\n" +
	"\n" +
	"updateTime\x18\v \x01(\tR\n" +
	"updateTime\"D\n" +
	"\x14ListTemplatesRequest\x12\x16\n" +
	"\x06Offset\x18\x01 \x01(\rR\x06Offset\x12\x14\n" +
	"\x05Limit\x18\x02 \x01(\rR\x05Limit\"\xb4\x01\n" +
	"\x15ListTemplatesResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12W\n" +
	"\x11templateAbstracts\x18\x03 \x03(\v2).ces.messaging.messaging.TemplateAbstractR\x11templateAbstracts\x12\x14\n" +
	"\x05total\x18\x04 \x01(\rR\x05total\"\x9c\x02\n" +
	"\vSendRequest\x12\x1e\n" +
	"\n" +
	"templateID\x18\x01 \x01(\tR\n" +
	"templateID\x12 \n" +
	"\vmessageType\x18\x02 \x01(\tR\vmessageType\x12\x1a\n" +
	"\breceiver\x18\x03 \x01(\tR\breceiver\x12i\n" +
	"\x11templateVariables\x18\x04 \x03(\v2;.ces.messaging.messaging.SendRequest.TemplateVariablesEntryR\x11templateVariables\x1aD\n" +
	"\x16TemplateVariablesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xce\x02\n" +
	"\x10BatchSendRequest\x12\x1e\n" +
	"\n" +
	"templateID\x18\x01 \x01(\tR\n" +
	"templateID\x12 \n" +
	"\vmessageType\x18\x02 \x01(\tR\vmessageType\x12B\n" +
	"\n" +
	"recipients\x18\x03 \x03(\v2\".ces.messaging.messaging.RecipientR\n" +
	"recipients\x12n\n" +
	"\x11templateVariables\x18\x04 \x03(\<EMAIL>\x11templateVariables\x1aD\n" +
	"\x16TemplateVariablesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xe6\x01\n" +
	"\tRecipient\x12\x1a\n" +
	"\breceiver\x18\x01 \x01(\tR\breceiver\x12s\n" +
	"\x15personalizedVariables\x18\x02 \x03(\v2=.ces.messaging.messaging.Recipient.PersonalizedVariablesEntryR\x15personalizedVariables\x1aH\n" +
	"\x1aPersonalizedVariablesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"T\n" +
	"\fSendResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x16\n" +
	"\x06taskID\x18\x03 \x01(\tR\x06taskID\"/\n" +
	"\x15CheckTaskStateRequest\x12\x16\n" +
	"\x06taskID\x18\x01 \x01(\tR\x06taskID\"t\n" +
	"\x16CheckTaskStateResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x16\n" +
	"\x06taskID\x18\x03 \x01(\tR\x06taskID\x12\x14\n" +
	"\x05state\x18\x04 \x01(\tR\x05state\"*\n" +
	"\x10RetryTaskRequest\x12\x16\n" +
	"\x06taskID\x18\x01 \x01(\tR\x06taskID\"A\n" +
	"\x11RetryTaskResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage*3\n" +
	"\vMessageType\x12\a\n" +
	"\x03SMS\x10\x00\x12\t\n" +
	"\x05EMAIL\x10\x01\x12\x10\n" +
	"\fNOTIFICATION\x10\x022\xc6\x01\n" +
	"\x10MessagingService\x12S\n" +
	"\x04Send\x12$.ces.messaging.messaging.SendRequest\x1a%.ces.messaging.messaging.SendResponse\x12]\n" +
	"\tBatchSend\x12).ces.messaging.messaging.BatchSendRequest\x1a%.ces.messaging.messaging.SendResponse2\xc4\x04\n" +
	"\x0fTemplateService\x12q\n" +
	"\x0eCreateTemplate\x12..ces.messaging.messaging.CreateTemplateRequest\x1a/.ces.messaging.messaging.CreateTemplateResponse\x12q\n" +
	"\x0eUpdateTemplate\x12..ces.messaging.messaging.UpdateTemplateRequest\x1a/.ces.messaging.messaging.UpdateTemplateResponse\x12q\n" +
	"\x0eDeleteTemplate\x12..ces.messaging.messaging.DeleteTemplateRequest\x1a/.ces.messaging.messaging.DeleteTemplateResponse\x12h\n" +
	"\vGetTemplate\x12+.ces.messaging.messaging.GetTemplateRequest\x1a,.ces.messaging.messaging.GetTemplateResponse\x12n\n" +
	"\rListTemplates\x12-.ces.messaging.messaging.ListTemplatesRequest\x1a..ces.messaging.messaging.ListTemplatesResponse2\xe4\x01\n" +
	"\vTaskService\x12q\n" +
	"\x0eCheckTaskState\x12..ces.messaging.messaging.CheckTaskStateRequest\x1a/.ces.messaging.messaging.CheckTaskStateResponse\x12b\n" +
	"\tRetryTask\x12).ces.messaging.messaging.RetryTaskRequest\x1a*.ces.messaging.messaging.RetryTaskResponseB\x0eZ\f/messagingpbb\x06proto3"

var (
	file_ces_messaging_messaging_messaging_proto_rawDescOnce sync.Once
	file_ces_messaging_messaging_messaging_proto_rawDescData []byte
)

func file_ces_messaging_messaging_messaging_proto_rawDescGZIP() []byte {
	file_ces_messaging_messaging_messaging_proto_rawDescOnce.Do(func() {
		file_ces_messaging_messaging_messaging_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_ces_messaging_messaging_messaging_proto_rawDesc), len(file_ces_messaging_messaging_messaging_proto_rawDesc)))
	})
	return file_ces_messaging_messaging_messaging_proto_rawDescData
}

var file_ces_messaging_messaging_messaging_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_ces_messaging_messaging_messaging_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_ces_messaging_messaging_messaging_proto_goTypes = []any{
	(MessageType)(0),               // 0: ces.messaging.messaging.MessageType
	(*TemplateAbstract)(nil),       // 1: ces.messaging.messaging.TemplateAbstract
	(*CreateTemplateRequest)(nil),  // 2: ces.messaging.messaging.CreateTemplateRequest
	(*CreateTemplateResponse)(nil), // 3: ces.messaging.messaging.CreateTemplateResponse
	(*UpdateTemplateRequest)(nil),  // 4: ces.messaging.messaging.UpdateTemplateRequest
	(*UpdateTemplateResponse)(nil), // 5: ces.messaging.messaging.UpdateTemplateResponse
	(*DeleteTemplateRequest)(nil),  // 6: ces.messaging.messaging.DeleteTemplateRequest
	(*DeleteTemplateResponse)(nil), // 7: ces.messaging.messaging.DeleteTemplateResponse
	(*GetTemplateRequest)(nil),     // 8: ces.messaging.messaging.GetTemplateRequest
	(*GetTemplateResponse)(nil),    // 9: ces.messaging.messaging.GetTemplateResponse
	(*ListTemplatesRequest)(nil),   // 10: ces.messaging.messaging.ListTemplatesRequest
	(*ListTemplatesResponse)(nil),  // 11: ces.messaging.messaging.ListTemplatesResponse
	(*SendRequest)(nil),            // 12: ces.messaging.messaging.SendRequest
	(*BatchSendRequest)(nil),       // 13: ces.messaging.messaging.BatchSendRequest
	(*Recipient)(nil),              // 14: ces.messaging.messaging.Recipient
	(*SendResponse)(nil),           // 15: ces.messaging.messaging.SendResponse
	(*CheckTaskStateRequest)(nil),  // 16: ces.messaging.messaging.CheckTaskStateRequest
	(*CheckTaskStateResponse)(nil), // 17: ces.messaging.messaging.CheckTaskStateResponse
	(*RetryTaskRequest)(nil),       // 18: ces.messaging.messaging.RetryTaskRequest
	(*RetryTaskResponse)(nil),      // 19: ces.messaging.messaging.RetryTaskResponse
	nil,                            // 20: ces.messaging.messaging.SendRequest.TemplateVariablesEntry
	nil,                            // 21: ces.messaging.messaging.BatchSendRequest.TemplateVariablesEntry
	nil,                            // 22: ces.messaging.messaging.Recipient.PersonalizedVariablesEntry
}
var file_ces_messaging_messaging_messaging_proto_depIdxs = []int32{
	1,  // 0: ces.messaging.messaging.ListTemplatesResponse.templateAbstracts:type_name -> ces.messaging.messaging.TemplateAbstract
	20, // 1: ces.messaging.messaging.SendRequest.templateVariables:type_name -> ces.messaging.messaging.SendRequest.TemplateVariablesEntry
	14, // 2: ces.messaging.messaging.BatchSendRequest.recipients:type_name -> ces.messaging.messaging.Recipient
	21, // 3: ces.messaging.messaging.BatchSendRequest.templateVariables:type_name -> ces.messaging.messaging.BatchSendRequest.TemplateVariablesEntry
	22, // 4: ces.messaging.messaging.Recipient.personalizedVariables:type_name -> ces.messaging.messaging.Recipient.PersonalizedVariablesEntry
	12, // 5: ces.messaging.messaging.MessagingService.Send:input_type -> ces.messaging.messaging.SendRequest
	13, // 6: ces.messaging.messaging.MessagingService.BatchSend:input_type -> ces.messaging.messaging.BatchSendRequest
	2,  // 7: ces.messaging.messaging.TemplateService.CreateTemplate:input_type -> ces.messaging.messaging.CreateTemplateRequest
	4,  // 8: ces.messaging.messaging.TemplateService.UpdateTemplate:input_type -> ces.messaging.messaging.UpdateTemplateRequest
	6,  // 9: ces.messaging.messaging.TemplateService.DeleteTemplate:input_type -> ces.messaging.messaging.DeleteTemplateRequest
	8,  // 10: ces.messaging.messaging.TemplateService.GetTemplate:input_type -> ces.messaging.messaging.GetTemplateRequest
	10, // 11: ces.messaging.messaging.TemplateService.ListTemplates:input_type -> ces.messaging.messaging.ListTemplatesRequest
	16, // 12: ces.messaging.messaging.TaskService.CheckTaskState:input_type -> ces.messaging.messaging.CheckTaskStateRequest
	18, // 13: ces.messaging.messaging.TaskService.RetryTask:input_type -> ces.messaging.messaging.RetryTaskRequest
	15, // 14: ces.messaging.messaging.MessagingService.Send:output_type -> ces.messaging.messaging.SendResponse
	15, // 15: ces.messaging.messaging.MessagingService.BatchSend:output_type -> ces.messaging.messaging.SendResponse
	3,  // 16: ces.messaging.messaging.TemplateService.CreateTemplate:output_type -> ces.messaging.messaging.CreateTemplateResponse
	5,  // 17: ces.messaging.messaging.TemplateService.UpdateTemplate:output_type -> ces.messaging.messaging.UpdateTemplateResponse
	7,  // 18: ces.messaging.messaging.TemplateService.DeleteTemplate:output_type -> ces.messaging.messaging.DeleteTemplateResponse
	9,  // 19: ces.messaging.messaging.TemplateService.GetTemplate:output_type -> ces.messaging.messaging.GetTemplateResponse
	11, // 20: ces.messaging.messaging.TemplateService.ListTemplates:output_type -> ces.messaging.messaging.ListTemplatesResponse
	17, // 21: ces.messaging.messaging.TaskService.CheckTaskState:output_type -> ces.messaging.messaging.CheckTaskStateResponse
	19, // 22: ces.messaging.messaging.TaskService.RetryTask:output_type -> ces.messaging.messaging.RetryTaskResponse
	14, // [14:23] is the sub-list for method output_type
	5,  // [5:14] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_ces_messaging_messaging_messaging_proto_init() }
func file_ces_messaging_messaging_messaging_proto_init() {
	if File_ces_messaging_messaging_messaging_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_ces_messaging_messaging_messaging_proto_rawDesc), len(file_ces_messaging_messaging_messaging_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   3,
		},
		GoTypes:           file_ces_messaging_messaging_messaging_proto_goTypes,
		DependencyIndexes: file_ces_messaging_messaging_messaging_proto_depIdxs,
		EnumInfos:         file_ces_messaging_messaging_messaging_proto_enumTypes,
		MessageInfos:      file_ces_messaging_messaging_messaging_proto_msgTypes,
	}.Build()
	File_ces_messaging_messaging_messaging_proto = out.File
	file_ces_messaging_messaging_messaging_proto_goTypes = nil
	file_ces_messaging_messaging_messaging_proto_depIdxs = nil
}
