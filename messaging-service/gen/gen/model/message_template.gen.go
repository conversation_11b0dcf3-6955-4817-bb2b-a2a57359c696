// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameMessageTemplate = "message_template"

// MessageTemplate mapped from table <message_template>
type MessageTemplate struct {
	ID                 string    `gorm:"column:id;type:varchar(128);primaryKey" json:"id"`
	TemplateSubject    string    `gorm:"column:template_subject;type:text;not null" json:"template_subject"`
	TempateDescription string    `gorm:"column:tempate_description;type:text;not null" json:"tempate_description"`
	Body               string    `gorm:"column:body;type:text;not null" json:"body"`
	Variables          string    `gorm:"column:variables;type:text;not null" json:"variables"`
	MessageType        string    `gorm:"column:message_type;type:varchar(128);not null" json:"message_type"`
	BussinessTag       string    `gorm:"column:bussiness_tag;type:varchar(128);not null" json:"bussiness_tag"`
	CreatedAt          time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;autoCreateTime" json:"created_at"`
	UpdatedAt          time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;autoUpdateTime" json:"updated_at"`
}

// TableName MessageTemplate's table name
func (*MessageTemplate) TableName() string {
	return TableNameMessageTemplate
}
