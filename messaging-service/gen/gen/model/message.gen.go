// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameMessage = "message"

// Message mapped from table <message>
type Message struct {
	ID            string    `gorm:"column:id;type:varchar(128);primaryKey" json:"id"`
	TemplateID    string    `gorm:"column:template_id;type:varchar(128);not null" json:"template_id"`
	MessageType   string    `gorm:"column:message_type;type:varchar(128);not null" json:"message_type"`
	Variables     string    `gorm:"column:variables;type:text;not null" json:"variables"`
	Receiver      string    `gorm:"column:receiver;type:text;not null" json:"receiver"`
	TaskID        string    `gorm:"column:task_id;type:varchar(128);not null" json:"task_id"`
	CreatedAt     time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;autoUpdateTime" json:"updated_at"`
	MessageStatus string    `gorm:"column:message_status;type:varchar(128);not null" json:"message_status"`
}

// TableName Message's table name
func (*Message) TableName() string {
	return TableNameMessage
}
