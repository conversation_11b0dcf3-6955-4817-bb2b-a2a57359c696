// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"ces-services/messaging-service/gen/gen/model"
)

func newMessageTemplate(db *gorm.DB, opts ...gen.DOOption) messageTemplate {
	_messageTemplate := messageTemplate{}

	_messageTemplate.messageTemplateDo.UseDB(db, opts...)
	_messageTemplate.messageTemplateDo.UseModel(&model.MessageTemplate{})

	tableName := _messageTemplate.messageTemplateDo.TableName()
	_messageTemplate.ALL = field.NewAsterisk(tableName)
	_messageTemplate.ID = field.NewString(tableName, "id")
	_messageTemplate.TemplateSubject = field.NewString(tableName, "template_subject")
	_messageTemplate.TempateDescription = field.NewString(tableName, "tempate_description")
	_messageTemplate.Body = field.NewString(tableName, "body")
	_messageTemplate.Variables = field.NewString(tableName, "variables")
	_messageTemplate.MessageType = field.NewString(tableName, "message_type")
	_messageTemplate.BussinessTag = field.NewString(tableName, "bussiness_tag")
	_messageTemplate.CreatedAt = field.NewTime(tableName, "created_at")
	_messageTemplate.UpdatedAt = field.NewTime(tableName, "updated_at")

	_messageTemplate.fillFieldMap()

	return _messageTemplate
}

type messageTemplate struct {
	messageTemplateDo messageTemplateDo

	ALL                field.Asterisk
	ID                 field.String
	TemplateSubject    field.String
	TempateDescription field.String
	Body               field.String
	Variables          field.String
	MessageType        field.String
	BussinessTag       field.String
	CreatedAt          field.Time
	UpdatedAt          field.Time

	fieldMap map[string]field.Expr
}

func (m messageTemplate) Table(newTableName string) *messageTemplate {
	m.messageTemplateDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m messageTemplate) As(alias string) *messageTemplate {
	m.messageTemplateDo.DO = *(m.messageTemplateDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *messageTemplate) updateTableName(table string) *messageTemplate {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewString(table, "id")
	m.TemplateSubject = field.NewString(table, "template_subject")
	m.TempateDescription = field.NewString(table, "tempate_description")
	m.Body = field.NewString(table, "body")
	m.Variables = field.NewString(table, "variables")
	m.MessageType = field.NewString(table, "message_type")
	m.BussinessTag = field.NewString(table, "bussiness_tag")
	m.CreatedAt = field.NewTime(table, "created_at")
	m.UpdatedAt = field.NewTime(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *messageTemplate) WithContext(ctx context.Context) IMessageTemplateDo {
	return m.messageTemplateDo.WithContext(ctx)
}

func (m messageTemplate) TableName() string { return m.messageTemplateDo.TableName() }

func (m messageTemplate) Alias() string { return m.messageTemplateDo.Alias() }

func (m messageTemplate) Columns(cols ...field.Expr) gen.Columns {
	return m.messageTemplateDo.Columns(cols...)
}

func (m *messageTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *messageTemplate) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 9)
	m.fieldMap["id"] = m.ID
	m.fieldMap["template_subject"] = m.TemplateSubject
	m.fieldMap["tempate_description"] = m.TempateDescription
	m.fieldMap["body"] = m.Body
	m.fieldMap["variables"] = m.Variables
	m.fieldMap["message_type"] = m.MessageType
	m.fieldMap["bussiness_tag"] = m.BussinessTag
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m messageTemplate) clone(db *gorm.DB) messageTemplate {
	m.messageTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m messageTemplate) replaceDB(db *gorm.DB) messageTemplate {
	m.messageTemplateDo.ReplaceDB(db)
	return m
}

type messageTemplateDo struct{ gen.DO }

type IMessageTemplateDo interface {
	gen.SubQuery
	Debug() IMessageTemplateDo
	WithContext(ctx context.Context) IMessageTemplateDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMessageTemplateDo
	WriteDB() IMessageTemplateDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMessageTemplateDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMessageTemplateDo
	Not(conds ...gen.Condition) IMessageTemplateDo
	Or(conds ...gen.Condition) IMessageTemplateDo
	Select(conds ...field.Expr) IMessageTemplateDo
	Where(conds ...gen.Condition) IMessageTemplateDo
	Order(conds ...field.Expr) IMessageTemplateDo
	Distinct(cols ...field.Expr) IMessageTemplateDo
	Omit(cols ...field.Expr) IMessageTemplateDo
	Join(table schema.Tabler, on ...field.Expr) IMessageTemplateDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMessageTemplateDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMessageTemplateDo
	Group(cols ...field.Expr) IMessageTemplateDo
	Having(conds ...gen.Condition) IMessageTemplateDo
	Limit(limit int) IMessageTemplateDo
	Offset(offset int) IMessageTemplateDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMessageTemplateDo
	Unscoped() IMessageTemplateDo
	Create(values ...*model.MessageTemplate) error
	CreateInBatches(values []*model.MessageTemplate, batchSize int) error
	Save(values ...*model.MessageTemplate) error
	First() (*model.MessageTemplate, error)
	Take() (*model.MessageTemplate, error)
	Last() (*model.MessageTemplate, error)
	Find() ([]*model.MessageTemplate, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MessageTemplate, err error)
	FindInBatches(result *[]*model.MessageTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MessageTemplate) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMessageTemplateDo
	Assign(attrs ...field.AssignExpr) IMessageTemplateDo
	Joins(fields ...field.RelationField) IMessageTemplateDo
	Preload(fields ...field.RelationField) IMessageTemplateDo
	FirstOrInit() (*model.MessageTemplate, error)
	FirstOrCreate() (*model.MessageTemplate, error)
	FindByPage(offset int, limit int) (result []*model.MessageTemplate, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMessageTemplateDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m messageTemplateDo) Debug() IMessageTemplateDo {
	return m.withDO(m.DO.Debug())
}

func (m messageTemplateDo) WithContext(ctx context.Context) IMessageTemplateDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m messageTemplateDo) ReadDB() IMessageTemplateDo {
	return m.Clauses(dbresolver.Read)
}

func (m messageTemplateDo) WriteDB() IMessageTemplateDo {
	return m.Clauses(dbresolver.Write)
}

func (m messageTemplateDo) Session(config *gorm.Session) IMessageTemplateDo {
	return m.withDO(m.DO.Session(config))
}

func (m messageTemplateDo) Clauses(conds ...clause.Expression) IMessageTemplateDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m messageTemplateDo) Returning(value interface{}, columns ...string) IMessageTemplateDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m messageTemplateDo) Not(conds ...gen.Condition) IMessageTemplateDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m messageTemplateDo) Or(conds ...gen.Condition) IMessageTemplateDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m messageTemplateDo) Select(conds ...field.Expr) IMessageTemplateDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m messageTemplateDo) Where(conds ...gen.Condition) IMessageTemplateDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m messageTemplateDo) Order(conds ...field.Expr) IMessageTemplateDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m messageTemplateDo) Distinct(cols ...field.Expr) IMessageTemplateDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m messageTemplateDo) Omit(cols ...field.Expr) IMessageTemplateDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m messageTemplateDo) Join(table schema.Tabler, on ...field.Expr) IMessageTemplateDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m messageTemplateDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMessageTemplateDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m messageTemplateDo) RightJoin(table schema.Tabler, on ...field.Expr) IMessageTemplateDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m messageTemplateDo) Group(cols ...field.Expr) IMessageTemplateDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m messageTemplateDo) Having(conds ...gen.Condition) IMessageTemplateDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m messageTemplateDo) Limit(limit int) IMessageTemplateDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m messageTemplateDo) Offset(offset int) IMessageTemplateDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m messageTemplateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMessageTemplateDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m messageTemplateDo) Unscoped() IMessageTemplateDo {
	return m.withDO(m.DO.Unscoped())
}

func (m messageTemplateDo) Create(values ...*model.MessageTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m messageTemplateDo) CreateInBatches(values []*model.MessageTemplate, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m messageTemplateDo) Save(values ...*model.MessageTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m messageTemplateDo) First() (*model.MessageTemplate, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MessageTemplate), nil
	}
}

func (m messageTemplateDo) Take() (*model.MessageTemplate, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MessageTemplate), nil
	}
}

func (m messageTemplateDo) Last() (*model.MessageTemplate, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MessageTemplate), nil
	}
}

func (m messageTemplateDo) Find() ([]*model.MessageTemplate, error) {
	result, err := m.DO.Find()
	return result.([]*model.MessageTemplate), err
}

func (m messageTemplateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MessageTemplate, err error) {
	buf := make([]*model.MessageTemplate, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m messageTemplateDo) FindInBatches(result *[]*model.MessageTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m messageTemplateDo) Attrs(attrs ...field.AssignExpr) IMessageTemplateDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m messageTemplateDo) Assign(attrs ...field.AssignExpr) IMessageTemplateDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m messageTemplateDo) Joins(fields ...field.RelationField) IMessageTemplateDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m messageTemplateDo) Preload(fields ...field.RelationField) IMessageTemplateDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m messageTemplateDo) FirstOrInit() (*model.MessageTemplate, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MessageTemplate), nil
	}
}

func (m messageTemplateDo) FirstOrCreate() (*model.MessageTemplate, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MessageTemplate), nil
	}
}

func (m messageTemplateDo) FindByPage(offset int, limit int) (result []*model.MessageTemplate, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m messageTemplateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m messageTemplateDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m messageTemplateDo) Delete(models ...*model.MessageTemplate) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *messageTemplateDo) withDO(do gen.Dao) *messageTemplateDo {
	m.DO = *do.(*gen.DO)
	return m
}
