syntax = "proto3";

package ces.mq.mq;
option go_package = "/mqpb";

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/api/field_behavior.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

service MessageQueueService {

  // 获取n条topic下的消息
  rpc GetSomeMessage(GetSomeMessageRequest) returns (GetSomeMessageResponse);

  // 建立消息流
  rpc GetMessageStream (GetMessageStreamRequest) returns (stream MessageEntity);
  
  // 生产一条消息
  rpc ProduceMessage(ProduceMessageRequest) returns (ProduceMessageResponse);

  // 生产n条消息
  rpc ProduceBatchMessages(ProduceBatchMessagesRequest) returns (ProduceBatchMessagesResponse);
  
}

message MessageEntity {
  string topic = 1;
  bytes msg_body = 2;
  string tag = 3;
}

message GetSomeMessageRequest {
  uint32 n = 1;
  string topic = 2;
  string tag = 3;
  uint32 timeout_second = 4;
}

message GetSomeMessageResponse {
  uint32 result_length = 1;
  repeated MessageEntity messages = 2;
}

message GetMessageStreamRequest {
  string topic = 1;
  string tag = 2;
}

message ProduceMessageRequest {
  MessageEntity msg = 1;
}

message ProduceMessageResponse {
  bool success = 1;
}

message ProduceBatchMessagesRequest {
  uint32 batch_length = 1;
  repeated MessageEntity message_batch = 2;
}

message ProduceBatchMessagesResponse {
  uint32 total = 1;
  uint32 success = 2;
}