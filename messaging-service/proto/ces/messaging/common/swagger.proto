syntax = "proto3";

package ces.messaging.common;
option go_package = "/commonpb";

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "消息中台服务";
    version: "1.0.0";
  };
  external_docs: {
    description: "注意: 200 响应中不包含code msg 等统一返回值, 仅代表data";
    url: "none";
  };
  schemes: HTTP;
  schemes: HTTPS;
  security_definitions: {
    security: {
      key: "ApiKeyAuth";
      value: {
        type: TYPE_API_KEY;
        in: IN_HEADER;
        name: "Authorization";
      }
    }
  };
  security: {
    security_requirement: {
      key: "ApiKeyAuth";
      value: {};
    }
  };
};