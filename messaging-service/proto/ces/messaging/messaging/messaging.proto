syntax="proto3";

package ces.messaging.messaging;

option go_package = "/messagingpb";


enum MessageType {
    // 短信
    SMS = 0;
    // 邮件
    EMAIL = 1;
    // 站内信
    NOTIFICATION = 2;
}

service MessagingService {
// 只发单条
rpc Send(SendRequest) returns (SendResponse);
// 多条发送
rpc BatchSend(BatchSendRequest) returns (SendResponse);

}

service TemplateService {
    rpc CreateTemplate(CreateTemplateRequest) returns (CreateTemplateResponse);
    rpc UpdateTemplate(UpdateTemplateRequest) returns (UpdateTemplateResponse);
    rpc DeleteTemplate(DeleteTemplateRequest) returns (DeleteTemplateResponse);
    rpc GetTemplate(GetTemplateRequest) returns (GetTemplateResponse);
    rpc ListTemplates(ListTemplatesRequest) returns (ListTemplatesResponse);
}

service TaskService {
    rpc CheckTaskState(CheckTaskStateRequest) returns (CheckTaskStateResponse);
    rpc RetryTask(RetryTaskRequest) returns (RetryTaskResponse);
}

message TemplateAbstract {
    string templateID = 1;
    string description = 2;
}

message CreateTemplateRequest {
    string templateID = 1;
    string description = 2;
    string templateSubject = 3;
    string templateBody = 4;
    repeated string variableKeys = 5;
    repeated string messageType = 6;
    repeated string bussinessTag = 7;
}

message CreateTemplateResponse {
    uint32 code = 1;
    string message = 2;
    string templateID = 3;
}

message UpdateTemplateRequest {
    string templateID = 1;
    string description = 2;
    string templateSubject = 3;
    string templateBody = 4;
    repeated string variableKeys = 5;
    repeated string messageType = 6;
    repeated string bussinessTag = 7;
}

message UpdateTemplateResponse {
    uint32 code = 1;
    string message = 2;
}

message DeleteTemplateRequest {
    string templateID = 1;
}

message DeleteTemplateResponse {
    uint32 code = 1;
    string message = 2;
}

message GetTemplateRequest {
    string templateID = 1;
}

message GetTemplateResponse {
    uint32 code = 1;
    string message = 2;
    string templateID = 3;
    string description = 4;
    string templateSubject = 5;
    string templateBody = 6;
    repeated string variableKeys = 7;
    repeated string messageType = 8;
    repeated string bussinessTag = 9;
    string createTime = 10;
    string updateTime = 11;
}

message ListTemplatesRequest {
    uint32 Offset = 1;
    uint32 Limit = 2;
}

message ListTemplatesResponse {
    uint32 code = 1;
    string message = 2;
    repeated TemplateAbstract templateAbstracts = 3;
    uint32 total = 4;
}

message SendRequest {
    // 模板 code 对应管理员创建的模板
    string templateID = 1;
    string messageType = 2;
    // 接收方
    string receiver = 3;
    // 模板变量数据，key为模板中的变量名，value为替换的值   
    map<string, string> templateVariables = 4;
}

message BatchSendRequest {
    // 模板 code 对应管理员创建的模板
    string templateID = 1;
    string messageType = 2;
    // 接收方
    repeated Recipient recipients = 3;
     // 模板变量数据，key为模板中的变量名，value为替换的值   
    map<string, string> templateVariables = 4;
}

message Recipient {
    string receiver = 1;
    // 可覆盖外层消息中 templateVariables 变量
    map<string, string> personalizedVariables = 2;
}

message SendResponse {
    uint32 code = 1;
    string message = 2;
    string taskID = 3;
}

message CheckTaskStateRequest {
    string taskID = 1;
}

message CheckTaskStateResponse {
    uint32 code = 1;
    string message = 2;
    string taskID = 3;
    string state = 4;
}

message RetryTaskRequest {
    string taskID = 1;
}

message RetryTaskResponse {
    uint32 code = 1;
    string message = 2;
}