package main

import (
	"ces-services/messaging-service/internal/infra/queue"
	"ces-services/messaging-service/internal/infra/sender"
)

type Application struct {
	consumer *queue.InfraMessageConsumer
}

func NewApplication(
	consumer *queue.InfraMessageConsumer,
	emailSender *sender.EmailSender,
) *Application {

	consumer.RegisterMessageHandler(
		"email",
		sender.CreateEmailMessageHandlerFunc(emailSender),
	)

	return &Application{
		consumer: consumer,
	}

}


func (app *Application) Wait() {
	app.consumer.Wait()
}