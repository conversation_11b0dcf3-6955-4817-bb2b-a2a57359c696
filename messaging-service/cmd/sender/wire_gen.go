// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"ces-services/messaging-service/cmd/sender/config"
	"ces-services/messaging-service/internal/infra"
	"ces-services/messaging-service/internal/infra/message"
	"ces-services/messaging-service/internal/infra/queue"
	"ces-services/messaging-service/internal/infra/sender"
	"ces-services/messaging-service/internal/infra/task"
	"ces-services/messaging-service/internal/infra/template"
	"context"
)

// Injectors from wire.go:

func InitializeMessageConsumer(ctx context.Context, configPath2 string) (*Application, func()) {
	configConfig := config.MustLoad(configPath2)
	messsageQueueConfig := config.ProvideMessageQueueConfig(configConfig)
	messageQueueServiceClient := queue.NewMessageQueueClient(messsageQueueConfig)
	infraMessageConsumer := queue.NewInfraMessageConsumer(messageQueueServiceClient)
	emailSenderCfg := config.ProvideEmailSenderConfig(configConfig)
	repoConfig := config.ProvideRepoConfig(configConfig)
	db := infra.CreateGormDB(repoConfig)
	templateDatabaseRepo := template.CreateTemplateDatabaseRepo(db)
	messageRepo := message.CreateMessageRepo(db)
	taskRepo := task.CreateTaskRepo(db)
	emailSender := sender.NewEmailSender(emailSenderCfg, templateDatabaseRepo, messageRepo, taskRepo)
	application := NewApplication(infraMessageConsumer, emailSender)
	return application, func() {
	}
}
