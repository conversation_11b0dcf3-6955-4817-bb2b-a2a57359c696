//go:build wireinject

package main

import (
	"ces-services/messaging-service/cmd/sender/config"
	"ces-services/messaging-service/internal/domain"
	"ces-services/messaging-service/internal/infra"
	"ces-services/messaging-service/internal/infra/message"
	"ces-services/messaging-service/internal/infra/queue"
	"ces-services/messaging-service/internal/infra/sender"
	"ces-services/messaging-service/internal/infra/task"
	"ces-services/messaging-service/internal/infra/template"
	"context"

	"github.com/google/wire"
)

func InitializeMessageConsumer(ctx context.Context, configPath string) (*Application, func()) {
	panic(wire.Build(
		config.MustLoad,
		config.ProvideRepoConfig,
		config.ProvideEmailSenderConfig,
		config.ProvideMessageQueueConfig,
		queue.NewMessageQueueClient,
		infra.CreateGormDB,
		template.CreateTemplateDatabaseRepo,
		wire.Bind(new(domain.TemplateRepo), new(*template.TemplateDatabaseRepo)),
		task.CreateTaskRepo,
		message.CreateMessageRepo,
		queue.NewInfraMessageConsumer,
		sender.NewEmailSender,
		NewApplication,
	))
}
