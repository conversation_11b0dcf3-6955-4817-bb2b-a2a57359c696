package config

import (
	"ces-services/messaging-service/internal/domain"
	"cnb.cool/cymirror/ces-services/common/conf"
	"cnb.cool/cymirror/ces-services/common/server/config"
)

type Config struct {
	config.GrpcServerConfig `mapstructure:",squash"`
	Database                DatabaseCfg        `mapstructure:"database"`
	Email                   EmailConfig        `mapstructure:"email"`
	MessageQueue            MessageQueueConfig `mapstructure:"mq"`
}

func MustLoad(path string) *Config {
	_, c := conf.MustLoad[Config](path)
	return c
}

func GetGrpcServerConfig(cfg *Config) *config.GrpcServerConfig {
	if cfg == nil {
		panic("grpc server config is nil")
	}
	return &cfg.GrpcServerConfig
}

func ProvideRepoConfig(cfg *Config) *domain.RepoConfig {
	return &domain.RepoConfig{
		Host:     cfg.Database.Host,
		Port:     cfg.Database.Port,
		User:     cfg.Database.User,
		Password: cfg.Database.Password,
		Database: cfg.Database.Database,
	}
}

func ProvideEmailSenderConfig(cfg *Config) *domain.EmailSenderCfg {
	return &domain.EmailSenderCfg{
		SMTPHost:      cfg.Email.SMTPHost,
		SMTPPort:      cfg.Email.SMTPPort,
		User:          cfg.Email.Username,
		Password:      cfg.Email.Password,
		SenderAddress: cfg.Email.From,
	}
}

func ProvideMessageQueueConfig(cfg *Config) *domain.MesssageQueueConfig {
	return &domain.MesssageQueueConfig{
		Host: cfg.MessageQueue.Host,
		Port: cfg.MessageQueue.Port,
	}
}
