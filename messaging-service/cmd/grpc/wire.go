//go:build wireinject

package main

import (
	"ces-services/messaging-service/cmd/grpc/config"
	"ces-services/messaging-service/internal"

	"cnb.cool/cymirror/ces-services/common/server"

	"context"

	"github.com/google/wire"
)

func InitializeApplication(ctx context.Context, configPath string) (*Application, func()) {
	panic(wire.Build(
		config.ConfigProviderSet,
		internal.InternalProviderSet,
		server.InitializeServer,
		NewApplication,
	))
}
