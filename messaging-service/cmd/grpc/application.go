package main

import (
	"context"

	"ces-services/messaging-service/cmd/grpc/config"
	messagingpb "ces-services/messaging-service/gen/proto/ces/messaging/messaging"
	"ces-services/messaging-service/internal/application/messaging"
	"ces-services/messaging-service/internal/application/task"
	"ces-services/messaging-service/internal/application/template"

	"cnb.cool/cymirror/ces-services/common/server"
	"google.golang.org/grpc"
)

type Application struct {
	Server *server.Server
}

func NewApplication(
	cfg *config.Config,
	s *server.Server,
	msgService *messaging.MessagingService,
	tmplService *template.TemplateService,
	taskService *task.TaskService,
) *Application {

	s.RegisterServer(func(s *grpc.Server) {
		messagingpb.RegisterMessagingServiceServer(s, msgService)
		messagingpb.RegisterTemplateServiceServer(s, tmplService)
		messagingpb.RegisterTaskServiceServer(s, taskService)
	})

	return &Application{
		Server: s,
	}
}

func (s *Application) Run(ctx context.Context) error {
	return s.Server.Run(ctx)
}
