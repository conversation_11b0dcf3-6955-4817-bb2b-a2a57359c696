package config

import (
	"ces-services/messaging-service/internal/domain"
	"cnb.cool/cymirror/ces-services/common/conf"
	"cnb.cool/cymirror/ces-services/common/server/config"
)

type Config struct {
	config.GrpcServerConfig `mapstructure:",squash"`
	Database                DatabaseCfg        `mapstructure:"database"`
	MessageQueue            MessageQueueConfig `mapstructure:"mq"`
}

func MustLoad(path string) *Config {
	_, c := conf.MustLoad[Config](path)
	return c
}

func GetGrpcServerConfig(cfg *Config) *config.GrpcServerConfig {
	if cfg == nil {
		panic("grpc server config is nil")
	}
	return &cfg.GrpcServerConfig
}

func ProvideRepoConfig(cfg *Config) *domain.RepoConfig {
	return &domain.RepoConfig{
		Host:     cfg.Database.Host,
		Port:     cfg.Database.Port,
		User:     cfg.Database.User,
		Password: cfg.Database.Password,
		Database: cfg.Database.Database,
	}
}

func ProvideMessageQueueConfig(cfg *Config) *domain.MesssageQueueConfig {
	return &domain.MesssageQueueConfig{
		Host: cfg.MessageQueue.Host,
		Port: cfg.MessageQueue.Port,
	}
}
