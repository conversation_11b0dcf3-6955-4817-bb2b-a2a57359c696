// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"ces-services/messaging-service/cmd/grpc/config"
	"ces-services/messaging-service/internal/application/messaging"
	task2 "ces-services/messaging-service/internal/application/task"
	template2 "ces-services/messaging-service/internal/application/template"
	"ces-services/messaging-service/internal/infra"
	"ces-services/messaging-service/internal/infra/message"
	"ces-services/messaging-service/internal/infra/queue"
	"ces-services/messaging-service/internal/infra/task"
	"ces-services/messaging-service/internal/infra/template"
	"cnb.cool/cymirror/ces-services/common/server"
	"context"
)

// Injectors from wire.go:

func InitializeApplication(ctx context.Context, configPath2 string) (*Application, func()) {
	configConfig := config.MustLoad(configPath2)
	grpcServerConfig := config.GetGrpcServerConfig(configConfig)
	serverServer, cleanup := server.InitializeServer(ctx, grpcServerConfig)
	repoConfig := config.ProvideRepoConfig(configConfig)
	db := infra.CreateGormDB(repoConfig)
	templateDatabaseRepo := template.CreateTemplateDatabaseRepo(db)
	messageRepo := message.CreateMessageRepo(db)
	taskRepo := task.CreateTaskRepo(db)
	messsageQueueConfig := config.ProvideMessageQueueConfig(configConfig)
	messageQueueServiceClient := queue.NewMessageQueueClient(messsageQueueConfig)
	messageProducer := queue.NewMessageProducer(messageQueueServiceClient)
	messagingService := messaging.CreateMessagingService(templateDatabaseRepo, messageRepo, taskRepo, messageProducer)
	templateService := template2.CreateTemplateService(templateDatabaseRepo)
	taskService := task2.CreateTaskService(taskRepo, messageRepo, messageProducer)
	application := NewApplication(configConfig, serverServer, messagingService, templateService, taskService)
	return application, func() {
		cleanup()
	}
}
