CREATE TABLE message_template (
    id VARCHAR(128) PRIMARY KEY  NOT NULL,
    template_subject TEXT NOT NULL,
    tempate_description TEXT  NOT NULL,
    body TEXT NOT NULL,
    variables TEXT NOT NULL,
    message_type VARCHAR(128) NOT NULL,
    bussiness_tag VARCHAR(128) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE task (
    id VARCHAR(128) PRIMARY KEY NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE message (
    id VARCHAR(128) PRIMARY KEY  NOT NULL,
    template_id VARCHAR(128) NOT NULL,
    message_type VARCHAR(128) NOT NULL,
    variables TEXT NOT NULL,
    receiver TEXT NOT NULL,
    task_id VARCHAR(128) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    message_status VARCHAR(128) NOT NULL,
    FOREIGN KEY (template_id) REFERENCES message_template(id),
    FOREIGN KEY (task_id) REFERENCES task(id)
);

