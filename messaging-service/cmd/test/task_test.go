package test

import (
	messagingpb "ces-services/messaging-service/gen/proto/ces/messaging/messaging"
	"context"
	"fmt"
	"testing"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

var taskID = "f5712bc2-5020-4902-93c4-11c92a4c2bca"

func TestCheckTaskStatus(t *testing.T) {
	grpcCli, err := grpc.NewClient("localhost:8081",
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)

	if err != nil {
		t.Fatalf("Failed to create gRPC client: %v", err)
	}

	serviceCli := messagingpb.NewTaskServiceClient(grpcCli)
	resp, err := serviceCli.CheckTaskState(context.Background(), &messagingpb.CheckTaskStateRequest{
		// Assuming TaskID is a string, replace with the actual type if different
		TaskID: taskID,
	})
	if err != nil {
		t.Fatalf("Failed to check task status: %v", err)
	}
	if resp == nil {
		t.Fatalf("Expected a response, got nil")
	}
	if resp.Code != 0 {
		t.Log("Response: ", resp.Message)
		t.Fatalf("Expected code 0, got %d", resp.Code)
	}
	fmt.Printf("Task status: %s\n", resp.State)
}

func TestRetryTask(t *testing.T) {
	grpcCli, err := grpc.NewClient("localhost:8081",
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)

	if err != nil {
		t.Fatalf("Failed to create gRPC client: %v", err)
	}

	serviceCli := messagingpb.NewTaskServiceClient(grpcCli)
	resp, err := serviceCli.RetryTask(context.Background(), &messagingpb.RetryTaskRequest{
		TaskID: taskID,
	})
	if err != nil {
		t.Fatalf("Failed to retry task: %v", err)
	}
	if resp == nil {
		t.Fatalf("Expected a response, got nil")
	}
	if resp.Code != 0 {
		t.Log("Response: ", resp.Message)
		t.Fatalf("Expected code 0, got %d", resp.Code)
	}
}
