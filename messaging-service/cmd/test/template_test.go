package test

import (
	messagingpb "ces-services/messaging-service/gen/proto/ces/messaging/messaging"
	"ces-services/messaging-service/internal/domain"
	"context"
	"fmt"
	"testing"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func TestCreateTemplateSuccess(t *testing.T) {
	// Create a new template
	template := domain.Template{
		ID:            "1",
		Description:   "This is a test template",
		Subject:       "Test Subject",
		Body:          "This is a test body, var1 is {{.var1}}, var2 is {{.var2}}",
		Variables:     []string{"var1", "var2"},
		MessageTypes:  []string{"email"},
		BussinessTags: []string{"tag1", "tag2"},
	}

	grpcCli, err := grpc.NewClient("localhost:8081",
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)

	if err != nil {
		t.Fatalf("Failed to create gRPC client: %v", err)
	}

	serviceCli := messagingpb.NewTemplateServiceClient(grpcCli)

	resp, err := serviceCli.CreateTemplate(context.Background(), &messagingpb.CreateTemplateRequest{
		TemplateID:      template.ID,
		Description:     template.Description,
		TemplateSubject: template.Subject,
		TemplateBody:    template.Body,
		VariableKeys:    template.Variables,
		MessageType:     template.MessageTypes,
		BussinessTag:    template.BussinessTags,
	})

	if err != nil {
		t.Fatalf("Failed to create template: %v", err)
	}

	if resp == nil {
		t.Fatalf("Expected a response, got nil")
	}

	if resp.TemplateID != template.ID {
		t.Fatalf("Expected template ID %s, got %s", template.ID, resp.TemplateID)
	}

	if resp.Code != 0 {
		t.Log("Response: ", resp.Message)
		t.Fatalf("Expected code 0, got %d", resp.Code)
	}

}

func TestGetTemplate(t *testing.T) {
	templateID := "1"

	grpcCli, err := grpc.NewClient("localhost:8081",
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)

	if err != nil {
		t.Fatalf("Failed to create gRPC client: %v", err)
	}

	serviceCli := messagingpb.NewTemplateServiceClient(grpcCli)
	resp, err := serviceCli.GetTemplate(context.Background(), &messagingpb.GetTemplateRequest{
		TemplateID: templateID,
	})
	if err != nil {
		t.Fatalf("Failed to get template: %v", err)
	}
	if resp == nil {
		t.Fatalf("Expected a response, got nil")
	}
	if resp.TemplateID != templateID {
		t.Fatalf("Expected template ID %s, got %s", templateID, resp.TemplateID)
	}
	if resp.Code != 0 {
		t.Log("Response: ", resp.Message)
		t.Fatalf("Expected code 0, got %d", resp.Code)
	}
	fmt.Println("Template ID: ", resp.TemplateID)
	fmt.Println("Description: ", resp.Description)
	fmt.Println("Subject: ", resp.TemplateSubject)

	fmt.Println("Body: ", resp.TemplateBody)
	fmt.Println("Variables: ", resp.VariableKeys)
	fmt.Println("Message Types: ", resp.MessageType)
	fmt.Println("Business Tags: ", resp.BussinessTag)
	fmt.Println("Created At: ", resp.CreateTime)
	fmt.Println("Updated At: ", resp.UpdateTime)
}

func TestUpdateTemplate(t *testing.T) {

	template := domain.Template{
		ID:            "1",
		Description:   "This is a test template",
		Subject:       "Test Subject",
		Body:          "This is a test body, var1 is {{.var1}}, var2 is {{.var2}}",
		Variables:     []string{"var1", "var2"},
		MessageTypes:  []string{"email", "sms"},
		BussinessTags: []string{"tag1", "tag2", "tag3"},
	}

	grpcCli, err := grpc.NewClient("localhost:8081",
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)

	if err != nil {
		t.Fatalf("Failed to create gRPC client: %v", err)
	}

	serviceCli := messagingpb.NewTemplateServiceClient(grpcCli)
	resp, err := serviceCli.UpdateTemplate(context.Background(), &messagingpb.UpdateTemplateRequest{
		TemplateID:      template.ID,
		Description:     template.Description,
		TemplateSubject: template.Subject,
		TemplateBody:    template.Body,
		VariableKeys:    template.Variables,
		MessageType:     template.MessageTypes,
		BussinessTag:    template.BussinessTags,
	})
	if err != nil {
		t.Fatalf("Failed to update template: %v", err)
	}
	if resp == nil {
		t.Fatalf("Expected a response, got nil")
	}
	if resp.Code != 0 {
		t.Log("Response: ", resp.Message)
		t.Fatalf("Expected code 0, got %d", resp.Code)
	}
}

func TestDeleteTemplate(t *testing.T) {
	templateID := "1"
	grpcCli, err := grpc.NewClient("localhost:8081",
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)

	if err != nil {
		t.Fatalf("Failed to create gRPC client: %v", err)
	}

	serviceCli := messagingpb.NewTemplateServiceClient(grpcCli)
	resp, err := serviceCli.DeleteTemplate(context.Background(), &messagingpb.DeleteTemplateRequest{
		TemplateID: templateID,
	})
	if err != nil {
		t.Fatalf("Failed to delete template: %v", err)
	}
	if resp == nil {
		t.Fatalf("Expected a response, got nil")
	}

	if resp.Code != 0 {
		t.Log("Response: ", resp.Message)
		t.Fatalf("Expected code 0, got %d", resp.Code)
	}
}

func TestListTemplates(t *testing.T) {
	grpcCli, err := grpc.NewClient("localhost:8081",
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)

	if err != nil {
		t.Fatalf("Failed to create gRPC client: %v", err)
	}

	serviceCli := messagingpb.NewTemplateServiceClient(grpcCli)

	resp, err := serviceCli.ListTemplates(context.Background(), &messagingpb.ListTemplatesRequest{
		Offset: 0,
		Limit:  10,
	})
	if err != nil {
		t.Fatalf("Failed to list templates: %v", err)
	}
	if resp == nil {
		t.Fatalf("Expected a response, got nil")
	}
	if resp.Code != 0 {

		t.Log("Response: ", resp.Message)
		t.Fatalf("Expected code 0, got %d", resp.Code)
	}
	for _, template := range resp.TemplateAbstracts {
		fmt.Println("Template ID: ", template.TemplateID)
		fmt.Println("Description: ", template.Description)
	}

}
