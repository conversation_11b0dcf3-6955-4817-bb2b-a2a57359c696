package test

import (
	messagingpb "ces-services/messaging-service/gen/proto/ces/messaging/messaging"
	"context"
	"fmt"
	"testing"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func TestSendEmail(t *testing.T) {

	templateID := "1"

	grpcCli, err := grpc.NewClient("localhost:8081",
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)

	if err != nil {
		t.Fatalf("Failed to create gRPC client: %v", err)
	}

	serviceCli := messagingpb.NewMessagingServiceClient(grpcCli)

	resp, err := serviceCli.Send(context.Background(), &messagingpb.SendRequest{
		MessageType: "email",
		TemplateID:  templateID,
		Receiver:    "**",
		TemplateVariables: map[string]string{
			"var1": "value1",
			"var2": "value2",
			"var3": "value3",
		},
	})
	if err != nil {
		t.Fatalf("Failed to send email: %v", err)
	}
	if resp == nil {
		t.Fatalf("Expected a response, got nil")
	}
	if resp.Code != 0 {
		t.Log("Response: ", resp.Message)
		t.Fatalf("Expected code 0, got %d", resp.Code)
	}

	fmt.Println("Send Task ID is", resp.TaskID)
}

func TestBatchSendEmail(t *testing.T) {
	templateID := "1"
	req := messagingpb.BatchSendRequest{
		MessageType: "email",
		TemplateID:  templateID,
		TemplateVariables: map[string]string{
			"var1": "value1",
		},
		Recipients: []*messagingpb.Recipient{
			{
				Receiver: "**",
				PersonalizedVariables: map[string]string{
					"var1": "value112",
					"var2": "value21",
				},
			}, {
				Receiver: "**",
				PersonalizedVariables: map[string]string{
					"var2": "value22",
				},
			},
		},
	}

	grpcCli, err := grpc.NewClient("localhost:8081",
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)

	if err != nil {
		t.Fatalf("Failed to create gRPC client: %v", err)
	}

	serviceCli := messagingpb.NewMessagingServiceClient(grpcCli)
	resp, err := serviceCli.BatchSend(context.Background(), &req)
	if err != nil {
		t.Fatalf("Failed to send email: %v", err)
	}
	if resp == nil {
		t.Fatalf("Expected a response, got nil")
	}
	if resp.Code != 0 {
		t.Log("Response: ", resp.Message)
		t.Fatalf("Expected code 0, got %d", resp.Code)
	}
	fmt.Println("Send Task ID is", resp.TaskID)
}
