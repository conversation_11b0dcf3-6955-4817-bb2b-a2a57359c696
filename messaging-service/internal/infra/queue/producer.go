package queue

import (
	"context"
	"encoding/json"
	"errors"

	mqpb "ces-services/messaging-service/gen/proto/ces/mq/mq"
	"ces-services/messaging-service/internal/domain"

	"go.uber.org/zap"
)

type MessageProducer struct {
	producerCli mqpb.MessageQueueServiceClient
}

func NewMessageProducer(producerCli mqpb.MessageQueueServiceClient) *MessageProducer {
	return &MessageProducer{
		producerCli: producerCli,
	}
}

func (p *MessageProducer) ProduceMessage(msg *domain.Message) error {
	dump, err := json.Marshal(msg)
	if err != nil {
		return errors.New("produce message " + msg.MessageID + " to queue error: " + err.Error())
	}

	resp, err := p.producerCli.ProduceMessage(
		context.Background(),
		&mqpb.ProduceMessageRequest{
			Msg: &mqpb.MessageEntity{
				Topic:   msg.MessageType,
				MsgBody: dump,
				Tag:     "",
			},
		},
	)

	if err != nil {
		return errors.New("produce message " + msg.MessageID + " to queue error: " + err.Error())
	}

	if !resp.Success {
		return errors.New("produce message " + msg.MessageID + " to queue error fail")
	}
	return nil
}

func (p *MessageProducer) ProduceMessageWithTemplate(tmpl *domain.Template, msg *domain.Message) error {

	zap.L().Warn("Not implemented")
	return nil
}
