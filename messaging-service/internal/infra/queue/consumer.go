package queue

import (
	"context"
	"encoding/json"
	"io"
	"sync"

	mqpb "ces-services/messaging-service/gen/proto/ces/mq/mq"
	"ces-services/messaging-service/internal/domain"

	"go.uber.org/zap"
	"google.golang.org/grpc"
)

type InfraMessageConsumer struct {
	topicHandlers map[string]domain.MessageHandler
	consumerPool  map[string]grpc.ServerStreamingClient[mqpb.MessageEntity]
	mqInfraClient mqpb.MessageQueueServiceClient
	cancelFuncMap map[string]func()
	mutex         *sync.Mutex
	wg            *sync.WaitGroup
}

func NewInfraMessageConsumer(mqInfraClient mqpb.MessageQueueServiceClient) *InfraMessageConsumer {
	return &InfraMessageConsumer{
		topicHandlers: make(map[string]domain.MessageHandler),
		consumerPool:  make(map[string]grpc.ServerStreamingClient[mqpb.MessageEntity]),
		mqInfraClient: mqInfraClient,
		cancelFuncMap: make(map[string]func()),
		mutex:         &sync.Mutex{},
		wg:            &sync.WaitGroup{},
	}
}

func (consumer *InfraMessageConsumer) Wait() {
	consumer.wg.Wait()
}

func (consumer *InfraMessageConsumer) Close() {
	consumer.mutex.Lock()
	defer consumer.mutex.Unlock()

	for topic, cancelFunc := range consumer.cancelFuncMap {
		cancelFunc()
		delete(consumer.cancelFuncMap, topic)
	}

	for topic, streamCli := range consumer.consumerPool {
		streamCli.CloseSend()
		delete(consumer.consumerPool, topic)
	}
}

func (consumer *InfraMessageConsumer) RegisterMessageHandler(topic string, handler domain.MessageHandler) error {

	consumer.mutex.Lock()
	defer consumer.mutex.Unlock()

	consumer.topicHandlers[topic] = handler

	ctx, cancel := context.WithCancel(context.Background())

	streamCli, err := consumer.mqInfraClient.GetMessageStream(
		ctx,
		&mqpb.GetMessageStreamRequest{
			Topic: topic,
		},
	)

	if err != nil {
		cancel()
		return err
	}

	consumer.consumerPool[topic] = streamCli
	consumer.cancelFuncMap[topic] = cancel
	consumer.wg.Add(1)
	go func(handler domain.MessageHandler) {
		defer consumer.wg.Done()
		for {
			msg, err := streamCli.Recv()
			if err != nil && err == io.EOF {
				// stream closed
				break
			}

			if err != nil {
				zap.L().Error("GetMessageStream Error", zap.String("topic", topic), zap.String("err", err.Error()))
				break
			}
			var msgDomain domain.Message
			err = json.Unmarshal(msg.MsgBody, &msgDomain)
			if err != nil {
				zap.L().Error("Unmarshal Message Error", zap.String("topic", topic), zap.String("err", err.Error()))
				continue
			}
			err = handler(&msgDomain)
			if err != nil {
				zap.L().Error("Handle Message Error", zap.String("topic", topic), zap.String("err", err.Error()))
				continue
			}
		}
		// Recreate the stream
		if _, ok := <-streamCli.Context().Done(); !ok {
			consumer.RegisterMessageHandler(topic, handler)
		}
	}(handler)

	return nil
}
