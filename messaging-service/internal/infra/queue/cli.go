package queue

import (
	mqpb "ces-services/messaging-service/gen/proto/ces/mq/mq"
	"ces-services/messaging-service/internal/domain"
	"fmt"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func NewMessageQueueClient(cfg *domain.MesssageQueueConfig) mqpb.MessageQueueServiceClient {

	grpcCli, err := grpc.NewClient(
		fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		grpc.WithTransportCredentials(
			insecure.NewCredentials(),
		),
	)

	if err != nil {
		panic(err)
	}

	cli := mqpb.NewMessageQueueServiceClient(
		grpcCli,
	)
	return cli
}
