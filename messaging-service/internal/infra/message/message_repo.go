package message

import (
	"context"
	"errors"

	"ces-services/messaging-service/gen/gen/query"
	"ces-services/messaging-service/internal/domain"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type MessageRepo struct {
	messageRepo *query.Query
}

func CreateMessageRepo(ormDB *gorm.DB) *MessageRepo {
	return &MessageRepo{
		messageRepo: query.Use(ormDB),
	}
}

func (repo *MessageRepo) CreateMessage(msg *domain.Message) error {
	// Convert domain message to model message
	modelMsg, err := domain.MessageDomain2Model(msg)
	if err != nil {
		return err
	}

	// Insert the message into the database
	err = repo.messageRepo.Message.WithContext(context.Background()).Create(modelMsg)
	if err != nil {
		return err
	}

	return nil
}

func (repo *MessageRepo) UpdateMessage(msgID string, msg *domain.Message) error {
	modelMsg, err := domain.MessageDomain2Model(msg)

	if err != nil {
		return err
	}

	_, err = repo.messageRepo.Message.WithContext(context.Background()).Where(repo.messageRepo.Message.ID.Eq(msgID)).Updates(*modelMsg)

	if err != nil {
		return err
	}

	return nil

}

func (repo *MessageRepo) GetMessage(msgID string) (*domain.Message, error) {
	msgModel, err := repo.messageRepo.Message.WithContext(context.Background()).Where(repo.messageRepo.Message.ID.Eq(msgID)).First()
	if err != nil {
		return nil, err
	}
	msg, err := domain.MessageModel2Domain(msgModel)
	if err != nil {
		return nil, err
	}
	return msg, nil
}

func (repo *MessageRepo) DeleteMessage(msgID string) error {
	_, err := repo.messageRepo.Message.WithContext(context.Background()).Where(repo.messageRepo.Message.ID.Eq(msgID)).Delete()
	if err != nil {
		return err
	}
	return nil
}

func (repo *MessageRepo) GetMessageList(offset, limit int) ([]*domain.Message, error) {
	msgModels, err := repo.messageRepo.Message.WithContext(context.Background()).Offset(offset).Limit(limit).Find()
	if err != nil {
		return nil, err
	}
	ret := make([]*domain.Message, len(msgModels))
	for i, msgModel := range msgModels {
		var err error
		ret[i], err = domain.MessageModel2Domain(msgModel)
		if err != nil {
			ret[i] = &domain.Message{}
			zap.L().Error("Trans Message From Model to Domain Error: ", zap.String("id", msgModel.ID), zap.String("err", err.Error()))
		}
	}
	return ret, nil
}

func (repo *MessageRepo) GetMessagesWithTaskID(taskID string) ([]*domain.Message, error) {
	msgModels, err := repo.messageRepo.Message.
		WithContext(context.Background()).
		Where(repo.messageRepo.Message.TaskID.Eq(taskID)).Find()

	if err != nil {
		return nil, errors.New("find message with task id " + taskID + "error: " + err.Error())
	}

	ret := make([]*domain.Message, len(msgModels))

	for i, msgModel := range msgModels {
		msg, err := domain.MessageModel2Domain(msgModel)
		if err != nil {
			zap.L().Error("Translate Message Model to Domain error", zap.String("id", msg.MessageID), zap.String("err", err.Error()))
		}
		ret[i] = msg
	}
	return ret, nil
}
