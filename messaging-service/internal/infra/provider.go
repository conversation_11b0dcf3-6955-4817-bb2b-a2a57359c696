package infra

import (
	"errors"
	"fmt"

	"ces-services/messaging-service/internal/domain"
	"ces-services/messaging-service/internal/infra/message"
	"ces-services/messaging-service/internal/infra/queue"
	"ces-services/messaging-service/internal/infra/task"
	"ces-services/messaging-service/internal/infra/template"

	"github.com/google/wire"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func CreateGormDB(cfg *domain.RepoConfig) *gorm.DB {
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
		cfg.Host, cfg.Port, cfg.User, cfg.Password, cfg.Database)
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(errors.New("failed to connect database: " + err.Error()))
	}
	return db
}

// InfraProviderSet Infrastructure providers
var InfraProviderSet = wire.NewSet(
	CreateGormDB,
	template.CreateTemplateDatabaseRepo,
	task.CreateTaskRepo,
	queue.NewMessageQueueClient,
	queue.NewMessageProducer,
	message.CreateMessageRepo,
	wire.Bind(new(domain.TemplateRepo), new(*template.TemplateDatabaseRepo)),
)
