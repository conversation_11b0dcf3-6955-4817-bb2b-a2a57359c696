package sender

import (
	"ces-services/messaging-service/internal/domain"
	"ces-services/messaging-service/internal/infra/builder"
	"ces-services/messaging-service/internal/infra/message"
	"ces-services/messaging-service/internal/infra/task"
	"crypto/tls"
	"fmt"

	"go.uber.org/zap"
	"gopkg.in/gomail.v2"
)

type EmailSender struct {
	senderAddress string
	senderName    string
	smtpHost      string
	smtpPort      int
	smtpPassword  string
	templateRepo  domain.TemplateRepo
	msgRepo       *message.MessageRepo
	taskRepo      *task.TaskRepo
	tokenBucket   chan bool
}

func NewEmailSender(cfg *domain.EmailSenderCfg, templateRepo domain.TemplateRepo, msgRepo *message.MessageRepo, taskRepo *task.TaskRepo) *EmailSender {
	fmt.Printf("EmailSenderCfg: %v", cfg)
	return &EmailSender{
		senderAddress: cfg.SenderAddress,
		senderName:    cfg.User,
		smtpHost:      cfg.SMTPHost,
		smtpPort:      cfg.SMTPPort,
		smtpPassword:  cfg.Password,
		templateRepo:  templateRepo,
		msgRepo:       msgRepo,
		taskRepo:      taskRepo,
		tokenBucket:   make(chan bool, cfg.MaxParallelism),
	}
}

func CreateEmailMessageHandlerFunc(sender *EmailSender) domain.MessageHandler {
	return func(msg *domain.Message) error {
		<-sender.tokenBucket
		defer func() {
			sender.tokenBucket <- true
		}()
		tmpl, err := sender.templateRepo.GetTemplate(msg.TemplateID)
		if err != nil {
			zap.L().Error("Get Template Error.", zap.String("id", msg.TemplateID), zap.String("err", err.Error()))
			return err
		}
		err = sender.Send(tmpl, msg)
		if err != nil {
			zap.L().Error("Send Email Error.", zap.String("id", msg.TemplateID), zap.String("err", err.Error()))
			msg.Status = domain.MessageStatusFailed
		} else {
			msg.Status = domain.MessageStatusSuccess
		}
		err = sender.msgRepo.UpdateMessage(
			msg.MessageID,
			msg,
		)

		if err != nil {
			zap.L().Error("Update Message Error.", zap.String("id", msg.MessageID), zap.String("err", err.Error()))
			return err
		}

		return err
	}
}

func (e *EmailSender) Send(template *domain.Template, message *domain.Message) error {
	em := gomail.NewMessage()
	builder := &builder.EmailMessageBuilder{}
	fmt.Printf("Send Message: %v\n", message)

	if e.senderName != "" {

		em.SetHeader("From", em.FormatAddress(e.senderAddress, e.senderName))
	} else {
		em.SetHeader("From", e.senderAddress)
	}
	em.SetHeader("To", message.Receivier)
	em.SetHeader("Subject", template.Subject)

	emailBody, err := builder.BuilderToString(template, message)
	if err != nil {
		fmt.Println("failed to build email body:", err)
		return err
	}

	em.SetBody("text/html", emailBody)
	d := gomail.NewDialer(e.smtpHost, e.smtpPort, e.senderAddress, e.smtpPassword)
	d.SSL = true
	d.TLSConfig = &tls.Config{
		ServerName: e.smtpHost,
	}
	if err := d.DialAndSend(em); err != nil {
		fmt.Println("failed to send email:", err)
		zap.L().Error("failed to send email", zap.Error(err))
		return err
	}

	return nil
}

func (e *EmailSender) BatchSend(templates []*domain.Template, messages []*domain.Message) error {

	if len(templates) != len(messages) {
		return domain.ErrTemplateAndMessageNotMatch
	}

	builder := builder.EmailMessageBuilder{}

	for i, message := range messages {
		em := gomail.NewMessage()
		em.SetHeader("From", e.senderName)
		em.SetHeader("To", message.Receivier)
		em.SetHeader("Subject", templates[i].Subject)
		emailBody, err := builder.BuilderToString(templates[i], message)
		if err != nil {
			zap.L().Error("failed to build email body", zap.Error(err))
		}
		em.SetBody("text/html", emailBody)

	}

	return nil
}
