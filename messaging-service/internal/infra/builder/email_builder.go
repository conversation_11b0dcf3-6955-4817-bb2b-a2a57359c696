package builder

import (
	"html/template"
	"strings"

	"ces-services/messaging-service/internal/domain"
)

type EmailMessageBuilder struct {
}

func (e *EmailMessageBuilder) BuilderToString(tmpl *domain.Template, msg *domain.Message) (string, error) {
	tmplObj, err := template.New("email").Parse(tmpl.Body)
	if err != nil {
		return "", err
	}
	resultBuilder := strings.Builder{}
	err = tmplObj.Execute(&resultBuilder, msg.Variables)
	if err != nil {
		return "", err
	}
	return resultBuilder.String(), nil
}
