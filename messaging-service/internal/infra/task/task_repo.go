package task

import (
	"context"

	"ces-services/messaging-service/gen/gen/query"
	"ces-services/messaging-service/internal/domain"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type TaskRepo struct {
	db *query.Query
}

func CreateTaskRepo(ormDB *gorm.DB) *TaskRepo {
	return &TaskRepo{
		db: query.Use(ormDB),
	}
}

func (repo *TaskRepo) CreateTask(task *domain.Task) error {
	// Implement the logic to create a task in the database
	// For example:
	taskModel := domain.TaskDomain2Model(task)
	err := repo.db.Task.WithContext(context.Background()).Create(taskModel)
	if err != nil {
		zap.L().Error("failed to create task:", zap.Error(err))
	}
	return err
}

func (repo *TaskRepo) UpdateTaskState(task *domain.Task) error {
	return nil
}

func (repo *TaskRepo) GetTask(taskID string) (*domain.Task, error) {
	// Implement the logic to get a task from the database
	// For example:
	taskModel, err := repo.db.Task.WithContext(context.Background()).Where(repo.db.Task.ID.Eq(taskID)).First()
	if err != nil {
		zap.L().Error("failed to get task:", zap.Error(err))
		return nil, err
	}
	return domain.TaskModel2Domain(taskModel), nil
}

func (repo *TaskRepo) DeleteTask(taskID string) error {
	// Implement the logic to delete a task from the database
	// For example:
	_, err := repo.db.Task.WithContext(context.Background()).Where(repo.db.Task.ID.Eq(taskID)).Delete()
	if err != nil {
		zap.L().Error("failed to delete task:", zap.Error(err))
	}
	return err
}

func (repo *TaskRepo) ListTasks(offset, limit int) ([]*domain.Task, error) {
	// Implement the logic to list tasks from the database
	// For example:
	tasks, err := repo.db.Task.WithContext(context.Background()).Offset(offset).Limit(limit).Find()
	if err != nil {
		zap.L().Error("failed to list tasks:", zap.Error(err))
		return nil, err
	}
	ret := make([]*domain.Task, len(tasks))
	for i, task := range tasks {
		ret[i] = domain.TaskModel2Domain(task)
	}
	return ret, nil
}
