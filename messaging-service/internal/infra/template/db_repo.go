package template

import (
	"context"

	"ces-services/messaging-service/gen/gen/query"
	"ces-services/messaging-service/internal/domain"

	"gorm.io/gorm"

	"go.uber.org/zap"
)

type TemplateDatabaseRepo struct {
	templateDB *query.Query
}

func CreateTemplateDatabaseRepo(ormDB *gorm.DB) *TemplateDatabaseRepo {
	return &TemplateDatabaseRepo{
		templateDB: query.Use(ormDB),
	}
}

func (dbRepo *TemplateDatabaseRepo) GetTemplate(id string) (*domain.Template, error) {
	ctx := context.Background()
	dbData, err := dbRepo.templateDB.MessageTemplate.WithContext(ctx).Where(dbRepo.templateDB.MessageTemplate.ID.Eq(id)).First()
	if err != nil {
		return nil, err
	}
	ret, err := domain.TemplateModel2Domain(dbData)
	if err != nil {
		return nil, err
	}
	return ret, nil
}

func (dbRepo *TemplateDatabaseRepo) UpdateTemplate(id string, template *domain.Template) error {
	ctx := context.Background()
	dbData, err := domain.TemplateDomain2Model(template)
	if err != nil {
		return err
	}

	_, err = dbRepo.templateDB.MessageTemplate.WithContext(ctx).Where(dbRepo.templateDB.MessageTemplate.ID.Eq(id)).Updates(*dbData)
	if err != nil {
		return err
	}
	return nil
}

func (dbRepo *TemplateDatabaseRepo) DeleteTemplate(id string) error {
	ctx := context.Background()
	_, err := dbRepo.templateDB.MessageTemplate.WithContext(ctx).Where(dbRepo.templateDB.MessageTemplate.ID.Eq(id)).Delete()
	if err != nil {
		return err
	}
	return nil
}

func (dbRepo *TemplateDatabaseRepo) ListTemplates(offset, limit int) ([]*domain.Template, error) {
	ctx := context.Background()
	templates, err := dbRepo.templateDB.MessageTemplate.WithContext(ctx).Offset(offset).Limit(limit).Find()
	if err != nil {
		return nil, err
	}
	ret := make([]*domain.Template, len(templates))
	for i, tmpl := range templates {
		tmplDomain, err := domain.TemplateModel2Domain(tmpl)
		if err != nil {
			ret[i] = nil
			zap.L().Error("failed to convert template model to domain", zap.String("id", tmpl.ID), zap.Error(err))
		}
		ret[i] = tmplDomain
	}
	return ret, nil
}

func (dbRepo *TemplateDatabaseRepo) CreateTemplate(template *domain.Template) error {
	ctx := context.Background()
	dbData, err := domain.TemplateDomain2Model(template)

	if err != nil {
		return err
	}

	err = dbRepo.templateDB.MessageTemplate.WithContext(ctx).Create(dbData)

	if err != nil {
		return err
	}
	return nil
}
