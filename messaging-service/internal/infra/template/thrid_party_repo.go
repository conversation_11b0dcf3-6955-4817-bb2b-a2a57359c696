package template

import "context"

type TemplateThirdPartyRepo struct {
}


func (tpRepo *TemplateThirdPartyRepo) GetTemplate(ctx context.Context, code string) (string, error) {

	return "", nil
}

func (tpRepo *TemplateThirdPartyRepo) UpdateTemplate(ctx context.Context, code, template string) error {
	return nil
}

func (tpRepo *TemplateThirdPartyRepo) DeleteTemplate(ctx context.Context, code string) error {
	return nil
}

func (tpRepo *TemplateThirdPartyRepo) ListTemplates(ctx context.Context, offset, limit int) ([]string, error) {
	return nil, nil
}

func (tpRepo *TemplateThirdPartyRepo) CreateTemplate(ctx context.Context, template string) error {
	return nil
}
