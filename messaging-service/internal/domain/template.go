package domain

import (
	"errors"
	"regexp"
	"strings"
	"time"

	"ces-services/messaging-service/gen/gen/model"
)

var ValidMessageTypes = map[string]bool{
	"sms":          true,
	"email":        true,
	"notification": true,
}

var RegVerfiier = regexp.MustCompile(`^[a-zA-Z][a-zA-Z0-9_]*$`)

const spliter = ","

type Template struct {
	ID            string    // The unique identifier for the template
	Subject       string    // The subject line of the template
	Description   string    // A brief description of the template
	Body          string    // The body of the template, which can include placeholders for dynamic content
	Variables     []string  // A list of variables that can be replaced in the body of the template
	BussinessTags []string  // Tags that categorize the template for business purposes
	MessageTypes  []string  // The type of message the template is for (e.g., email, SMS, etc.)
	CreatedAt     time.Time // The timestamp when the template was created
	UpdatedAt     time.Time // The timestamp when the template was last updated
}

type TemplateRepo interface {
	GetTemplate(code string) (*Template, error)           // Retrieves a template by its unique code
	UpdateTemplate(code string, template *Template) error // Updates an existing template
	DeleteTemplate(code string) error                     // Deletes a template by its unique code
	ListTemplates(offset, limit int) ([]*Template, error) // Lists templates with pagination
	CreateTemplate(template *Template) error              // Creates a new template
}

func VerifyTemplateMessageType(msgType string) bool {
	return ValidMessageTypes[msgType]
}

func VerifyVariableNameOrTagName(name string) bool {
	return RegVerfiier.MatchString(name)
}

func ParseString2MessageTypes(s string) ([]string, error) {
	ret := strings.Split(s, spliter)
	for _, str := range ret {
		if !VerifyTemplateMessageType(str) {
			return nil, errors.New("invalid message type: " + str)
		}
	}
	return ret, nil
}

func ParseString2Variables(s string) ([]string, error) {
	ret := strings.Split(s, spliter)
	for _, str := range ret {
		if !VerifyVariableNameOrTagName(str) {
			return nil, errors.New("invalid variable name: " + str)
		}
	}
	return ret, nil
}

func ParseString2BussinessTags(s string) ([]string, error) {
	ret := strings.Split(s, spliter)
	for _, str := range ret {
		if !VerifyVariableNameOrTagName(str) {
			return nil, errors.New("invalid business tag: " + str)
		}
	}
	return ret, nil
}

func TemplateModel2Domain(tmplDB *model.MessageTemplate) (*Template, error) {
	ret := &Template{
		ID:          tmplDB.ID,
		Subject:     tmplDB.TemplateSubject,
		Description: tmplDB.TempateDescription,
		Body:        tmplDB.Body,
		CreatedAt:   tmplDB.CreatedAt,
		UpdatedAt:   tmplDB.UpdatedAt,
	}
	var err error
	ret.MessageTypes, err = ParseString2MessageTypes(tmplDB.MessageType)
	if err != nil {
		return nil, err
	}
	ret.Variables, err = ParseString2Variables(tmplDB.Variables)
	if err != nil {
		return nil, err
	}
	ret.BussinessTags, err = ParseString2BussinessTags(tmplDB.BussinessTag)
	if err != nil {
		return nil, err
	}
	return ret, nil
}

func TemplateDomain2Model(tmpl *Template) (*model.MessageTemplate, error) {
	ret := &model.MessageTemplate{
		ID:                 tmpl.ID,
		TemplateSubject:    tmpl.Subject,
		TempateDescription: tmpl.Description,
		Body:               tmpl.Body,
		CreatedAt:          tmpl.CreatedAt,
		UpdatedAt:          tmpl.UpdatedAt,
	}

	messageTypeBuilder := strings.Builder{}
	bussinessTagBuilder := strings.Builder{}
	variablesBuilder := strings.Builder{}
	for i, messageType := range tmpl.MessageTypes {
		if !VerifyTemplateMessageType(messageType) {
			return nil, errors.New("invalid message type: " + messageType)
		}
		messageTypeBuilder.WriteString(messageType)
		if i != len(tmpl.MessageTypes)-1 {
			messageTypeBuilder.WriteString(spliter)
		}
	}
	for i, bussinessTag := range tmpl.BussinessTags {
		bussinessTagBuilder.WriteString(bussinessTag)
		if i != len(tmpl.BussinessTags)-1 {
			bussinessTagBuilder.WriteString(spliter)
		}
	}
	for i, variable := range tmpl.Variables {
		variablesBuilder.WriteString(variable)
		if i != len(tmpl.Variables)-1 {
			variablesBuilder.WriteString(spliter)
		}
	}

	ret.MessageType = messageTypeBuilder.String()
	ret.BussinessTag = bussinessTagBuilder.String()
	ret.Variables = variablesBuilder.String()

	return ret, nil
}
