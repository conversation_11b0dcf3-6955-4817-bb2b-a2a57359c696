package domain

import (
	"encoding/json"
	"time"

	"ces-services/messaging-service/gen/gen/model"

	"github.com/google/uuid"
)

const (
	MessageStatusCreated = "created"
	MessageStatusSuccess = "success"
	MessageStatusFailed  = "failed"
)

type Message struct {
	MessageID   string
	TemplateID  string
	Variables   map[string]string
	MessageType string
	TaskID      string
	Status      string
	UpdatedAt   time.Time
	CreatedAt   time.Time
	Receivier   string
}

func NewMessage(tmplID, msgType, receiver string, variables map[string]string) *Message {
	return &Message{
		MessageID:   uuid.NewString(),
		TemplateID:  tmplID,
		Variables:   variables,
		MessageType: msgType,
		Receivier:   receiver,
		Status:      MessageStatusCreated,
	}
}

func (msg *Message) VerifyMessageTypes(tmpl *Template) bool {
	for _, tmplType := range tmpl.MessageTypes {
		if tmplType == msg.MessageType {
			return true
		}
	}
	return false
}

func (msg *Message) VerifyVariables(tmpl *Template) bool {
	for _, variable := range tmpl.Variables {
		if _, ok := msg.Variables[variable]; !ok {
			return false
		}
	}
	return true
}

func MessageDomain2Model(msg *Message) (*model.Message, error) {

	variableJsonStr, err := json.Marshal(msg.Variables)
	if err != nil {
		return nil, err
	}

	return &model.Message{
		ID:            msg.MessageID,
		TemplateID:    msg.TemplateID,
		Variables:     string(variableJsonStr),
		MessageType:   msg.MessageType,
		TaskID:        msg.TaskID,
		MessageStatus: msg.Status,
		UpdatedAt:     msg.UpdatedAt,
		CreatedAt:     msg.CreatedAt,
		Receiver:      msg.Receivier,
	}, nil
}

func MessageModel2Domain(msg *model.Message) (*Message, error) {
	variableMap := make(map[string]string)
	err := json.Unmarshal([]byte(msg.Variables), &variableMap)
	if err != nil {
		return nil, err
	}
	return &Message{
		MessageID:   msg.ID,
		TemplateID:  msg.TemplateID,
		Variables:   variableMap,
		MessageType: msg.MessageType,
		TaskID:      msg.TaskID,
		Status:      msg.MessageStatus,
		UpdatedAt:   msg.UpdatedAt,
		CreatedAt:   msg.CreatedAt,
		Receivier:   msg.Receiver,
	}, nil
}
