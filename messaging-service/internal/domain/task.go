package domain

import (
	"ces-services/messaging-service/gen/gen/model"

	"github.com/google/uuid"
)

const (
	TaskStatusCreated    = "created"
	TaskStatusProcessing = "processing"
	TaskStatusSuccess    = "success"
	TaskStatusFailed     = "failed"
	TaskStatusTimeout    = "timeout"
	TaskStatusCancelled  = "cancelled"
	TaskStatusUnknown    = "unknown"
)

type Task struct {
	TaskID     string
	TaskStatus string
}

func NewTask() *Task {
	return &Task{
		TaskID:     uuid.NewString(),
		TaskStatus: TaskStatusCreated,
	}
}

func TaskDomain2Model(taskModel *Task) *model.Task {
	return &model.Task{
		ID: taskModel.TaskID,
	}
}

func TaskModel2Domain(task *model.Task) *Task {
	return &Task{
		TaskID: task.ID,
	}
}
