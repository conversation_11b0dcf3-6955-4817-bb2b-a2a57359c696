package task

import (
	"context"

	messagingpb "ces-services/messaging-service/gen/proto/ces/messaging/messaging"
	"ces-services/messaging-service/internal/infra/message"
	"ces-services/messaging-service/internal/infra/queue"
	"ces-services/messaging-service/internal/infra/task"

	"go.uber.org/zap"
)

var _ messagingpb.TaskServiceServer = (*TaskService)(nil)

type TaskService struct {
	taskRepo        *task.TaskRepo
	messageRepo     *message.MessageRepo
	messageProducer *queue.MessageProducer
}

func CreateTaskService(
	taskRepo *task.TaskRepo,
	messageRepo *message.MessageRepo,
	messageProducer *queue.MessageProducer,
) *TaskService {
	return &TaskService{
		taskRepo:        taskRepo,
		messageRepo:     messageRepo,
		messageProducer: messageProducer,
	}
}

func (s *TaskService) CheckTaskState(ctx context.Context, req *messagingpb.CheckTaskStateRequest) (*messagingpb.CheckTaskStateResponse, error) {
	ret := &messagingpb.CheckTaskStateResponse{
		Code:    0,
		Message: "ok",
		TaskID:  req.TaskID,
	}
	// Step1: Parse the req to the Task ID
	taskID := req.TaskID
	// Step2: Query the task state from the database
	taskModel, err := s.taskRepo.GetTask(taskID)
	if err != nil {
		ret.Code = 1
		ret.Message = "failed to get task state: " + err.Error()
		return ret, nil
	}

	// Step3: Return the task state to the client

	ret.State = taskModel.TaskStatus

	return ret, nil
}

func (s *TaskService) RetryTask(ctx context.Context, req *messagingpb.RetryTaskRequest) (*messagingpb.RetryTaskResponse, error) {
	ret := &messagingpb.RetryTaskResponse{
		Code:    0,
		Message: "ok",
	}

	msgs, err := s.messageRepo.GetMessagesWithTaskID(req.TaskID)

	if err != nil {
		ret.Code = 1
		ret.Message = err.Error()
		return ret, err
	}

	for _, v := range msgs {
		err := s.messageProducer.ProduceMessage(v)
		if err != nil {
			zap.L().Error("Produce message to message queue", zap.String("id", v.MessageID), zap.String("err", err.Error()))
		}
	}
	return ret, nil
}
