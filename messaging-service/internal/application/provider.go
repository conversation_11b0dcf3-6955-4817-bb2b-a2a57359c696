package application

import (
	"ces-services/messaging-service/internal/application/messaging"
	"ces-services/messaging-service/internal/application/task"
	"ces-services/messaging-service/internal/application/template"

	"github.com/google/wire"
)

// AppProviderSet Application providers
var AppProviderSet = wire.NewSet(
	messaging.CreateMessagingService,
	template.CreateTemplateService,
	task.CreateTaskService,
)
