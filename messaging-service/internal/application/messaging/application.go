package messaging

import (
	"context"
	"fmt"

	messagingpb "ces-services/messaging-service/gen/proto/ces/messaging/messaging"
	"ces-services/messaging-service/internal/domain"
	"ces-services/messaging-service/internal/infra/message"
	"ces-services/messaging-service/internal/infra/queue"
	"ces-services/messaging-service/internal/infra/task"

	"go.uber.org/zap"
)

var _ messagingpb.MessagingServiceServer = (*MessagingService)(nil)

type MessagingService struct {
	templateRepo    domain.TemplateRepo
	messageRepo     *message.MessageRepo
	taskRepo        *task.TaskRepo
	messageProducer *queue.MessageProducer
}

func CreateMessagingService(
	tmplRepo domain.TemplateRepo,
	messagingRepo *message.MessageRepo,
	taskRepo *task.TaskRepo,
	messageProducer *queue.MessageProducer,
) *MessagingService {
	return &MessagingService{
		templateRepo:    tmplRepo,
		messageRepo:     messagingRepo,
		taskRepo:        taskRepo,
		messageProducer: messageProducer,
	}
}

// 只发单条
func (s *MessagingService) Send(ctx context.Context, req *messagingpb.SendRequest) (*messagingpb.SendResponse, error) {

	task := domain.NewTask()

	ret := &messagingpb.SendResponse{
		Code:    0,
		Message: "ok",
		TaskID:  task.TaskID,
	}

	// Step1: Parse the req to the Message Model
	msg := domain.NewMessage(
		req.TemplateID,
		req.MessageType,
		req.Receiver,
		req.TemplateVariables,
	)

	tmpl, err := s.templateRepo.GetTemplate(req.TemplateID)

	if err != nil {
		zap.L().Error("Get Template Error.", zap.String("id", req.TemplateID), zap.String("err", err.Error()))
		ret.Code = 1
		ret.Message = "Get Template Error: " + err.Error()
		ret.TaskID = ""
		return nil, err
	}

	valid := msg.VerifyMessageTypes(tmpl)
	if !valid {
		errMsg := fmt.Errorf("invalid message type: %s for template %s", msg.MessageType, msg.TemplateID)
		ret.Code = 1
		ret.Message = errMsg.Error()
		ret.TaskID = ""
		return nil, errMsg
	}

	valid = msg.VerifyVariables(tmpl)
	if !valid {
		errMsg := fmt.Errorf("invalid variables: %v for template %s", msg.Variables, msg.TemplateID)
		ret.Code = 1
		ret.Message = errMsg.Error()
		ret.TaskID = ""
		return nil, errMsg
	}

	// Print msg for debug
	fmt.Printf("msg: %+v\n", msg)
	msg.TaskID = task.TaskID
	err = s.messageProducer.ProduceMessage(msg)

	if err != nil {
		zap.L().Error("Produce message to message queue", zap.String("id", msg.MessageID), zap.String("err", err.Error()))
		ret.Code = 1
		ret.Message = "produce message " + msg.MessageID + " to queue error: " + err.Error()
		return ret, err
	}

	err = s.taskRepo.CreateTask(task)

	if err != nil {
		zap.L().Error("Create Task in Database Error:", zap.String("id", task.TaskID), zap.String("err", err.Error()))
		ret.Code = 1
		ret.Message = "Create Task in Database Error: " + err.Error()
		ret.TaskID = ""
	}

	err = s.messageRepo.CreateMessage(msg)

	if err != nil {
		zap.L().Error("Create Message in Database Error:", zap.String("id", msg.MessageID), zap.String("err", err.Error()))
		task.TaskStatus = domain.TaskStatusUnknown
		ret.Code = 1
		ret.Message = "Create Message in Database Error: " + err.Error()
	}

	return ret, err
}

// 多条发送
func (s *MessagingService) BatchSend(ctx context.Context, req *messagingpb.BatchSendRequest) (*messagingpb.SendResponse, error) {

	task := domain.NewTask()

	ret := &messagingpb.SendResponse{
		Code:    0,
		Message: "ok",
		TaskID:  task.TaskID,
	}

	err := s.taskRepo.CreateTask(task)

	if err != nil {
		zap.L().Error("Create Task in Database Error:", zap.String("id", task.TaskID), zap.String("err", err.Error()))
		ret.Code = 1
		ret.Message = "Create Task in Database Error: " + err.Error()
		ret.TaskID = ""
		return nil, err
	}

	for _, recipient := range req.Recipients {
		// Step1: Parse the req to the Message Model
		subVariables := recipient.PersonalizedVariables
		for tmplKey, variable := range req.TemplateVariables {
			if _, ok := subVariables[tmplKey]; !ok {
				subVariables[tmplKey] = variable
			}
		}
		msg := domain.NewMessage(
			req.TemplateID,
			req.MessageType,
			recipient.Receiver,
			subVariables,
		)

		tmpl, err := s.templateRepo.GetTemplate(req.TemplateID)

		if err != nil {
			zap.L().Error("Get Template Error.", zap.String("id", req.TemplateID), zap.String("err", err.Error()))
			ret.Code = 1
			ret.Message = "Get Template Error: " + err.Error()
			ret.TaskID = ""
			return nil, err
		}

		valid := msg.VerifyMessageTypes(tmpl)
		if !valid {
			errMsg := fmt.Errorf("invalid message type: %s for template %s", msg.MessageType, msg.TemplateID)
			ret.Code = 1
			ret.Message = errMsg.Error()
			ret.TaskID = ""
			return nil, errMsg
		}

		valid = msg.VerifyVariables(tmpl)
		if !valid {
			errMsg := fmt.Errorf("invalid variables: %v for template %s", msg.Variables, msg.TemplateID)
			ret.Code = 1
			ret.Message = errMsg.Error()
			ret.TaskID = ""
			return nil, errMsg
		}

		msg.TaskID = task.TaskID

		err = s.messageProducer.ProduceMessage(msg)

		if err != nil {
			zap.L().Error("Produce message to message queue", zap.String("id", msg.MessageID), zap.String("err", err.Error()))
			continue
		}

		err = s.messageRepo.CreateMessage(msg)

		if err != nil {
			zap.L().Error("Create Message in Database Error:", zap.String("id", msg.MessageID), zap.String("err", err.Error()))
			task.TaskStatus = domain.TaskStatusUnknown
			ret.Code = 1
			ret.Message = "Create Message in Database Error: " + err.Error()
		}

	}

	return ret, nil
}
