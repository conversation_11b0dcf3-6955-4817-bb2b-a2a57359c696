package template

import (
	"context"

	messagingpb "ces-services/messaging-service/gen/proto/ces/messaging/messaging"
	"ces-services/messaging-service/internal/domain"
)

var _ messagingpb.TemplateServiceServer = (*TemplateService)(nil)

type TemplateService struct {
	tmplRepo domain.TemplateRepo
}

func CreateTemplateService(tmplRepo domain.TemplateRepo) *TemplateService {
	return &TemplateService{
		tmplRepo: tmplRepo,
	}
}

func (s *TemplateService) CreateTemplate(ctx context.Context, req *messagingpb.CreateTemplateRequest) (*messagingpb.CreateTemplateResponse, error) {

	// Step1: Parse the req to the Template Model

	template := &domain.Template{
		ID:            req.TemplateID,
		Description:   req.Description,
		Subject:       req.TemplateSubject,
		Body:          req.TemplateBody,
		Variables:     req.VariableKeys,
		MessageTypes:  req.MessageType,
		BussinessTags: req.BussinessTag,
	}

	// Step1.5(Optional): Validate the Template Model

	// Step3: Create the Template in the database

	s.tmplRepo.CreateTemplate(template)

	// Step4: Return the Template ID to the client

	return &messagingpb.CreateTemplateResponse{
		Code:       0,
		Message:    "ok",
		TemplateID: template.ID,
	}, nil
}
func (s *TemplateService) UpdateTemplate(ctx context.Context, req *messagingpb.UpdateTemplateRequest) (*messagingpb.UpdateTemplateResponse, error) {
	ret := &messagingpb.UpdateTemplateResponse{
		Code:    0,
		Message: "ok",
	}
	// Step1: Parse the req to the Template Model
	// Step1.5(Optional): Validate the Template Model
	template := &domain.Template{
		ID:            req.TemplateID,
		Description:   req.Description,
		Subject:       req.TemplateSubject,
		Body:          req.TemplateBody,
		Variables:     req.VariableKeys,
		MessageTypes:  req.MessageType,
		BussinessTags: req.BussinessTag,
	}
	// Step3: Update the Template in the database
	err := s.tmplRepo.UpdateTemplate(req.TemplateID, template)
	if err != nil {
		return nil, err
	}
	// Step4: Return the Template ID to the client

	return ret, nil
}
func (s *TemplateService) DeleteTemplate(ctx context.Context, req *messagingpb.DeleteTemplateRequest) (*messagingpb.DeleteTemplateResponse, error) {
	ret := &messagingpb.DeleteTemplateResponse{
		Code:    0,
		Message: "ok",
	}
	// Step1: Parse the req to the Template ID
	// Step3: Delete the Template in the database
	err := s.tmplRepo.DeleteTemplate(req.TemplateID)
	if err != nil {
		ret.Code = 1
		ret.Message = "failed to delete template: " + err.Error()
		return ret, err
	}
	// Step4: Return the Template ID to the client

	return ret, nil
}
func (s *TemplateService) GetTemplate(ctx context.Context, req *messagingpb.GetTemplateRequest) (*messagingpb.GetTemplateResponse, error) {
	ret := &messagingpb.GetTemplateResponse{
		Code:    0,
		Message: "ok",
	}

	// Step1: Parse the req to the Template ID
	template, err := s.tmplRepo.GetTemplate(req.TemplateID)
	if err != nil {
		ret.Code = 1
		ret.Message = "failed to get template: " + err.Error()
		return ret, err
	}
	// Step3: Get the Template from the database
	// Step4: Return the Template to the client

	ret.TemplateID = template.ID
	ret.Description = template.Description
	ret.TemplateSubject = template.Subject
	ret.TemplateBody = template.Body
	ret.VariableKeys = template.Variables
	ret.MessageType = template.MessageTypes
	ret.BussinessTag = template.BussinessTags
	ret.CreateTime = template.CreatedAt.String()
	ret.UpdateTime = template.UpdatedAt.String()

	return ret, nil
}
func (s *TemplateService) ListTemplates(ctx context.Context, req *messagingpb.ListTemplatesRequest) (*messagingpb.ListTemplatesResponse, error) {
	ret := &messagingpb.ListTemplatesResponse{
		Code:    0,
		Message: "ok",
	}

	// Step1: Parse the req to the Template ID
	// Step3: List the Template from the database
	// Step4: Return the Template to the client

	templates, err := s.tmplRepo.ListTemplates(int(req.Offset), int(req.Limit))
	if err != nil {
		ret.Code = 1
		ret.Message = "failed to list templates: " + err.Error()
		return ret, err
	}

	for _, template := range templates {
		ret.TemplateAbstracts = append(ret.TemplateAbstracts, &messagingpb.TemplateAbstract{
			TemplateID:  template.ID,
			Description: template.Description,
		})
	}
	ret.Total = uint32(len(templates))

	return ret, nil
}
